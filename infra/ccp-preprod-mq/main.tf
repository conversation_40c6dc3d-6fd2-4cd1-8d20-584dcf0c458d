# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.47
# template-version: 7.18.47
# module-version: v10.16.2
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
path = replace("secret/data/mccprod/infra/286806641839/atlantis-c2p-pre-prod", "/data/", "/")
}
data "vault_generic_secret" "aws_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

provider "aws" {
access_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
secret_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
region            = "us-east-2"
use_fips_endpoint = false
}

provider "aws" {
    alias             = "dns"
    access_key        = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
    secret_key        = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
    region            = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
    use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/ccp-preprod-mq.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/ccp-preprod-mq.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/aws-rabbitmq?ref=v10.16.2"
  providers = {
    aws = aws
  }
  aws_mq_admin_credential_path = "secret/data/mccprod/infra/ccp-rabbitmq"
  aws_mq_broker_name = "ccp-primary-preprod-rabbitmq-prt"
  aws_mq_configuration_name = "ccp-preprod-rabbitmq-prt-configuration"
  aws_mq_publicly_accessible = false
  aws_mq_security_groups = {
    rabbitmq = {
      rules = [
        {
          cidrs = [
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "10.0.0.0/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16"
          ]
          from = 5671
          ipv6_cidrs = [
          ]
          name = "amqp"
          protocol = "tcp"
          to = 5671
          type = "ingress"
        },
        {
          cidrs = [
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "10.0.0.0/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16",
            "********/16"
          ]
          from = 443
          ipv6_cidrs = [
          ]
          name = "admin"
          protocol = "tcp"
          to = 443
          type = "ingress"
        }
      ]
    }
  }
  ccp_account_id = "32bc6b5d-84a0-4df6-9838-fb9a16d4cffb"
  ccp_organization_id = "9edd5e35-1f5a-4fbc-978d-8f250f3501b0"
  subnet_type = "cloud"
  vpc_name = "c1p_pst1"
  
}
output "primary_rabbitmq_console_url" {
  value = module.infra.primary_rabbitmq_console_url
}
output "primary_rabbitmq_endpoint" {
  value = module.infra.primary_rabbitmq_endpoint
}

