# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.47
# template-version: v7.19.8-next
# module-version: ccp-cmdb-v0.0.7
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
path = replace("secret/data/mccprod/infra/286806641839/atlantis-c2p-pre-prod", "/data/", "/")
}

provider "aws" {
access_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
secret_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
region            = "us-east-2"
use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/ccp-preprod-eventrule.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/ccp-preprod-eventrule.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/event_rule?ref=ccp-cmdb-v0.0.7"
  providers = {
    aws = aws
  }
  ccp_cmdb_role_credential_path = "secret/data/mccprod/infra/ccp-cmdb/role"
  ccp_cmdb_url = "https://api.ciscoplatform.cloud/api/cmdb/v1/"
  ccp_vault_address = "https://bootstrap.keeper.cisco.com/"
  ccp_vault_name_space = "CiscoCloudPlatform/pre-prod"
  
}

