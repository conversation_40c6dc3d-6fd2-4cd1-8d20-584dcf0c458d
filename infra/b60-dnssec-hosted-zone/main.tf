# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.48
# module-version: dnssec-nlb
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-int/aws", "/data/", "/")
}

data "vault_generic_secret" "cluster_zone_creds" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

provider "aws" {
  access_key = data.vault_generic_secret.cluster_zone_creds.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.cluster_zone_creds.data["AWS_SECRET_ACCESS_KEY"]
  region     = data.vault_generic_secret.cluster_zone_creds.data["AWS_DEFAULT_REGION"]

  use_fips_endpoint = false

  default_tags {
    tags = {
      ApplicationName = "wbx3_platform"
      CiscoMailAlias  = "<EMAIL>"
      ResourceOwner   = "BU_Collaboration"
    }
  }
}

provider "aws" {
  alias = "kms-region"
  access_key = data.vault_generic_secret.cluster_zone_creds.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.cluster_zone_creds.data["AWS_SECRET_ACCESS_KEY"]
  region = "us-east-1" // KMS Keys for DNSSEC have to be in us-east-1

  default_tags {
    tags = {
      ApplicationName = "wbx3_platform"
      CiscoMailAlias  = "<EMAIL>"
      ResourceOwner   = "BU_Collaboration"
    }
  }
}

data "vault_generic_secret" "parent_role_arn" {
  path = replace("secret/data/mccprod/infra/route/webexgov-route53-updater-role", "/data/", "/")
}


provider "aws" {
  alias = "domain-parent-account"
  
  region     = data.vault_generic_secret.aws_infra_credential.data["AWS_DEFAULT_REGION"]
  access_key = data.vault_generic_secret.cluster_zone_creds.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.cluster_zone_creds.data["AWS_SECRET_ACCESS_KEY"]
  
  assume_role {
    role_arn = data.vault_generic_secret.parent_role_arn.data["ROLE_ARN"]
  }

  default_tags {
    tags = {
      ApplicationName = "wbx3_platform"
      CiscoMailAlias  = "<EMAIL>"
      ResourceOwner   = "BU_Collaboration"
    }
  }
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/b60-dnssec-hosted-zone.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/b60-dnssec-hosted-zone.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/route53_hosted_zone?ref=d5f934f321714fde56062375e92e55f47f95e72b"
  providers = {
    aws = aws
    aws.domain-parent-account = aws.domain-parent-account
    aws.kms-region = aws.kms-region
  }
  cloud = "mccprod"
  cluster_zone_provider_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  domain = "ite.webexgov.us"
  name = "b60-dnssec-hosted-zone"
  parent_domain_role_credentials_path = "secret/data/mccprod/infra/route/webexgov-route53-updater-role"
  zone_name = "b60"
  
}

