# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: 7.18.47
# module-version: v10.16.19
# wbx3-infra-version: <nil>
# resource: ccg-prod-eu-policy
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    policy_description: Allow calling-compliance-gateway-user to access calling-compliance-gateway-prod-eu S3 bucket.
    policy_json: |
        {
          "Version": "2012-10-17",
          "Statement": [
            {
              "Action": "s3:ListBucket",
              "Resource": "arn:aws:s3:::calling-compliance-gateway-prod-eu",
              "Effect": "Allow"
            },
            {
              "Action": "*",
              "Resource": "arn:aws:s3:::calling-compliance-gateway-prod-eu/*",
              "Effect": "Allow"
            }
          ]
        }
env_name: a-usea1-i0-0
module_path: modules/aws/aws_iam_policy
module_version: v10.16.19
name: ccg-prod-eu-policy
terraform_version: v1.5.7

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cloud_service_provider: aws
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: a00.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.33
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/ccg-prod-eu-policy
    name: ccg-prod-eu-policy
    workflow: standard
dir: infra
external: false
include_defaults: true
manifest_path: manifests/iam/calling-compliance-gateway/manifest.yaml
s3_bucket_path: terraform-state/infra/ccg-prod-eu-policy.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.47
    templateversion: 7.18.47
workflow: standard