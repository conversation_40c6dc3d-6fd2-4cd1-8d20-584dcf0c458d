# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: 7.18.47
# module-version: v10.16.19
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/ccg-prod-eu-policy.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/ccg-prod-eu-policy.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/aws_iam_policy?ref=v10.16.19"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  name = "ccg-prod-eu-policy"
  policy_description = "Allow calling-compliance-gateway-user to access calling-compliance-gateway-prod-eu S3 bucket."
  policy_json = <<-EOF
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Action": "s3:ListBucket",
        "Resource": "arn:aws:s3:::calling-compliance-gateway-prod-eu",
        "Effect": "Allow"
      },
      {
        "Action": "*",
        "Resource": "arn:aws:s3:::calling-compliance-gateway-prod-eu/*",
        "Effect": "Allow"
      }
    ]
  }
  
  EOF
  
}
output "iam_policy_arn" {
  value = module.infra.iam_policy_arn
}
output "iam_policy_id" {
  value = module.infra.iam_policy_id
}

