# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.8
# module-version: v10.11.13
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/wxt/sg/wxt-d70-app1-sg.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/wxt/sg/wxt-d70-app1-sg.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/security_groups?ref=v10.11.13"
  command_and_control = "mccprod"
  env_name = "a-usea2-p0-0"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  security_groups = {
    wxt-a02-app2-sg = {
      create_new = true
      description = "SG attached on WxT workers to access persistence"
      egress_rules = [
        {
          cidr_blocks = [
            "0.0.0.0/0"
          ]
          from_port = 0
          ipv6_cidr_blocks = [
          ]
          prefix_list_ids = [
          ]
          protocol = "TCP"
          security_groups = [
          ]
          self = false
          to_port = 65535
        }
      ]
      existing_sg_id = ""
      ingress_rules = [
        {
          cidr_blocks = [
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "**********/16",
            "***********/25",
            "**********/16",
            "********/16",
            "*************/28",
            "***********/24",
            "********/16",
            "************/20",
            "************/20",
            "************/20",
            "**********/20",
            "************/22",
            "************/20",
            "**********/16",
            "*********/16"
          ]
          from_port = 0
          ipv6_cidr_blocks = [
          ]
          prefix_list_ids = [
          ]
          protocol = "TCP"
          security_groups = [
          ]
          self = false
          to_port = 65535
        }
      ]
      tags = {
        name = "wxt-d70-app1-sg"
        security_group_type = "kubed"
      }
    }
  }
  source_aws_infra_region = "us-east-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "d70_app1"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

