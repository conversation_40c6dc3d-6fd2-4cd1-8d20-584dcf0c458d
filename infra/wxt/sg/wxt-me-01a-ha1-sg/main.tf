# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.19
# template-version: 7.18.19
# module-version: v10.7.10
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/wxt/sg/wxt-me-01a-ha1-sg.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/wxt/sg/wxt-me-01a-ha1-sg.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/security_groups?ref=v10.7.10"
  command_and_control = "mccprod"
  env_name = "me-01a-ha1"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  security_groups = {
    wxt-sg-01a-ha1-cloudhsm = {
      create_new = true
      description = "SG attached on WxT workers to access cloudhsm cluster"
      egress_rules = [
        {
          cidr_blocks = [
            "**********/16"
          ]
          from_port = 2223
          ipv6_cidr_blocks = [
          ]
          prefix_list_ids = [
          ]
          protocol = "TCP"
          security_groups = [
          ]
          self = false
          to_port = 2225
        }
      ]
      existing_sg_id = ""
      ingress_rules = [
      ]
      tags = {
        name = "wxt-sg-01a-ha1-cloudhsm"
        security_group_type = "kubed"
      }
    }
    wxt-sg-01a-ha1-pst = {
      create_new = true
      description = "SG attached on WxT workers to access persistence in me-01a-pst"
      egress_rules = [
        {
          cidr_blocks = [
            "**********/16",
            "**********/16"
          ]
          from_port = 0
          ipv6_cidr_blocks = [
          ]
          prefix_list_ids = [
          ]
          protocol = "TCP"
          security_groups = [
          ]
          self = false
          to_port = 65535
        }
      ]
      existing_sg_id = ""
      ingress_rules = [
        {
          cidr_blocks = [
            "**********/16",
            "**********/16"
          ]
          from_port = 0
          ipv6_cidr_blocks = [
          ]
          prefix_list_ids = [
          ]
          protocol = "TCP"
          security_groups = [
          ]
          self = false
          to_port = 65535
        }
      ]
      tags = {
        name = "wxt-me-01a-ha1-pst"
        security_group_type = "kubed"
      }
    }
  }
  source_aws_infra_region = "me-central-1"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "p6_mtg1"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}
