# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: v7.19.8-next
# module-version: v10.11.13
# wbx3-infra-version: <nil>
# resource: wxt-d81-app2-sg
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    security_groups:
        wxt-a02-app2-sg:
            create_new: true
            description: SG attached on WxT workers to access persistence
            egress_rules:
                - cidr_blocks:
                    - 0.0.0.0/0
                  from_port: 0
                  ipv6_cidr_blocks: []
                  prefix_list_ids: []
                  protocol: TCP
                  security_groups: []
                  self: false
                  to_port: 65535
            existing_sg_id: ""
            ingress_rules:
                - cidr_blocks:
                    - ********/16
                    - ********/16
                    - *************/28
                    - ************/21
                    - **********/24
                    - ***********/25
                    - ***********/20
                    - ***********/20
                    - ***********/20
                    - **********/16
                    - **********/16
                    - **********/16
                    - **********/16
                    - *************/23
                    - ***********/18
                    - **********/16
                  from_port: 0
                  ipv6_cidr_blocks: []
                  prefix_list_ids: []
                  protocol: TCP
                  security_groups: []
                  self: false
                  to_port: 65535
            tags:
                name: wxt-d81-app2-sg
                security_group_type: kubed
    source_aws_infra_region: ap-northeast-1
    source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    source_vpc_name: d81_app2
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
env_name: a-apne1-p0-0
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.19.0
module_path: modules/aws/wxt/security_groups
module_version: v10.11.13
name: wxt-d81-app2-sg
s3_bucket_path: terraform-state/infra/wxt/sg/wxt-d81-app2-sg.tfstate
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.0
use_provider_template: true

#~~ No Blueprint Values


#~~ Environment Values
consul_host: consul.int.mcc01.prod.infra.webex.com

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/wxt/sg/wxt-d81-app2-sg
    name: wxt-d81-app2-sg
    workflow: standard
dir: infra/wxt/sg
external: false
include_defaults: false
include_environment: false
manifest_path: manifest.yaml
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: v7.19.8-next
    templateversion: v7.19.8-next
workflow: standard