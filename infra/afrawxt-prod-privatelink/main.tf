# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: 7.17.2
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/afrawxt-prod-privatelink.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/privatelink_aws_kafka?ref=v8.1.1"
  aws_infra_region = "eu-central-1"
  bootstrap = "lkc-7nq07w-6x1v7g.eu-central-1.aws.glb.confluent.cloud:9092"
  confluent_azs = [
    "euc1-az1",
    "euc1-az2",
    "euc1-az3"
  ]
  name = "afrawxt-prod-privatelink"
  privatelink_service_name = "com.amazonaws.vpce.eu-central-1.vpce-svc-0237de3c157bc43b8"
  subnet_tags = {
    Tier = "public"
  }
  vpc_name = "afrawxt-prod"
  
}

output "subnets_to_privatelink" {
  value = module.infra.subnets_to_privatelink
}
