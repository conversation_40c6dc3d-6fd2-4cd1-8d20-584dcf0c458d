# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: v7.18.0-EXPERIMENTAL-fc69d52
# module-version: v9.4.13
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/anrtwxt-prod-v2-vgw.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/dx_vgw?ref=v9.4.13"
  aws_infra_region = "ap-northeast-1"
  dx_gateway_account = ************
  dx_gateway_id = "7fb9c799-29ca-45cc-9972-291b8b9f4ff2"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  routes = [
    {
      subnet = "10.0.0.0/8"
      subnet_tier = "provider"
    }
  ]
  vpc_name = "anrtwxt-prod-v2"
  
}

