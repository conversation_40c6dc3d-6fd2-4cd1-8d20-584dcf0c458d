# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: 7.17.2
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/anrtwxt-prod-privatelink.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/privatelink_aws_kafka?ref=v8.1.1"
  aws_infra_region = "ap-northeast-1"
  bootstrap = "lkc-w51gxm-6no4xg.ap-northeast-1.aws.glb.confluent.cloud:9092"
  confluent_azs = [
    "apne1-az1",
    "apne1-az2",
    "apne1-az4"
  ]
  name = "anrtwxt-prod-privatelink"
  privatelink_service_name = "com.amazonaws.vpce.ap-northeast-1.vpce-svc-0110a0aba8b0cb5cd"
  subnet_tags = {
    Tier = "public"
  }
  vpc_name = "anrtwxt-prod"
  
}

output "subnets_to_privatelink" {
  value = module.infra.subnets_to_privatelink
}
