# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.21
# template-version: 7.18.22
# module-version: v10.10.2
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = "me-central-1"
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/cloudhsm/kms-adxb.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/cloudhsm/kms-adxb.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/cloudhsm?ref=v10.10.2"
  providers = {
    aws = aws
  }
  azs = [
    "me-central-1a",
    "me-central-1b",
    "me-central-1c"
  ]
  hsm_type = "hsm1.medium"
  vault_path = "secret/mccprod/cloudhsm/kms-adxb"
  vpc_id = "vpc-028bc0cf41cbcf455"

}
output "aws_hardware_certificate" {
  value = module.infra.aws_hardware_certificate
  sensitive = true
}
output "cluster_certificate" {
  value = module.infra.cluster_certificate
  sensitive = true
}
output "cluster_csr" {
  value = module.infra.cluster_csr
  sensitive = true
}
output "hsm_certificate" {
  value = module.infra.hsm_certificate
  sensitive = true
}
output "hsm_ips" {
  value = module.infra.hsm_ips
}
output "manufacturer_hardware_certificate" {
  value = module.infra.manufacturer_hardware_certificate
  sensitive = true
}
