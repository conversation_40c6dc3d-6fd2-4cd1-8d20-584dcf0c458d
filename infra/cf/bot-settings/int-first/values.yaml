# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v9.3.4
# wbx3-infra-version: <nil>
# resource: cf/bot-settings/int-first
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    aliases:
        - bot-settings-int.webex.com
    aws_infra_region: us-east-1
    create_origin_access_identity: false
    origin:
        s3_origin:
            domain_name: webex-bot-settings-int-first.s3.us-east-1.amazonaws.com
            origin_id: webex-bot-settings-int-first
    tags:
        ApplicationGroup: wxt-general
        Name: webex-bot-settings-int-first
        Service: webex-bot-settings
    viewer_certificate:
        acm_certificate_arn: arn:aws:acm:us-east-1:527856644868:certificate/7f434206-cd21-46c1-a74e-4b6fb4005297
env_name: aiadwxt-int
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infractl_version: v7.18.36
module_path: modules/aws/cloudfront
module_version: v9.3.4
name: cf/bot-settings/int-first
terraform_version: v1.5.7

#~~ No Blueprint Values


#~~ Environment Values
admin_port: 6442
aws_infra_az: us-east-1a
aws_infra_region: us-east-1
backend: s3
base_image: wbx3-focal-1.23.5-containerd-265_41f9b97
base_k8s_image: wbx3-focal-1.23.5-containerd-265_41f9b97
bastion_count: 1
bastion_flavor: m5a.large
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
cloud_service_provider: aws
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
create_eip: true
create_vpc: true
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
external_network: aiadwxt-int
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: false
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - **********/16
image_flavor: m5a.2xlarge
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
ingress_count: 3
ingress_flavor: c5n.2xlarge
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
internal_network: multi-az
location: IAD
master_count: 3
master_flavor: m5ad.4xlarge
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
nat_gateway: true
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pipeline_bundles:
    - platform/post-provision-ecr.yaml
    - platform/aws-cloud-controller.yaml
    - platform/base-apps.yaml
    - platform/cluster-autoscaler.yaml
    - platform/kafka-base-apps.yaml
    - platform/falco.yaml
pki_roles:
    - k8s-admin
    - k8s-read-only
provisioning_extra_args: ""
provisioning_module_version: master
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
server_group_policies:
    - anti-affinity
thousand_eyes_count: 0
thousand_eyes_node_count: 0
use_floating_ips: false
use_provider_template: true
vpc_name: aiadwxt-int
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: 8vcpu.32mem.80ssd.0eph

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/cf/bot-settings/int-first
    name: cf/bot-settings/int-first
    workflow: standard
dir: infra
external: false
manifest_path: manifests/general/webex-bot-settings/manifest.yaml
s3_bucket_path: terraform-state/infra/cf/bot-settings/int-first.tfstate
versioningData:
    infractlversion: 7.18.36
    templateversion: 7.18.36
workflow: standard