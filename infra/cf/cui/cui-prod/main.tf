# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.19
# template-version: 7.18.47
# module-version: v10.16.11
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = "us-east-2"
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/cf/cui/cui-prod.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/cf/cui/cui-prod.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cloudfront?ref=v10.16.11"
  providers = {
    aws = aws
  }
  aliases = [
    "accounts.cisco.com"
  ]
  create_origin_access_identity = false
  custom_error_response = [
    {
      error_caching_min_ttl = 10
      error_code = 403
      response_code = 200
      response_page_path = "/index.html"
    }
  ]
  default_root_object = "/index.html"
  origin = {
    s3_origin = {
      custom_origin_config = {
        http_port = 80
        https_port = 443
        origin_protocol_policy = "https-only"
        origin_ssl_protocols = [
          "TLSv1.2"
        ]
      }
      domain_name = "cisco-unified-identity-prod.s3.amazonaws.com"
      origin_id = "cisco-unified-identity-prod"
    }
  }
  origin_group = {
  }
  tags = {
    ApplicationGroup = "cisco-unified-identity"
    Name = "cisco-unified-identity"
    Service = "cisco-unified-identity"
  }
  viewer_certificate = {
    acm_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/d587131b-bf4f-4153-839c-92495d5f60a8"
    minimum_protocol_version = "TLSv1.2_2021"
  }
  
}
output "cloudfront_distribution_arn" {
  value = module.infra.cloudfront_distribution_arn
}
output "cloudfront_distribution_caller_reference" {
  value = module.infra.cloudfront_distribution_caller_reference
}
output "cloudfront_distribution_domain_name" {
  value = module.infra.cloudfront_distribution_domain_name
}
output "cloudfront_distribution_etag" {
  value = module.infra.cloudfront_distribution_etag
}
output "cloudfront_distribution_hosted_zone_id" {
  value = module.infra.cloudfront_distribution_hosted_zone_id
}
output "cloudfront_distribution_id" {
  value = module.infra.cloudfront_distribution_id
}
output "cloudfront_distribution_in_progress_validation_batches" {
  value = module.infra.cloudfront_distribution_in_progress_validation_batches
}
output "cloudfront_distribution_last_modified_time" {
  value = module.infra.cloudfront_distribution_last_modified_time
}
output "cloudfront_distribution_status" {
  value = module.infra.cloudfront_distribution_status
}
output "cloudfront_distribution_tags" {
  value = module.infra.cloudfront_distribution_tags
}
output "cloudfront_distribution_trusted_signers" {
  value = module.infra.cloudfront_distribution_trusted_signers
}
output "cloudfront_monitoring_subscription_id" {
  value = module.infra.cloudfront_monitoring_subscription_id
}
output "cloudfront_origin_access_controls" {
  value = module.infra.cloudfront_origin_access_controls
}
output "cloudfront_origin_access_controls_ids" {
  value = module.infra.cloudfront_origin_access_controls_ids
}
output "cloudfront_origin_access_identities" {
  value = module.infra.cloudfront_origin_access_identities
}
output "cloudfront_origin_access_identity_iam_arns" {
  value = module.infra.cloudfront_origin_access_identity_iam_arns
}
output "cloudfront_origin_access_identity_ids" {
  value = module.infra.cloudfront_origin_access_identity_ids
}

