# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.6
# template-version: 7.18.47
# module-version: v9.3.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/cf/openh264.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/cf/openh264.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cloudfront?ref=v9.3.4"
  aliases = [
    "ciscobinarytest.openh264.org",
    "ciscobinary.openh264.org"
  ]
  aws_infra_region = "us-east-1"
  comment = "OpenH264.org"
  create_origin_access_identity = false
  geo_restriction = {
    locations = [
      "BY",
      "CU",
      "IR",
      "KP",
      "RU",
      "SD",
      "SY",
      "UA"
    ]
    restriction_type = "blacklist"
  }
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  origin = {
    s3_origin = {
      connection_attempts = 3
      connection_timeout = 10
      domain_name = "ciscoopenh264.s3.us-east-1.amazonaws.com"
      origin_id = "ciscoopenh264"
      s3_origin_config = {
        origin_access_identity = "origin-access-identity/cloudfront/EWRBK18B7TENA"
      }
    }
  }
  tags = {
    ApplicationGroup = "openh264"
    Name = "ciscoopenh264"
    Service = "ciscoopenh264"
  }
  viewer_certificate = {
    acm_certificate_arn = "arn:aws:acm:us-east-1:527856644868:certificate/138d01b1-5d8b-4669-b2ef-f7b908a52dd6"
    minimum_protocol_version = "TLSv1.2_2021"
  }
  
}
output "cloudfront_distribution_arn" {
  value = module.infra.cloudfront_distribution_arn
}
output "cloudfront_distribution_caller_reference" {
  value = module.infra.cloudfront_distribution_caller_reference
}
output "cloudfront_distribution_domain_name" {
  value = module.infra.cloudfront_distribution_domain_name
}
output "cloudfront_distribution_etag" {
  value = module.infra.cloudfront_distribution_etag
}
output "cloudfront_distribution_hosted_zone_id" {
  value = module.infra.cloudfront_distribution_hosted_zone_id
}
output "cloudfront_distribution_id" {
  value = module.infra.cloudfront_distribution_id
}
output "cloudfront_distribution_in_progress_validation_batches" {
  value = module.infra.cloudfront_distribution_in_progress_validation_batches
}
output "cloudfront_distribution_last_modified_time" {
  value = module.infra.cloudfront_distribution_last_modified_time
}
output "cloudfront_distribution_status" {
  value = module.infra.cloudfront_distribution_status
}
output "cloudfront_distribution_tags" {
  value = module.infra.cloudfront_distribution_tags
}
output "cloudfront_distribution_trusted_signers" {
  value = module.infra.cloudfront_distribution_trusted_signers
}
output "cloudfront_monitoring_subscription_id" {
  value = module.infra.cloudfront_monitoring_subscription_id
}
output "cloudfront_origin_access_controls" {
  value = module.infra.cloudfront_origin_access_controls
}
output "cloudfront_origin_access_controls_ids" {
  value = module.infra.cloudfront_origin_access_controls_ids
}
output "cloudfront_origin_access_identities" {
  value = module.infra.cloudfront_origin_access_identities
}
output "cloudfront_origin_access_identity_iam_arns" {
  value = module.infra.cloudfront_origin_access_identity_iam_arns
}
output "cloudfront_origin_access_identity_ids" {
  value = module.infra.cloudfront_origin_access_identity_ids
}

