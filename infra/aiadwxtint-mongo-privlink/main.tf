# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: 7.17.2
# template-version: EXPERIMENTAL
# module-version: v8.1.1
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/aiadwxtint-mongo-privlink.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/privatelink_aws_kafka?ref=v8.1.1"
  aws_infra_region = "us-east-1"
  bootstrap = "aws-playground-pl-1.sukxj.mongodb.net:27017"
  confluent_azs = [
    "use1-az2",
    "use1-az4",
    "use1-az6"
  ]
  name = "aiadwxtint-mongo-privlink"
  privatelink_service_name = "com.amazonaws.vpce.us-east-1.vpce-svc-09932a0733495897a"
  subnet_tags = {
    Tier = "public"
  }
  vpc_name = "aiadwxt-int"
  
}

output "subnets_to_privatelink" {
  value = module.infra.subnets_to_privatelink
}
