# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.44
# template-version: 7.18.44
# module-version: v10.11.13
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "aws_pub_dns_credential" {
path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "infra_credential" {
path = replace("secret/data/mccprod/infra/dfwha-prod/openstack", "/data/", "/")
}

provider "openstack" {
# v3
application_credential_id     = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_ID"], null)
application_credential_secret = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_SECRET"], null)
# v2
tenant_name         = try(data.vault_generic_secret.infra_credential.data["OS_TENANT_NAME"], data.vault_generic_secret.infra_credential.data["OS_PROJECT_NAME"], null)
user_name           = try(data.vault_generic_secret.infra_credential.data["OS_USERNAME"], null)
password            = try(data.vault_generic_secret.infra_credential.data["OS_PASSWORD"], null)
user_domain_name    = try(data.vault_generic_secret.infra_credential.data["OS_USER_DOMAIN_NAME"], null)
project_domain_name = try(data.vault_generic_secret.infra_credential.data["OS_PROJECT_DOMAIN_NAME"], null)
insecure = try(data.vault_generic_secret.infra_credential.data["OS_INSECURE"], false)
# common
auth_url = data.vault_generic_secret.infra_credential.data["OS_AUTH_URL"]
region   = data.vault_generic_secret.infra_credential.data["OS_REGION_NAME"]
}


# DNS AWS provider
provider "aws" {
access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
region     = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
use_fips_endpoint = false
}

provider "aws" {
alias      = "dns"
access_key = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_ACCESS_KEY_ID"]
secret_key = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
region     = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_DEFAULT_REGION"]
use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/lb-lma.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
      source = "terraform-provider-openstack/openstack"
    }
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/lb-lma.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/load-balancer?ref=v10.11.13"
  providers = {
    aws = aws
    aws.dns = aws.dns
    openstack = openstack
  }
  deployment_group = "aggmet-3az-prod"
  domain = "prod.infra.webex.com"
  ingressgateway_name = "lma-pub-ingressgateway"
  loadbalancer_provider = "f5"
  mesh_name = "lma"
  network_name = "kubed-nlb-public-network"
  
}
output "lb_a_record" {
  value = module.infra.lb_a_record
}
output "lb_id" {
  value = module.infra.lb_id
}
output "lb_name" {
  value = module.infra.lb_name
}
output "lb_provider" {
  value = module.infra.lb_provider
}
output "lb_vip_address" {
  value = module.infra.lb_vip_address
}

