# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.6
# template-version: EXPERIMENTAL
# module-version: v10.2.15
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/ap-northeast-1-resolvers.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/route53_endpoint?ref=v10.2.15"
  aws_infra_region = "ap-northeast-1"
  direction = "OUTBOUND"
  infra_credentials_path = "secret/mccprod/infra/mpe-aws-prod/aws"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "ap-northeast-1-resolvers"
  resolver_rules = [
    {
      domain_name = "webex.com"
      name = "webex-com-resolver"
      rule_type = "FORWARD"
      targets = [
        {
          target_ip = "*******"
          target_port = 53
        },
        {
          target_ip = "**************"
          target_port = 53
        }
      ]
      vpc_ids = [
        "vpc-0b4f98626c107f52c",
        "vpc-09298b06b2e36213d"
      ]
    },
    {
      domain_name = "wbx2.com"
      name = "wbx2-com-resolver"
      rule_type = "FORWARD"
      targets = [
        {
          target_ip = "*******"
          target_port = 53
        },
        {
          target_ip = "**************"
          target_port = 53
        }
      ]
      vpc_ids = [
        "vpc-0b4f98626c107f52c",
        "vpc-09298b06b2e36213d"
      ]
    },
    {
      domain_name = "ciscospark.com"
      name = "ciscospark-com-resolver"
      rule_type = "FORWARD"
      targets = [
        {
          target_ip = "*******"
          target_port = 53
        },
        {
          target_ip = "**************"
          target_port = 53
        }
      ]
      vpc_ids = [
        "vpc-0b4f98626c107f52c",
        "vpc-09298b06b2e36213d"
      ]
    }
  ]
  subnet_tier = "private"
  vpc_name = "anrtwxt-prod"
  
}
