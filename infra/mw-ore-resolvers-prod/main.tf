# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.6
# template-version: EXPERIMENTAL
# module-version: v8.1.8_with_aws_efs
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/mw-ore-resolvers-prod.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/route53_endpoint?ref=96d2955454ea33b8b2d77eddeb33888835d28466"
  aws_infra_region = "us-west-2"
  direction = "OUTBOUND"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "mw-ore-resolvers-prod"
  resolver_rules = [
    {
      domain_name = "webex.com"
      name = "webex-resolver"
      rule_type = "FORWARD"
      targets = [
        {
          target_ip = "************"
          target_port = 53
        },
        {
          target_ip = "************"
          target_port = 53
        }
      ]
    }
  ]
  subnet_tier = "provider"
  vpc_name = "aore-mw-prod"
  
}
