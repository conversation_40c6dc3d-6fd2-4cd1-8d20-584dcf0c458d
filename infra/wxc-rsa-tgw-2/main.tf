# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.17
# template-version: 7.18.19
# module-version: v10.7.5
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/wxc-rsa-tgw-2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/wxc-rsa-tgw-2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/transit_gateway?ref=v10.7.5"
  aws_infra_region = "af-south-1"
  extra_tgw_filters = [
    {
      name = "transit-gateway-id"
      values = [
        "tgw-039ec0e7c854e43b6"
      ]
    }
  ]
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  name = "wxc-rsa-tgw-2"
  routes = [
    {
      subnet = "10.192.112.128/25"
      subnet_tags = {
        webex_purpose = "access-untrusted-media"
      }
    },
    {
      subnet = "10.192.113.0/25"
      subnet_tags = {
        webex_purpose = "access-untrusted-media"
      }
    },
    {
      subnet = "10.192.113.128/25"
      subnet_tags = {
        webex_purpose = "access-untrusted-media"
      }
    }
  ]
  subnet_filter = {
    webex_purpose = "access-untrusted-media"
  }
  tgw_owner_id = "423400439627"
  vpc_name = "p2_cal2"
  
}

