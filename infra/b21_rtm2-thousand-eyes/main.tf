# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.22
# template-version: 7.18.22
# module-version: v10.13.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/b21_rtm2-thousand-eyes.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/b21_rtm2-thousand-eyes.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/eni?ref=v10.13.4"
  aws_infra_region = "ap-northeast-3"
  eni_pools = {
    thousand-eyes-access-media-prv = {
      availability_zones = [
        "ap-northeast-3a",
        "ap-northeast-3b",
        "ap-northeast-3c"
      ]
      dns_prefix = "te-access-media-prv"
      dns_zone = "b21.prod.infra.webex.com"
      eip = {
        enabled = false
      }
      instances_per_az = 1
      security_group_names = [
        "thousand-eyes",
        "sse_media"
      ]
      subnet_tags = {
        webex_purpose = "access-trusted-media"
        webex_reachability = "private-provider"
      }
    }
    thousand-eyes-access-media-pub = {
      availability_zones = [
        "ap-northeast-3a",
        "ap-northeast-3b",
        "ap-northeast-3c"
      ]
      dns_prefix = "te-access-media-pub"
      dns_zone = "b21.prod.infra.webex.com"
      eip = {
        enabled = false
      }
      instances_per_az = 1
      security_group_names = [
        "thousand-eyes",
        "sse_ext"
      ]
      subnet_tags = {
        webex_purpose = "access-media"
        webex_reachability = "public-provider"
      }
    }
    thousand-eyes-public = {
      availability_zones = [
        "ap-northeast-3a",
        "ap-northeast-3b",
        "ap-northeast-3c"
      ]
      dns_prefix = "te-public"
      dns_zone = "b21.prod.infra.webex.com"
      eip = {
        create = true
        enabled = true
      }
      instances_per_az = 1
      is_public = true
      security_group_names = [
        "thousand-eyes"
      ]
      subnet_tags = {
        webex_purpose = "service"
        webex_reachability = "public"
      }
    }
    thousand-eyes-trunk-media-prv = {
      availability_zones = [
        "ap-northeast-3a",
        "ap-northeast-3b",
        "ap-northeast-3c"
      ]
      dns_prefix = "te-trunk-media-prv"
      dns_zone = "b21.prod.infra.webex.com"
      eip = {
        enabled = false
      }
      instances_per_az = 1
      security_group_names = [
        "thousand-eyes"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "private-provider"
      }
    }
    thousand-eyes-trunk-media-pub = {
      availability_zones = [
        "ap-northeast-3a",
        "ap-northeast-3b",
        "ap-northeast-3c"
      ]
      dns_prefix = "te-trunk-media-pub"
      dns_zone = "b21.prod.infra.webex.com"
      eip = {
        enabled = false
      }
      instances_per_az = 1
      security_group_names = [
        "thousand-eyes"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "public-provider"
      }
    }
  }
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  security_groups = {
    sse_ext = [
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "all-egress"
        protocol = "all"
        to = 0
        type = "egress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 5061
        name = "sse-sip-internal-tcp-3"
        protocol = "tcp"
        to = 5061
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 5062
        name = "sse-sip-internal-tcp-4"
        protocol = "tcp"
        to = 5062
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 8934
        name = "sse-sip-internal-tcp-5"
        protocol = "tcp"
        to = 8934
        type = "ingress"
      }
    ]
    sse_media = [
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "all-egress"
        protocol = "all"
        to = 0
        type = "egress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 5060
        name = "sse-sip-internal-udp-1"
        protocol = "udp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "**********/12"
        from = 5060
        name = "sse-sip-internal-udp-2"
        protocol = "udp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 5060
        name = "sse-sip-internal-tcp-1"
        protocol = "tcp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "**********/12"
        from = 5060
        name = "sse-sip-internal-tcp-2"
        protocol = "tcp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 3000
        name = "sse-lb-udp"
        protocol = "udp"
        to = 5000
        type = "ingress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 3000
        name = "sse-lb-tcp"
        protocol = "tcp"
        to = 5000
        type = "ingress"
      }
    ]
    thousand-eyes = [
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "thousand-eyes-icmp-echo-reply"
        protocol = "ICMP"
        to = 0
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 8
        name = "thousand-eyes-icmp-echo"
        protocol = "ICMP"
        to = 8
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 3
        name = "thousand-eyes-icmp-unreachable"
        protocol = "ICMP"
        to = 3
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 11
        name = "thousand-eyes-icmp-time-exceeded"
        protocol = "ICMP"
        to = 11
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 49153
        name = "thousand-eyes-a2a-tcp"
        protocol = "TCP"
        to = 49153
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 49153
        name = "thousand-eyes-a2a-udp"
        protocol = "UDP"
        to = 49153
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 443
        name = "thousand-eyes-https"
        protocol = "TCP"
        to = 443
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "allow-all-egress"
        protocol = "all"
        to = 0
        type = "egress"
      }
    ]
  }
  vpc_name = "b21_rtm2"
  
}
output "network_interfaces" {
  value = module.infra.network_interfaces
}

