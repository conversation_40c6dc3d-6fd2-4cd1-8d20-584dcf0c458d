# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v10.13.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

provider "aws" {
  access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
}

data "vault_generic_secret" "infra_credential" {
  path = replace("secret/data/mccprod/infra/wsjc03-ocp4-prod/openstack", "/data/", "/")
}

provider "openstack" {
  # v3
  application_credential_id     = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_ID"], null)
  application_credential_secret = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_SECRET"], null)
  # v2
  tenant_name = try(data.vault_generic_secret.infra_credential.data["OS_TENANT_NAME"], null)
  user_name   = try(data.vault_generic_secret.infra_credential.data["OS_USERNAME"], null)
  password    = try(data.vault_generic_secret.infra_credential.data["OS_PASSWORD"], null)

  # common
  auth_url = data.vault_generic_secret.infra_credential.data["OS_AUTH_URL"]
  region   = data.vault_generic_secret.infra_credential.data["OS_REGION_NAME"]
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/sjc03-vlan687-rr.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
      source = "terraform-provider-openstack/openstack"
    }
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/sjc03-vlan687-rr.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/route-reflector?ref=v10.13.4"
  providers = {
    aws = aws
    openstack = openstack
  }
  availability_zones = [
    "az1"
  ]
  control_network = "provider-411"
  create_dns_record = true
  domain = "prod.infra.webex.com"
  enable_block_storage = true
  flavor = "kubed.gv.2vcpu.4mem.0ssd.0eph"
  force_delete = true
  image_name = "wbx3-capi-jammy-1.31.5-c8d-amd64-v1.29.6"
  iso_network = "workload_Isolated_net_vlan687"
  iso_network_ips = {
    az1 = [
      "***********",
      "***********"
    ]
  }
  name = "sjc03-vlan687-rr"
  ocp_ownership_business_service = "WBX3 Platform - Meetings"
  ocp_ownership_component = "wxkb"
  ocp_ownership_server_type = "wxkbsvr"
  ocp_ownership_support_group = "wbx3-prod"
  replicas = 2
  server_group_policies = [
    "anti-affinity"
  ]
  
}
output "instances" {
  value = module.infra.instances
}

