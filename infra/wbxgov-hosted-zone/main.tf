# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.19
# template-version: 7.19.4
# module-version: hosted-zone
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = data.vault_generic_secret.aws_infra_credential.data["AWS_DEFAULT_REGION"]
  use_fips_endpoint = false

  default_tags {
    tags = {
      ApplicationName = "wbx3_platform"
      CiscoMailAlias  = "<EMAIL>"
      ResourceOwner   = "BU_Collaboration"
    }
  }
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/wbxgov-hosted-zone.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/wbxgov-hosted-zone.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/route53_hosted_zone?ref=69faa8796f5869616bf981603035332f9037ac4b"
  providers = {
    aws = aws
  }
  associated_vpcs = [
    {
      vpc_id = "vpc-07c5b6144fdede8da"
      vpc_region = "us-east-1"
    }
  ]
  cloud = "mccprod"
  dnssec_enable = false
  name = "wbxgov-hosted-zone"
  private_hosted_zone = true
  use_existing_zone = false
  zone_name = "int.webexgov.us"
  
}

