# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: 7.18.33
# module-version: dnssec-nlb
# wbx3-infra version: <nil>
# resource: wxtmer-int-first-mercury2
# resource type: <nil>
# environment: a-usea1-i0-0
# -----------------------------------------------#~~ Resource Values
args:
    additional_domains:
        - a00.ite.webexgov.us
    assign_eip: true
    aws_infra_region: us-east-1
    deployment_group: wxtmer-int-first-mercury2
    domain: a00.prod.infra.webex.com
    ingressgateway_name: wxt-mercury-ingressgateway
    ip_address_type: dualstack
    ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
    mesh_name: wxt-mercury
    subnet_type: public-eip
    target_groups:
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: a01_app2
env_name: a-usea1-i0-0
module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
module_path: modules/aws/nlb
module_version: dnssec-nlb
name: wxtmer-int-first-mercury2

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.33
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.3
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/nlb/wxtmer-int-first-mercury2
    name: wxtmer-int-first-mercury2
    workflow: standard
dir: infra/nlb
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/a-usea1-i0-0/a-usea1-i0-0-ha2/load-balancers/manifest.yaml
s3_bucket_path: terraform-state/infra/nlb/wxtmer-int-first-mercury2.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.33
    templateversion: 7.18.33
workflow: standard