# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.30
# template-version: v7.19.8-next
# module-version: v10.16.11
# wbx3-infra version: <nil>
# -----------------------------------------------

name: nlb-us-ohacuib2-c2pshared
env_name: a-usea2-i3-2

# Resource Values
admin_port: 6442
args:
    allowed_clusters:
        - us-ohacuib2
    assign_eip: true
    aws_infra_region: us-east-2
    deployment_group: kubed-prod-gen
    domain: ciscoplatform.cloud
    ingressgateway_name: c2p-shared-ingressgateway
    ip_address_type: dualstack
    ipv4_eip_pool: ipv4pool-ec2-02605f051c656037f
    mesh_name: c2p-shared
    security_groups:
        istio-pub:
            rules:
                - cidrs:
                    - 0.0.0.0/0
                  from: 443
                  ipv6_cidrs:
                    - ::/0
                  name: https
                  protocol: tcp
                  to: 443
                  type: ingress
                - cidrs:
                    - 0.0.0.0/0
                  from: 15021
                  ipv6_cidrs:
                    - ::/0
                  name: health
                  protocol: tcp
                  to: 15021
                  type: ingress
                - cidrs:
                    - 0.0.0.0/0
                  from: 0
                  ipv6_cidrs:
                    - ::/0
                  name: egress
                  protocol: tcp
                  to: 65535
                  type: egress
    subnet_type: public-eip
    tags:
        CcpAccountId: 32bc6b5d-84a0-4df6-9838-fb9a16d4cffb
        CcpOrganizationId: 9edd5e35-1f5a-4fbc-978d-8f250f3501b0
    target_groups:
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: c20_app1
aws_infra_az: <replace-me>
base_image: <replace-me>
base_k8s_image: <replace-me>
bastion_count: 1
bastion_flavor: 2vcpu.4mem.80ssd.0eph
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: <not-used>
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
infra_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
ingress_count: 3
ingress_flavor: 8vcpu.16mem.80ssd.0eph
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
master_count: 3
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
module_version: v10.16.11
nameservers:
    - <replace-me>
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
provisioning_module_version: master
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
server_group_policies:
    - anti-affinity
thousand_eyes_count: 0
use_floating_ips: false
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: 8vcpu.32mem.80ssd.0eph

# No Blueprint defined values used


# Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infractl_version: v7.18.30
s3_bucket_region: us-east-2
terraform_version: v1.6.3
use_provider_template: true

# Default Values
address_pools:
    pods_0: **********/15
    pods_1: **********/13
    pods_2: **********/12
    pods_3: **********/14