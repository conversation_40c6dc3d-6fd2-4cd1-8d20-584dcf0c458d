# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: v7.19.8-next
# module-version: v10.16.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_s3_credential" {
path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

# Infra AWS Provider
provider "aws" {
region            = "us-west-2"
use_fips_endpoint = false
}

# DNS AWS provider
provider "aws" {
alias             = "dns"
region            = "us-west-2"
use_fips_endpoint = false
}

provider "aws" {
  alias             = "s3"
  access_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = "us-east-2"
  use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/nlb/wxtmsg-p-aore-message1-a.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/nlb/wxtmsg-p-aore-message1-a.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/nlb?ref=v10.16.4"
  providers = {
    aws = aws
    aws.dns = aws.dns
  }
  allowed_clusters = [
    "us-orawxt0"
  ]
  assign_eip = true
  custom_nlb_name = "wxtmsg-prod-aore-message1"
  deployment_group = "wxtmsg-prod-aore-message1"
  domain = "b80.prod.infra.webex.com"
  ingressgateway_name = "wxt-message-ingressgateway"
  ip_address_type = "dualstack"
  ipv4_eip_pool = "ipv4pool-ec2-021a4b5fd403fe977"
  mesh_name = "wxt-message"
  subnet_type = "public-eip"
  target_groups = {
    TCP-15021 = {
      hc_matcher = "200-399"
      hc_path = "/healthz/ready"
      hc_port = 15021
      hc_protocol = "HTTP"
      port = 15021
      protocol = "TCP"
      target_type = "ip"
    }
    TCP-443 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 443
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
  }
  vpc_name = "b80_app1"
  
}
output "nlb_a_record" {
  value = module.infra.nlb_a_record
}
output "nlb_arn" {
  value = module.infra.nlb_arn
}
output "nlb_dns_name" {
  value = module.infra.nlb_dns_name
}
output "nlb_ds_record" {
  value = module.infra.nlb_ds_record
}
output "target_group_arn" {
  value = module.infra.target_group_arn
}

