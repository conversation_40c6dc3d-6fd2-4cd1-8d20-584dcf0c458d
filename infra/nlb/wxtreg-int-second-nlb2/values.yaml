# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.48
# module-version: dnssec-nlb
# wbx3-infra-version: <nil>
# resource: wxtreg-int-second-nlb2
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    additional_domains:
        - b61.ite.webexgov.us
    assign_eip: true
    aws_infra_region: us-west-2
    deployment_group: wxtreg-int-second-registration2
    domain: b61.prod.infra.webex.com
    ingressgateway_name: wxt-registration-ingressgateway
    ip_address_type: dualstack
    mesh_name: wxt-registration
    subnet_type: public-eip
    target_groups:
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: b61_app2
env_name: a-uswe2-i0-1
module_commit: d5f934f321714fde56062375e92e55f47f95e72b
module_path: modules/aws/nlb
module_version: dnssec-nlb
name: wxtreg-int-second-nlb2

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cloud_service_provider: aws
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: b60.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-int/aws
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.36
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/route53/credentials
terraform_version: v1.5.7
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/nlb/wxtreg-int-second-nlb2
    name: wxtreg-int-second-nlb2
    workflow: standard
dir: infra/nlb
external: false
include_defaults: true
include_environment: true
manifest_path: manifests/a-uswe2-i0-1/a-uswe2-i0-1-ha2/load-balancers/manifest.yaml
s3_bucket_path: terraform-state/infra/nlb/wxtreg-int-second-nlb2.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.48
    templateversion: 7.18.48
workflow: standard