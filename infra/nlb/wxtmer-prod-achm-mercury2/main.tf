# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.20.5
# module-version: v10.16.6
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_s3_credential" {
path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

# Infra AWS Provider
provider "aws" {
region            = "us-east-2"
use_fips_endpoint = false
}

# DNS AWS provider
provider "aws" {
alias             = "dns"
region            = "us-east-2"
use_fips_endpoint = false
}

provider "aws" {
  alias             = "s3"
  access_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = "us-east-2"
  use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/nlb/wxtmer-prod-achm-mercury2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/nlb/wxtmer-prod-achm-mercury2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/nlb?ref=v10.16.6"
  providers = {
    aws = aws
    aws.dns = aws.dns
  }
  assign_eip = true
  custom_nlb_name = "wxtmer-prod-achm-mercury2-legacy"
  deployment_group = "wxtmer-prod-achm-mercury2"
  domain = "prod.infra.webex.com"
  ingressgateway_name = "wxt-mercury-ingressgateway"
  ip_address_type = "dualstack"
  ipv4_eip_pool = "ipv4pool-ec2-0acf5e097d78cdf09"
  mesh_name = "mesh-wxt-mercury"
  security_groups = {
    istio-pub = {
      rules = [
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          from = 443
          ipv6_cidrs = [
            "::/0"
          ]
          name = "https"
          protocol = "tcp"
          to = 443
          type = "ingress"
        },
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          from = 8443
          ipv6_cidrs = [
            "::/0"
          ]
          name = "https"
          protocol = "tcp"
          to = 8443
          type = "ingress"
        },
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          from = 15021
          ipv6_cidrs = [
            "::/0"
          ]
          name = "health"
          protocol = "tcp"
          to = 15021
          type = "ingress"
        },
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          from = 0
          ipv6_cidrs = [
            "::/0"
          ]
          name = "egress"
          protocol = "tcp"
          to = 65535
          type = "egress"
        }
      ]
    }
  }
  subnet_type = "legacy_public"
  target_groups = {
    TCP-15021 = {
      hc_matcher = "200-399"
      hc_path = "/healthz/ready"
      hc_port = 15021
      hc_protocol = "HTTP"
      port = 15021
      protocol = "TCP"
      target_type = "ip"
    }
    TCP-443 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 443
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-5061 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 5061
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-5062 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 5062
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
  }
  vpc_name = "acmhwxt-prod-v2"
  
}
output "nlb_a_record" {
  value = module.infra.nlb_a_record
}
output "nlb_arn" {
  value = module.infra.nlb_arn
}
output "nlb_dns_name" {
  value = module.infra.nlb_dns_name
}
output "nlb_ds_record" {
  value = module.infra.nlb_ds_record
}
output "nlb_security_group_ids" {
  value = module.infra.nlb_security_group_ids
}
output "target_group_arn" {
  value = module.infra.target_group_arn
}

