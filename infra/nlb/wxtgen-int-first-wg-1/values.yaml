# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: v7.19.8-next
# module-version: dnssec-nlb
# wbx3-infra version: <nil>
# resource: wxtgen-int-first-wg-1
# resource type: <nil>
# environment: a-usea1-i0-0
# -----------------------------------------------#~~ Resource Values
args:
    additional_domains:
        - a00.ite.webexgov.us
    allowed_clusters:
        - us-vaawxti0
    assign_eip: true
    aws_infra_region: us-east-1
    deployment_group: wxtgen-int-first-wg-general1
    domain: a00.prod.infra.webex.com
    ingressgateway_name: wxt-general-wg-ingressgateway
    ip_address_type: dualstack
    ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
    mesh_name: wxt-general-wg
    subnet_type: public-eip
    target_groups:
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15020:
            hc_port: 15021
            hc_protocol: TCP
            port: 15020
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: a00_app1
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
env_name: a-usea1-i0-0
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.33
module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
module_path: modules/aws/nlb
module_version: dnssec-nlb
name: wxtgen-int-first-wg-1
s3_bucket_path: terraform-state/infra/nlb/wxtgen-int-first-wg-1.tfstate
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.3
use_provider_template: true

#~~ No Blueprint Values


#~~ Environment Values
consul_host: consul.int.mcc01.prod.infra.webex.com

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/nlb/wxtgen-int-first-wg-1
    name: wxtgen-int-first-wg-1
    workflow: standard
dir: infra/nlb
external: false
include_defaults: false
include_environment: false
manifest_path: manifest.yaml
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: v7.19.8-next
    templateversion: v7.19.8-next
workflow: standard