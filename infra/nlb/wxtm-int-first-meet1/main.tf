# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: v7.18.44-EXPERIMENTAL-c8204df
# module-version: dnssec-nlb
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_s3_credential" {
path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

# Infra AWS Provider
provider "aws" {
region            = "us-east-1"
use_fips_endpoint = false
}

# DNS AWS provider
provider "aws" {
alias             = "dns"
region            = "us-east-1"
use_fips_endpoint = false
}

provider "aws" {
  alias             = "s3"
  access_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = "us-east-2"
  use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/nlb/wxtm-int-first-meet1.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/nlb/wxtm-int-first-meet1.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/nlb?ref=46b39ffce2c4f07eee308d85ceb079349cb2cf43"
  providers = {
    aws = aws
    aws.dns = aws.dns
  }
  additional_domains = [
    "a00.ite.webexgov.us"
  ]
  assign_eip = true
  deployment_group = "wxtm-int-first-meet1"
  domain = "a00.prod.infra.webex.com"
  ingressgateway_name = "wxt-meet-ingressgateway"
  ip_address_type = "dualstack"
  ipv4_eip_pool = "ipv4pool-ec2-0277a64bec3ddeb69"
  mesh_name = "wxt-meet"
  subnet_type = "public-eip"
  target_groups = {
    TCP-15020 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 15020
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-15021 = {
      hc_matcher = "200-399"
      hc_path = "/healthz/ready"
      hc_port = 15021
      hc_protocol = "HTTP"
      port = 15021
      protocol = "TCP"
      target_type = "ip"
    }
    TCP-15061 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 15061
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-15062 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 15062
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-25061 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 25061
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-25062 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 25062
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-443 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 443
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-5061 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 5061
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
    TCP-5062 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 5062
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
  }
  vpc_name = "a00_app1"
  
}
output "nlb_a_record" {
  value = module.infra.nlb_a_record
}
output "nlb_arn" {
  value = module.infra.nlb_arn
}
output "nlb_dns_name" {
  value = module.infra.nlb_dns_name
}
output "nlb_ds_record" {
  value = module.infra.nlb_ds_record
}
output "target_group_arn" {
  value = module.infra.target_group_arn
}

