# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.20.5
# module-version: v10.16.6
# wbx3-infra-version: <nil>
# resource: wxc-general-pub-afra2
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    allowed_clusters:
        - afrawxt-prd-2
    assign_eip: true
    aws_infra_region: eu-central-1
    custom_nlb_name: wxc-general-pub-afra2-legacy
    deployment_group: mobius-prod-wxt-eu
    domain: prod.infra.webex.com
    ingressgateway_name: wxc-general-pub-ingressgateway
    ip_address_type: dualstack
    ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
    mesh_name: wxc-general
    security_groups:
        istio-pub:
            rules:
                - cidrs:
                    - 0.0.0.0/0
                  from: 443
                  ipv6_cidrs:
                    - ::/0
                  name: https
                  protocol: tcp
                  to: 443
                  type: ingress
                - cidrs:
                    - 0.0.0.0/0
                  from: 15021
                  ipv6_cidrs:
                    - ::/0
                  name: health
                  protocol: tcp
                  to: 15021
                  type: ingress
                - cidrs:
                    - 0.0.0.0/0
                  from: 0
                  ipv6_cidrs:
                    - ::/0
                  name: egress
                  protocol: tcp
                  to: 65535
                  type: egress
    subnet_type: legacy_public
    target_groups:
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: afrawxt-prod
env_name: afrawxt-prod
infractl_version: v7.19.0
module_path: modules/aws/nlb
module_version: v10.16.6
name: wxc-general-pub-afra2
terraform_version: v1.5.3

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/nlb/wxc-general-pub-afra2
    name: wxc-general-pub-afra2
    workflow: standard
dir: infra/nlb
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/a-euce1-p0-0/a-euce1-p0-0-ha2/load-balancers/manifest.yaml
s3_bucket_path: terraform-state/infra/nlb/wxc-general-pub-afra2.tfstate
versioningData:
    infractlversion: 7.20.5
    templateversion: 7.20.5
workflow: standard