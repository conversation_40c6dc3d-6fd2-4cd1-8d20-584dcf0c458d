# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.0
# module-version: v10.16.4
# wbx3-infra-version: <nil>
# resource: wxtci-prod-aore-ci2
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    assign_eip: true
    aws_infra_region: us-west-2
    custom_nlb_name: wxtci-prod-aore-ci2-legacy
    deployment_group: wxtci-prod-aore-ci2
    domain: prod.infra.webex.com
    ingressgateway_name: wxt-ci-ingressgateway
    ip_address_type: dualstack
    ipv4_eip_pool: ipv4pool-ec2-002c70c81368c701c
    mesh_name: wxt-ci
    subnet_type: legacy_public
    target_groups:
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: aorewxt-prod-v2
env_name: aorewxt-prod-v2
infractl_version: v7.19.0
module_path: modules/aws/nlb
module_version: v10.16.4
name: wxtci-prod-aore-ci2
terraform_version: v1.5.3

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/nlb/wxtci-prod-aore-ci2
    name: wxtci-prod-aore-ci2
    workflow: standard
dir: infra/nlb
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/a-uswe2-p0-0/a-uswe2-p0-0-ha2/load-balancers/manifest.yaml
s3_bucket_path: terraform-state/infra/nlb/wxtci-prod-aore-ci2.tfstate
versioningData:
    infractlversion: 7.19.0
    templateversion: 7.19.0
workflow: standard