# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.30
# template-version: 7.19.1
# module-version: v10.16.11
# wbx3-infra-version: <nil>
# resource: platform-infra-ccpb2
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    allowed_clusters:
        - us-oraccpb2
    assign_eip: true
    aws_infra_region: us-west-2
    deployment_group: kubed-prod-gen
    domain: ciscoplatform.cloud
    ingressgateway_name: infra-cisco-int-ingressgateway
    ip_address_type: dualstack
    ipv4_eip_pool: ipv4pool-ec2-08b46eca4b134628f
    mesh_name: platform-infra
    security_groups:
        istio-pub:
            rules:
                - cidrs:
                    - 0.0.0.0/0
                  from: 443
                  ipv6_cidrs:
                    - ::/0
                  name: https
                  protocol: tcp
                  to: 443
                  type: ingress
                - cidrs:
                    - 0.0.0.0/0
                  from: 15021
                  ipv6_cidrs:
                    - ::/0
                  name: health
                  protocol: tcp
                  to: 15021
                  type: ingress
                - cidrs:
                    - 0.0.0.0/0
                  from: 0
                  ipv6_cidrs:
                    - ::/0
                  name: egress
                  protocol: tcp
                  to: 65535
                  type: egress
    subnet_type: public-eip
    tags:
        CcpAccountId: 32bc6b5d-84a0-4df6-9838-fb9a16d4cffb
        CcpOrganizationId: 9edd5e35-1f5a-4fbc-978d-8f250f3501b0
    target_groups:
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: c30_app1
dns_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
env_name: a-uswe2-i3-0
infra_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
infractl_version: v7.18.30
module_path: modules/aws/nlb
module_version: v10.16.11
name: platform-infra-ccpb2
terraform_version: v1.6.3

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ Default Values
admin_port: 6442
aws_infra_az: <replace-me>
base_image: <replace-me>
base_k8s_image: <replace-me>
bastion_count: 1
bastion_flavor: 2vcpu.4mem.80ssd.0eph
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
dev_cluster: false
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: <not-used>
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
ingress_count: 3
ingress_flavor: 8vcpu.16mem.80ssd.0eph
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
master_count: 3
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
provisioning_module_version: master
server_group_policies:
    - anti-affinity
thousand_eyes_count: 0
use_floating_ips: false
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: 8vcpu.32mem.80ssd.0eph

#~~ Internally Generated Values
atlantis_options:
    dir: infra/nlb/platform-infra-ccpb2
    name: platform-infra-ccpb2
    workflow: standard
dir: infra/nlb
external: false
include_defaults: true
include_environment: false
manifest_path: manifests/a-uswe2-i3-0/a-uswe2-i3-0-ha1/infra/manifest.yaml
s3_bucket_path: terraform-state/infra/nlb/platform-infra-ccpb2.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.19.1
    templateversion: 7.19.1
workflow: standard