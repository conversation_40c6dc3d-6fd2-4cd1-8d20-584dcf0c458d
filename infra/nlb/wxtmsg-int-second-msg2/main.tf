# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.48
# module-version: dnssec-nlb
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-int/aws", "/data/", "/")
}
data "vault_generic_secret" "aws_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}
data "vault_generic_secret" "aws_s3_credential" {
path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

# Infra AWS Provider
provider "aws" {
access_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
secret_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
region            = "us-west-2"
use_fips_endpoint = false
}

# DNS AWS provider
provider "aws" {
alias             = "dns"
access_key        = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
secret_key        = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
region            = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
use_fips_endpoint = false
}

provider "aws" {
  alias             = "s3"
  access_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = "us-east-2"
  use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/nlb/wxtmsg-int-second-msg2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/nlb/wxtmsg-int-second-msg2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/nlb?ref=d5f934f321714fde56062375e92e55f47f95e72b"
  providers = {
    aws = aws
    aws.dns = aws.dns
  }
  additional_domains = [
    "b61.ite.webexgov.us"
  ]
  assign_eip = true
  deployment_group = "wxtmsg-int-second-message2"
  domain = "b61.prod.infra.webex.com"
  ingressgateway_name = "wxt-message-ingressgateway"
  ip_address_type = "dualstack"
  mesh_name = "wxt-message"
  subnet_type = "public-eip"
  target_groups = {
    TCP-15021 = {
      hc_matcher = "200-399"
      hc_path = "/healthz/ready"
      hc_port = 15021
      hc_protocol = "HTTP"
      port = 15021
      protocol = "TCP"
      target_type = "ip"
    }
    TCP-443 = {
      hc_port = 15021
      hc_protocol = "TCP"
      port = 443
      preserve_client_ip = true
      protocol = "TCP"
      proxy_protocol_v2 = true
      target_type = "ip"
    }
  }
  vpc_name = "b61_app2"
  
}
output "nlb_a_record" {
  value = module.infra.nlb_a_record
}
output "nlb_arn" {
  value = module.infra.nlb_arn
}
output "nlb_dns_name" {
  value = module.infra.nlb_dns_name
}
output "nlb_ds_record" {
  value = module.infra.nlb_ds_record
}
output "target_group_arn" {
  value = module.infra.target_group_arn
}

