# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: 7.18.33
# module-version: v10.8.10
# wbx3-infra version: <nil>
# resource: nlb-mw-ats-pub-mls
# resource type: <nil>
# environment: a-usea1-a0-0
# -----------------------------------------------#~~ Resource Values
args:
    assign_eip: true
    aws_infra_region: us-east-1
    deployment_group: ats1
    domain: a20.prod.infra.webex.com
    ingressgateway_name: mw-pub-ingressgateway-mls
    ip_address_type: dualstack
    ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
    mesh_name: webapps
    subnet_type: public-eip
    target_groups:
        TCP-80:
            hc_port: 15021
            hc_protocol: TCP
            port: 80
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: a20_app1
env_name: a-usea1-a0-0
module_path: modules/aws/nlb
module_version: v10.8.10
name: nlb-mw-ats-pub-mls

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.33
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.3
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/nlb/nlb-mw-ats-pub-mls
    name: nlb-mw-ats-pub-mls
    workflow: standard
dir: infra/nlb
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/a-usea1-a0-0/a-usea1-a0-0-ha1/load-balancers/manifest.yaml
s3_bucket_path: terraform-state/infra/nlb/nlb-mw-ats-pub-mls.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.33
    templateversion: 7.18.33
workflow: standard