# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.1
# module-version: v10.16.4
# wbx3-infra-version: <nil>
# resource: wxtreg-prod-adxb-regnlb2
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    assign_eip: true
    aws_infra_region: me-central-1
    deployment_group: wxtreg-prod-adxb-registration2
    domain: p7.prod.infra.webex.com
    ingressgateway_name: wxt-registration-ingressgateway
    ip_address_type: dualstack
    ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
    mesh_name: wxt-registration
    subnet_type: public-eip
    target_groups:
        TCP-443:
            hc_port: 15021
            hc_protocol: TCP
            port: 443
            preserve_client_ip: true
            protocol: TCP
            proxy_protocol_v2: true
            target_type: ip
        TCP-15021:
            hc_matcher: 200-399
            hc_path: /healthz/ready
            hc_port: 15021
            hc_protocol: HTTP
            port: 15021
            protocol: TCP
            target_type: ip
    vpc_name: p7_mtg1
env_name: me-01a-ha2
infractl_version: v7.19.0
module_path: modules/aws/nlb
module_version: v10.16.4
name: wxtreg-prod-adxb-regnlb2
terraform_version: v1.5.3

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/nlb/wxtreg-prod-adxb-regnlb2
    name: wxtreg-prod-adxb-regnlb2
    workflow: standard
dir: infra/nlb
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/me-01a/me-01a-ha2/load-balancers/manifest.yaml
s3_bucket_path: terraform-state/infra/nlb/wxtreg-prod-adxb-regnlb2.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.19.1
    templateversion: 7.19.1
workflow: standard