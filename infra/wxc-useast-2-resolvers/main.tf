# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.4
# template-version: EXPERIMENTAL
# module-version: v7.23.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/mccprod/infra/mpe-aws-prod/aws
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/wxc-useast-2-resolvers.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/route53_endpoint?ref=v7.23.4"
  aws_infra_region = "us-east-2"
  direction = "OUTBOUND"
  infra_credentials_path = "secret/mccprod/infra/mpe-aws-prod/aws"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "wxc-useast-2-resolvers"
  resolver_rules = [
    {
      domain_name = "broadcloud.org"
      name = "broadcloud-resolver"
      rule_type = "FORWARD"
      targets = [
        {
          target_ip = "***********"
          target_port = 53
        },
        {
          target_ip = "***********"
          target_port = 53
        },
        {
          target_ip = "**********"
          target_port = 53
        },
        {
          target_ip = "**********"
          target_port = 53
        },
        {
          target_ip = "***********"
          target_port = 53
        },
        {
          target_ip = "***********"
          target_port = 53
        }
      ]
    },
    {
      domain_name = "prv.webex.com"
      name = "prv-webex-com-resolver"
      rule_type = "FORWARD"
      targets = [
        {
          target_ip = "************"
          target_port = 53
        },
        {
          target_ip = "************"
          target_port = 53
        }
      ]
    }
  ]
  subnet_tier = "provider"
  vpc_name = "a-wxc-useast2-prod"
  
}

