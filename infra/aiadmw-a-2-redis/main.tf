# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.10
# template-version: EXPERIMENTAL
# module-version: WXCNP-26984
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/aiadmw-a-2-redis.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/elasticache_redis?ref=1b2bc95b1e9bad092f556e0c0ebedbbc417b3ac7"
  aws_infra_region = "us-east-1"
  cluster_mode_enabled = true
  cluster_mode_num_node_groups = 3
  cluster_mode_replicas_per_node_group = 1
  engine_version = 6.2
  family = "redis6.x"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  instance_type = "cache.m6g.xlarge"
  name = "aiadmw-a-2-redis"
  port = 6379
  subnet_ids = [
    "subnet-09cd94aece7b84bbd"
  ]
  transit_encryption_enabled = true
  vpc_id = "vpc-0457792eb7cf43d9b"
  
}
output "elasticache_redis_arn" {
  value = module.infra.elasticache_redis_arn
}

