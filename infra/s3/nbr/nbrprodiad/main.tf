# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  / 
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /  
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/   
#
# codegen-version: v7.7.4
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra_s3/nbr/nbrprodiad.tfstate"
    region = "us-east-2"
  }
}


module "infra" {

  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/s3?ref=v7.7.3"

  aws_infra_region = "us-east-1"
  bucket = "nbrprodiad"
  infra_credentials_path = "secret/data/mccprod/infra/nbr-s3/s3-iam"
  
}
