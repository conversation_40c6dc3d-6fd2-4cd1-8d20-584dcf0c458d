# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.0
# module-version: v10.16.4
# wbx3-infra-version: <nil>
# resource: s3/billing-reports/asyd
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    aws_infra_region: ap-southeast-2
    bucket: wxt-bpb-billing-reports-asyd
    server_side_encryption_configuration:
        rule:
            apply_server_side_encryption_by_default:
                sse_algorithm: AES256
    tags:
        ApplicationName: Webex Teams
        CiscoMailAlias: <EMAIL>
        DataClassification: Cisco Highly Confidential
        DataTaxonomy: Cisco Operations Data
        Environment: PROD
        ResourceOwner: Cisco Cloud Engineering
        supportgroup: wx-teams-platform
        webexservice: wholesale-billing-service
env_name: billing-service-env
module_path: modules/aws/s3
name: s3/billing-reports/asyd

#~~ No Blueprint Values


#~~ Environment Values
admin_port: 6442
aws_infra_az: <replace-me>
backend: s3
base_image: <replace-me>
base_k8s_image: <replace-me>
bastion_count: 1
bastion_flavor: 2vcpu.4mem.80ssd.0eph
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: <not-used>
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.19.0
ingress_count: 3
ingress_flavor: 8vcpu.16mem.80ssd.0eph
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
master_count: 3
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
module_version: v10.16.4
nameservers:
    - <replace-me>
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
provisioning_module_version: master
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
server_group_policies:
    - anti-affinity
terraform_version: v1.5.3
thousand_eyes_count: 0
use_floating_ips: false
use_provider_template: true
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: 8vcpu.32mem.80ssd.0eph

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/s3/billing-reports/asyd
    name: s3/billing-reports/asyd
    workflow: standard
dir: infra
external: false
manifest_path: manifests/general/wholesale-billing-service/manifest.yaml
s3_bucket_path: terraform-state/infra/s3/billing-reports/asyd.tfstate
versioningData:
    infractlversion: 7.19.0
    templateversion: 7.19.0
workflow: standard