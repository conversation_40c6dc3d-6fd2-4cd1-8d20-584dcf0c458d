# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.14
# template-version: 7.18.14
# module-version: v10.8.0
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

provider "aws" {
  access_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = "me-central-1"
  use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/s3/migration-service/adxb.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/s3/migration-service/adxb.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/s3?ref=v10.8.0"
  providers = {
    aws = aws
  }
  aws_infra_region = "me-central-1"
  bucket = "personalcontacts-migration-prod-adxb"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
  tags = {
    ApplicationName = "migration-service"
    CiscoMailAlias = "<EMAIL>"
    DataClassification = "Cisco Highly Confidential"
    DataTaxonomy = "Cisco Operations Data"
    Environment = "PROD"
    ResourceOwner = "Cisco Cloud Engineering"
    supportgroup = "wx-teams-platform"
    webexservice = "migration-service"
  }
  
}
output "s3_bucket_arn" {
  value = module.infra.s3_bucket_arn
}
output "s3_bucket_bucket_domain_name" {
  value = module.infra.s3_bucket_bucket_domain_name
}
output "s3_bucket_bucket_regional_domain_name" {
  value = module.infra.s3_bucket_bucket_regional_domain_name
}
output "s3_bucket_hosted_zone_id" {
  value = module.infra.s3_bucket_hosted_zone_id
}
output "s3_bucket_id" {
  value = module.infra.s3_bucket_id
}
output "s3_bucket_region" {
  value = module.infra.s3_bucket_region
}
output "s3_bucket_website_domain" {
  value = module.infra.s3_bucket_website_domain
}
output "s3_bucket_website_endpoint" {
  value = module.infra.s3_bucket_website_endpoint
}


