# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.17
# template-version: 7.17.17
# module-version: v7.19.0
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/s3/udp/jabber-ue1.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/s3?ref=v7.19.0"
  aws_infra_region = "us-east-1"
  bucket = "wap-udp-jabber-bts-useast1"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  lifecycle_rule = [
    {
      enabled = true
      noncurrent_version_expiration = {
        days = 1
      }
    }
  ]
  logging = {
    target_bucket = "wap-udp-auditlog-bts-useast1"
    target_prefix = "jabber-"
  }
  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
  tags = {
    DataTaxonomy = "Administrative Data+Customer Data"
    Environment = "BTS"
    servertype = "udp"
    supportgroup = "WAP"
  }
  versioning = {
    enabled = false
    mfa_delete = false
  }
  
}
output "s3_bucket_arn" {
  value = module.infra.s3_bucket_arn
}
output "s3_bucket_bucket_domain_name" {
  value = module.infra.s3_bucket_bucket_domain_name
}
output "s3_bucket_bucket_regional_domain_name" {
  value = module.infra.s3_bucket_bucket_regional_domain_name
}
output "s3_bucket_hosted_zone_id" {
  value = module.infra.s3_bucket_hosted_zone_id
}
output "s3_bucket_id" {
  value = module.infra.s3_bucket_id
}
output "s3_bucket_region" {
  value = module.infra.s3_bucket_region
}
output "s3_bucket_website_domain" {
  value = module.infra.s3_bucket_website_domain
}
output "s3_bucket_website_endpoint" {
  value = module.infra.s3_bucket_website_endpoint
}


