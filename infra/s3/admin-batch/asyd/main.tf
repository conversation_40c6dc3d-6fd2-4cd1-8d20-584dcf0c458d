# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.19
# template-version: 7.18.47
# module-version: v10.7.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

provider "aws" {
  access_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = "ap-southeast-2"
  use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/s3/admin-batch/asyd.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/s3/admin-batch/asyd.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/s3?ref=v10.7.4"
  providers = {
    aws = aws
  }
  attach_policy = true
  aws_infra_region = "ap-southeast-2"
  bucket = "adminbatch-asyd"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  policy = <<-EOF
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Sid": "Stmt1520534759616",
        "Effect": "Deny",
        "Principal": "*",
        "Action": "s3:*",
        "Resource": "arn:aws:s3:::adminbatch-asyd",
        "Condition": {
          "Bool": {
            "aws:SecureTransport": "false"
          }
        }
      },
      {
        "Sid": "DenyIncorrectEncryptionHeader",
        "Effect": "Deny",
        "Principal": "*",
        "Action": "s3:PutObject",
        "Resource": "arn:aws:s3:::adminbatch-asyd/*",
        "Condition": {
          "StringNotEquals": {
            "s3:x-amz-server-side-encryption": "AES256"
          }
        }
      },
      {
        "Sid": "DenyUnEncryptedObjectUploads",
        "Effect": "Deny",
        "Principal": "*",
        "Action": "s3:PutObject",
        "Resource": "arn:aws:s3:::adminbatch-asyd/*",
        "Condition": {
          "Null": {
            "s3:x-amz-server-side-encryption": "true"
          }
        }
      },
      {
        "Sid": "AllowPutobjectUploadsPRD",
        "Effect": "Allow",
        "Principal": {
          "AWS": "arn:aws:iam::527856644868:user/admin-batch-asyd-user"
        },
        "Action": [
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:DeleteObject",
          "s3:PutObjectTagging",
          "s3:PutObjectVersionTagging",
          "s3:GetObjectVersion",
          "s3:GetObject",
          "s3:GetObjectAcl"
        ],
        "Resource": "arn:aws:s3:::adminbatch-asyd/*"
      }
    ]
  }
  
  EOF
  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
  tags = {
    DataTaxonomy = "Administrative Data+Customer Data"
    Environment = "BTS"
    servertype = "udp"
    supportgroup = "WAP"
  }
  
}
output "s3_bucket_arn" {
  value = module.infra.s3_bucket_arn
}
output "s3_bucket_bucket_domain_name" {
  value = module.infra.s3_bucket_bucket_domain_name
}
output "s3_bucket_bucket_regional_domain_name" {
  value = module.infra.s3_bucket_bucket_regional_domain_name
}
output "s3_bucket_hosted_zone_id" {
  value = module.infra.s3_bucket_hosted_zone_id
}
output "s3_bucket_id" {
  value = module.infra.s3_bucket_id
}
output "s3_bucket_region" {
  value = module.infra.s3_bucket_region
}
output "s3_bucket_website_domain" {
  value = module.infra.s3_bucket_website_domain
}
output "s3_bucket_website_endpoint" {
  value = module.infra.s3_bucket_website_endpoint
}

