# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v10.16.19
# template-version: 7.18.47
# module-version: v10.8.0
# wbx3-infra-version: <nil>
# resource: s3/ccg/ccg-prod-eu
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    aws_infra_region: eu-central-1
    bucket: calling-compliance-gateway-prod-eu
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    lifecycle_rule:
        - enabled: true
          expiration:
            days: 30
          noncurrent_version_expiration:
            days: 30
    server_side_encryption_configuration:
        rule:
            apply_server_side_encryption_by_default:
                sse_algorithm: AES256
    tags:
        ApplicationName: Webex Teams
        CiscoMailAlias: <EMAIL>
        DataClassification: Cisco Highly Confidential
        DataTaxonomy: Cisco Operations Data
        Environment: PROD
        ResourceOwner: Cisco Cloud Engineering
        supportgroup: wx-teams-platform
        webexservice: calling-compliance-gateway
env_name: sg-01a-ha1
infractl_version: v10.16.19
module_path: modules/aws/s3
module_version: v10.8.0
name: s3/ccg/ccg-prod-eu
terraform_version: v1.5.0

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cloud_service_provider: aws
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: p3.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/s3/ccg/ccg-prod-eu
    name: s3/ccg/ccg-prod-eu
    workflow: standard
dir: infra
external: false
include_defaults: true
manifest_path: manifests/s3/calling-compliance-gateway/manifest.yaml
s3_bucket_path: terraform-state/infra/s3/ccg/ccg-prod-eu.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.47
    templateversion: 7.18.47
workflow: standard