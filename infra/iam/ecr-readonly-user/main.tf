# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.10.8
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/iam/ecr-readonly-user.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/iam?ref=v7.18.1"
  aws_infra_region = "us-east-2"
  command_and_control = "mccprod"
  env_name = "a-useast-1-gen"
  iam_policies = {
    ecr_readonly = {
      Statement = [
        {
          Action = [
            "ecr:BatchCheckLayerAvailability",
            "ecr:BatchGetImage",
            "ecr:DescribeImage",
            "ecr:DescribeRepositories",
            "ecr:GetAuthorizationToken",
            "ecr:GetDownloadUrlForLayer",
            "ecr:ListImages"
          ]
          Effect = "Allow"
          Resource = "*"
        }
      ]
      Version = "2012-10-17"
    }
    s3_readonly = {
      Statement = [
        {
          Action = [
            "s3:GetObject",
            "s3:GetObjectAttributes",
            "s3:ListBucket"
          ]
          Effect = "Allow"
          Resource = "*"
        }
      ]
      Version = "2012-10-17"
    }
  }
  infra_credentials_path = "secret/data/mccprod/infra/route53/credentials"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "ecr-readonly-user"
  vault_path = "secret/mccprod/infra/ecr-readonly/credentials"
  
}
