# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.0
# module-version: v10.7.4
# wbx3-infra-version: <nil>
# resource: file-brackets-user-usea1
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    aws_infra_region: us-east-1
    iam_policies:
        file-brackets-policy-usea1:
            Statement:
                - Action:
                    - s3:ListBucketVersions
                    - s3:ListBucket
                    - s3:GetBucketVersioning
                    - s3:PutObject
                    - s3:GetObject
                    - s3:DeleteObjectVersion
                    - s3:GetObjectVersion
                    - s3:GetObjectTagging
                    - s3:PutObjectTagging
                    - s3:AbortMultipartUpload
                    - s3:DeleteObject
                    - s3:GetBucketLocation
                    - s3:GetObjectMetadata
                    - s3:GeneratePresignedUrl
                  Effect: Allow
                  Resource:
                    - arn:aws:s3:::file-brackets-s3-usea1
                    - arn:aws:s3:::file-brackets-s3-usea1/*
            Version: "2012-10-17"
    iam_user_name: file-brackets-user-usea1
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    vault_path: secret/mccprod/infra/iam-users
env_name: a-usea1-i0-0
infractl_version: v7.19.0
module_path: modules/aws/wxt/iam
module_version: v10.7.4
name: file-brackets-user-usea1
terraform_version: v1.5.0

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cloud_service_provider: aws
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: a00.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/iam/file-brackets-user-usea1
    name: file-brackets-user-usea1
    workflow: standard
dir: infra/iam
external: false
include_defaults: true
include_environment: true
manifest_path: manifests/s3/file-brackets/manifest.yaml
s3_bucket_path: terraform-state/infra/iam/file-brackets-user-usea1.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.19.0
    templateversion: 7.19.0
workflow: standard