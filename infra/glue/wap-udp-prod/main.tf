# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.17
# template-version: v7.19.8-next
# module-version: v9.4.5
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/glue/wap-udp-prod.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/glue?ref=v9.4.5"
  aws_infra_region = "us-east-1"
  glue = {
    databases = {
      alertcenter = {
        description = "wap-udp-alertcenter-prod-useast1 db"
        location = "s3://wap-udp-alertcenter-prod-useast1"
        name = "wap_udp_alertcenter_prod_useast1"
      }
      atlas = {
        description = "wap_udp_atlas_prod_useast1 db"
        location = "s3://wap-udp-atlas-prod-useast1"
        name = "wap_udp_atlas_prod_useast1"
      }
      calling = {
        description = "wap_udp_calling_prod_useast1 db"
        location = "s3://wap-udp-calling-prod-useast1"
        name = "wap_udp_calling_prod_useast1"
      }
      cdf = {
        description = "wap_udp_cdf_prod_useast1 glue db"
        location = "s3://wap-udp-cdf-prod-useast1"
        name = "wap_udp_cdf_prod_useast1"
      }
      ci = {
        description = "wap_udp_ci_prod_useast1 db"
        location = "s3://wap-udp-ci-prod-useast1"
        name = "wap_udp_ci_prod_useast1"
      }
      cus_dashboard = {
        description = "wap-udp-cus-dashboard-prod-useast1 db"
        location = "s3://wap-udp-cus-dashboard-prod-useast1"
        name = "wap_udp_cus_dashboard_prod_useast1"
      }
      datahub = {
        description = "wap-udp-datahub db"
        location = "s3://wap-udp-datahub-prod-useast1"
        name = "wap_udp_datahub_prod_useast1"
      }
      datascience = {
        description = "wap-udp-datascience db"
        location = "s3://wap-udp-datascience-prod-useast1"
        name = "wap_udp_datascience_prod_useast1"
      }
      datawizards = {
        description = "wap-udp-datawizards db"
        location = "s3://wap-udp-datawizards-prod-useast1"
        name = "wap_udp_datawizards_prod_useast1"
      }
      device = {
        description = "wap-udp-device db"
        location = "s3://wap-udp-device-prod-useast1"
        name = "wap_udp_device_prod_useast1"
      }
      diag = {
        description = "wap-udp-diag db"
        location = "s3://wap-udp-diag-prod-useast1"
        name = "wap_udp_diag_prod_useast1"
      }
      jabber = {
        description = "wap-udp-jabber db"
        location = "s3://wap-udp-jabber-prod-useast1"
        name = "wap_udp_jabber_prod_useast1"
      }
      maf = {
        description = "wap_udp_maf_prod_useast1 db"
        location = "s3://wap-udp-maf-prod-useast1"
        name = "wap_udp_maf_prod_useast1"
      }
      mediation_prod = {
        description = "wap-udp-mediation-prod db"
        location = "s3://wap-udp-mediation-prod-useast1"
        name = "wap_udp_mediation_prod_useast1"
      }
      mediation_pstn = {
        description = "wap-udp-mediation_pstn db"
        location = "s3://wap-udp-mediation-pstn-prod-useast1"
        name = "wap_udp_mediation_pstn_prod_useast1"
      }
      meeting = {
        description = "wap_udp_meeting_prod_useast1 db"
        location = "s3://wap-udp-meeting-prod-useast1"
        name = "wap_udp_meeting_prod_useast1"
      }
      partnerhub = {
        description = "wap_udp_partnerhub_prod_useast1 db"
        location = "s3://wap-udp-partnerhub-prod-useast1"
        name = "wap_udp_partnerhub_prod_useast1"
      }
      roma = {
        description = "wap_udp_roma_prod_useast1 db"
        location = "s3://wap-udp-roma-prod-useast1"
        name = "wap_udp_roma_prod_useast1"
      }
      testSnowFlake = {
        description = "wap-udpsntest-prod-ue1 db"
        location = "s3://wap-udpsntest-prod-ue1"
        name = "wap_udpsntest_prod_useast1"
      }
      videomesh = {
        description = "wap_udp_videomesh_prod_useast1 db"
        location = "s3://wap-udp-videomesh-prod-useast1"
        name = "wap_udp_videomesh_prod_useast1"
      }
      videomesh1 = {
        description = "wap_udp_videomesh_prod_as2 db"
        location = "s3://wap-udp-videomesh-prod-as2"
        name = "wap_udp_videomesh_prod_as2"
      }
      wme_meeting = {
        description = "wap_udp_wme_meeting_prod_useast1 db"
        location = "s3://wap-udp-wme-meeting-prod-useast1"
        name = "wap_udp_wme_meeting_prod_useast1"
      }
      wxcall = {
        description = "wap-udp-wxcall db"
        location = "s3://wap-udp-wxcall-prod-useast1"
        name = "wap_udp_wxcall_prod_useast1"
      }
      wxcc = {
        description = "wap_udp_wxcc_prod_useast1 db"
        location = "s3://wap-udp-wxcc-prod-useast1"
        name = "wap_udp_wxcc_prod_useast1"
      }
      wxcc_telephone = {
        description = "wap_udp_wxcc_telephone_prod_useast1 db"
        location = "s3://wap-udp-wxcc-telephone-prod-useast1"
        name = "wap_udp_wxcc_telephone_prod_useast1"
      }
    }
  }
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  
}


