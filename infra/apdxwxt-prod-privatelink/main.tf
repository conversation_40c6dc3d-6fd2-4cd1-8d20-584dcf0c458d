# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: 7.17.2
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/apdxwxt-prod-privatelink.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/privatelink_aws_kafka?ref=v8.1.1"
  aws_infra_region = "us-west-2"
  bootstrap = "lkc-nwv1rd-p212lg.us-west-2.aws.glb.confluent.cloud:9092"
  confluent_azs = [
    "usw2-az1",
    "usw2-az2",
    "usw2-az3"
  ]
  name = "apdxwxt-prod-privatelink"
  privatelink_service_name = "com.amazonaws.vpce.us-west-2.vpce-svc-0efb273f05a60a3d3"
  subnet_tags = {
    Tier = "public"
  }
  vpc_name = "apdxwxt-prod"
  
}

output "subnets_to_privatelink" {
  value = module.infra.subnets_to_privatelink
}
