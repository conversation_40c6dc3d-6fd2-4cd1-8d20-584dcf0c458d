# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v10.13.4
# wbx3-infra-version: <nil>
# resource: sjc03-vlan695-rr
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
availability_zones:
    - az1
control_network: provider-432
create_dns_record: true
enable_block_storage: true
env_name: w-sjc03-i0-0
flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
image_name: wbx3-capi-jammy-1.31.5-c8d-amd64-v1.29.6
infractl_version: v7.18.36
iso_network: workload_Isolated_net_vlan695
iso_network_ips:
    az1:
        - ***********
        - ***********
module_path: modules/openstack/route-reflector
module_version: v10.13.4
name: sjc03-vlan695-rr
network_mission_tag: meetings
network_name: w-sjc03-i0-0-a1
provisioning_module_version: v5.13.5
replicas: 2
route_reflector:
    asn: 65001
    availability_zones:
        - az1
    neighbors:
        aci:
            attributes_unchanged:
                - next-hop
            maximum_prefixes: 1000
            neighbors:
                - increment: 2
                  subnet_type: iso_network
                - increment: 3
                  subnet_type: iso_network
            prefix_lists:
                - allow_in: true
                  allow_out: true
                  name: kubed_isolated_cidrs
            remote_as: 65000
        k8s-node:
            listen_range:
                subnet_type: iso_network
            maximum_prefixes: 1000
            prefix_lists:
                - allow_in: true
                  allow_out: true
                  name: kubed_isolated_cidrs
            remote_as: 65001
            route_reflector_client: true
    prefix_lists:
        f5:
            prefixes:
                - cidr: ***********/23
                  seq: 5
                  type: permit
        kubed_isolated_cidrs:
            prefixes:
                - cidr: **********/12
                  le: 27
                  seq: 5
                  type: permit

#~~ No Blueprint Values


#~~ Environment Values
address_pools:
    pods_0: **********/15
    pods_1: **********/13
    pods_2: **********/12
    pods_3: **********/14
admin_port: 6442
aws_infra_az: <replace-me>
backend: s3
base_image: <replace-me>
base_k8s_image: <replace-me>
bastion_count: 1
bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
cloud_provider: openstack
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: not-used
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
infra_credentials_path: secret/data/mccprod/infra/wsjc03-ocp4-int/openstack
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
ingress_count: 3
ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_int_count: 2
ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
location: SJC
master_count: 3
master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
ocp_ownership_business_service: WBX3 Platform - Meetings
ocp_ownership_component: wxkb
ocp_ownership_server_type: wxkbsvr
ocp_ownership_support_group: wbx3-prod
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
server_group_policies:
    - anti-affinity
terraform_version: v1.10.4
thousand_eyes_count: 0
use_floating_ips: false
use_provider_template: true
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/sjc03-vlan695-rr
    name: sjc03-vlan695-rr
    workflow: standard
dir: infra
external: false
include_defaults: true
manifest_path: manifests/w-sjc03-i0-0/w-sjc03-i0-0-a1/infra/manifest.yaml
s3_bucket_path: terraform-state/infra/sjc03-vlan695-rr.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
type: route-reflector
versioningData:
    infractlversion: 7.18.36
    templateversion: 7.18.36
workflow: standard