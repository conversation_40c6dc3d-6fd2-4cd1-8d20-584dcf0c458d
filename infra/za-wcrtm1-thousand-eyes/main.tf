# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.22
# template-version: v7.19.8-next
# module-version: v10.13.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/za-wcrtm1-thousand-eyes.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/za-wcrtm1-thousand-eyes.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/eni?ref=v10.13.4"
  aws_infra_region = "af-south-1"
  eni_pools = {
    thousand-eyes-access-media-prv = {
      availability_zones = [
        "af-south-1a",
        "af-south-1b",
        "af-south-1c"
      ]
      dns_prefix = "te-access-prv"
      dns_zone = "p1.prod.infra.webex.com"
      eip = {
        enabled = false
      }
      instances_per_az = 1
      is_public = false
      security_group_names = [
        "thousand-eyes",
        "sse_media",
        "mse_media"
      ]
      subnet_tags = {
        webex_purpose = "access-trusted-media"
        webex_reachability = "private-provider"
      }
    }
  }
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  security_groups = {
    mse_media = [
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "all-egress"
        protocol = "all"
        to = 0
        type = "egress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 9443
        name = "mse-grpc-tcp-1"
        protocol = "tcp"
        to = 9443
        type = "ingress"
      },
      {
        cidr = "**********/12"
        from = 9443
        name = "mse-grpc-tcp-2"
        protocol = "tcp"
        to = 9443
        type = "ingress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 19560
        name = "mse-media-rtp-udp-media-1"
        protocol = "udp"
        to = 65535
        type = "ingress"
      },
      {
        cidr = "**********/12"
        from = 19560
        name = "mse-media-rtp-udp-media-2"
        protocol = "udp"
        to = 65535
        type = "ingress"
      }
    ]
    sse_media = [
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "all-egress"
        protocol = "all"
        to = 0
        type = "egress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 5060
        name = "sse-sip-internal-udp-1"
        protocol = "udp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "**********/12"
        from = 5060
        name = "sse-sip-internal-udp-2"
        protocol = "udp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 5060
        name = "sse-sip-internal-tcp-1"
        protocol = "tcp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "**********/12"
        from = 5060
        name = "sse-sip-internal-tcp-2"
        protocol = "tcp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 3000
        name = "sse-lb-udp"
        protocol = "udp"
        to = 5000
        type = "ingress"
      },
      {
        cidr = "10.0.0.0/8"
        from = 3000
        name = "sse-lb-tcp"
        protocol = "tcp"
        to = 5000
        type = "ingress"
      }
    ]
    thousand-eyes = [
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "thousand-eyes-icmp-echo-reply"
        protocol = "ICMP"
        to = 0
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 8
        name = "thousand-eyes-icmp-echo"
        protocol = "ICMP"
        to = 8
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 3
        name = "thousand-eyes-icmp-unreachable"
        protocol = "ICMP"
        to = 3
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 11
        name = "thousand-eyes-icmp-time-exceeded"
        protocol = "ICMP"
        to = 11
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 49153
        name = "thousand-eyes-a2a-tcp"
        protocol = "TCP"
        to = 49153
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 49153
        name = "thousand-eyes-a2a-udp"
        protocol = "UDP"
        to = 49153
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 443
        name = "thousand-eyes-https"
        protocol = "TCP"
        to = 443
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "allow-all-egress"
        protocol = "all"
        to = 0
        type = "egress"
      }
    ]
  }
  vpc_name = "p1_cal1"
  
}
output "network_interfaces" {
  value = module.infra.network_interfaces
}

