# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: v7.18.48
# module-version: dnssec-nlb
# wbx3-infra-version: <nil>
# resource: a00-dnssec-hosted-zone
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    cloud: mccprod
    domain: ite.webexgov.us
    parent_domain_role_credentials_path: secret/data/mccprod/infra/route/webexgov-route53-updater-role
    zone_name: a00
env_name: a-usea1-i0-0
module_commit: 4fa11cb3df6aecbebf07f6f235e3961f7ba86871
module_path: modules/aws/route53_hosted_zone
module_version: dnssec-nlb
name: a00-dnssec-hosted-zone

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cloud_service_provider: aws
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: a00.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.33
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.3
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/a00-dnssec-hosted-zone
    name: a00-dnssec-hosted-zone
    workflow: standard
dir: infra
external: false
include_defaults: true
include_environment: true
manifest_path: manifests/a-usea1-i0-0/a-usea1-i0-0-ha2/load-balancers/manifest.yaml
s3_bucket_path: terraform-state/infra/a00-dnssec-hosted-zone.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: v7.18.48
    templateversion: v7.18.48
workflow: standard