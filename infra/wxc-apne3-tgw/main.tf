# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.17
# template-version: 7.18.17
# module-version: tgw-peering
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/wxc-apne3-tgw.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/wxc-apne3-tgw.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/tgw_peering?ref=09a507015d30d5bcc5b96b111ef294f93c6d7193"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  name = "wxc-apne3-tgw"
  peer_tgw_filters = [
    {
      name = "transit-gateway-id"
      values = [
        "tgw-0a286233c3c65389b"
      ]
    }
  ]
  peer_tgw_owner_id = "423400439627"
  peer_tgw_region = "ap-northeast-1"
  routes = [
    "10.192.126.0/25",
    "10.192.126.128/25",
    "10.192.127.0/25"
  ]
  source_tgw_filters = [
    {
      name = "transit-gateway-id"
      values = [
        "tgw-03759c75a1d1a7fdc"
      ]
    }
  ]
  source_tgw_owner_id = "527856644868"
  source_tgw_region = "ap-northeast-3"
  subnet_tags = {
    webex_purpose = "access-untrusted-media"
  }
  tgw_route_table = [
    {
      name = "tag:Name"
      values = [
        "core_rtm2_b21_tgw_rt"
      ]
    }
  ]
  vpc = "b21_rtm2"
  
}

