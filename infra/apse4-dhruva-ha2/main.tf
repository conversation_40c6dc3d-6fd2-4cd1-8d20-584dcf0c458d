# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.22
# template-version: 7.18.22
# module-version: master
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/apse4-dhruva-ha2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/apse4-dhruva-ha2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/eni?ref=9942427a0a0c4549fc0d03ccc66b10ba6f1ef966"
  aws_infra_region = "ap-southeast-4"
  eni_pools = {
    wxc-dhruva-antares-mno-provider = {
      availability_zones = [
        "ap-southeast-4a",
        "ap-southeast-4b",
        "ap-southeast-4c"
      ]
      dns_prefix = "wxc-dhruva-antares-mno$i.provider"
      dns_zone = "a5.prod.infra.webex.com"
      eip = {
        create = false
        enabled = false
      }
      instances_per_az = 2
      security_group_names = [
        "open_egress",
        "antares_media"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "private-provider"
      }
    }
    wxc-dhruva-antares-mno-public = {
      availability_zones = [
        "ap-southeast-4a",
        "ap-southeast-4b",
        "ap-southeast-4c"
      ]
      dns_prefix = "wxc-dhruva-antares-mno$i.public"
      dns_zone = "a5.prod.infra.webex.com"
      eip = {
        create = false
        enabled = false
      }
      instances_per_az = 2
      is_public = true
      security_group_names = [
        "open_egress",
        "antares_ext"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "public-provider"
      }
    }
    wxc-dhruva-antares-pstn-provider = {
      availability_zones = [
        "ap-southeast-4a",
        "ap-southeast-4b",
        "ap-southeast-4c"
      ]
      dns_prefix = "wxc-dhruva-antares-pstn$i.provider"
      dns_zone = "a5.prod.infra.webex.com"
      eip = {
        create = false
        enabled = false
      }
      instances_per_az = 2
      security_group_names = [
        "open_egress",
        "antares_media"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "private-provider"
      }
    }
    wxc-dhruva-antares-pstn-public = {
      availability_zones = [
        "ap-southeast-4a",
        "ap-southeast-4b",
        "ap-southeast-4c"
      ]
      dns_prefix = "wxc-dhruva-antares-pstn$i.public"
      dns_zone = "a5.prod.infra.webex.com"
      eip = {
        create = false
        enabled = false
      }
      instances_per_az = 2
      is_public = true
      security_group_names = [
        "open_egress",
        "antares_ext"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "public-provider"
      }
    }
    wxc-dhruva-proxy-mno-0-provider = {
      availability_zones = [
        "ap-southeast-4a",
        "ap-southeast-4b",
        "ap-southeast-4c"
      ]
      dns_prefix = "wxc-dhruva-proxy-mno.provider"
      dns_zone = "a5.prod.infra.webex.com"
      eip = {
        create = false
        enabled = false
      }
      fixed_ips = [
        {
          ip = "*************"
        },
        {
          ip = "*************"
        },
        {
          ip = "*************"
        }
      ]
      instances_per_az = 1
      security_group_names = [
        "open_egress",
        "dhruva_media"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "private-provider"
      }
    }
    wxc-dhruva-proxy-mno-0-public = {
      availability_zones = [
        "ap-southeast-4a",
        "ap-southeast-4b",
        "ap-southeast-4c"
      ]
      dns_prefix = "wxc-dhruva-proxy-mno.public"
      dns_zone = "a5.prod.infra.webex.com"
      eip = {
        create = false
        enabled = false
      }
      fixed_ips = [
        {
          ip = "**************"
        },
        {
          ip = "**************"
        },
        {
          ip = "**************"
        }
      ]
      instances_per_az = 1
      is_public = true
      security_group_names = [
        "open_egress",
        "dhruva_ext"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "public-provider"
      }
    }
    wxc-dhruva-proxy-pstn-0-provider = {
      availability_zones = [
        "ap-southeast-4a",
        "ap-southeast-4b",
        "ap-southeast-4c"
      ]
      dns_prefix = "wxc-dhruva-proxy-pstn.provider"
      dns_zone = "a5.prod.infra.webex.com"
      eip = {
        create = false
        enabled = false
      }
      fixed_ips = [
        {
          ip = "*************"
        },
        {
          ip = "*************"
        },
        {
          ip = "*************"
        }
      ]
      instances_per_az = 1
      security_group_names = [
        "open_egress",
        "dhruva_media"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "private-provider"
      }
    }
    wxc-dhruva-proxy-pstn-0-public = {
      availability_zones = [
        "ap-southeast-4a",
        "ap-southeast-4b",
        "ap-southeast-4c"
      ]
      dns_prefix = "wxc-dhruva-proxy-pstn.public"
      dns_zone = "a5.prod.infra.webex.com"
      eip = {
        create = false
        enabled = false
      }
      fixed_ips = [
        {
          ip = "**************"
        },
        {
          ip = "**************"
        },
        {
          ip = "**************"
        }
      ]
      instances_per_az = 1
      is_public = true
      security_group_names = [
        "open_egress",
        "dhruva_ext"
      ]
      subnet_tags = {
        webex_purpose = "trunk-media"
        webex_reachability = "public-provider"
      }
    }
  }
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  security_groups = {
    antares_ext = [
      {
        cidr = "0.0.0.0/0"
        from = 5060
        name = "wxc-dhruva-antares-udp-1"
        protocol = "udp"
        to = 5061
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 19560
        name = "wxc-dhruva-antares-udp-2"
        protocol = "udp"
        to = 65535
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 5060
        name = "wxc-dhruva-antares-tcp-2"
        protocol = "tcp"
        to = 5061
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 19560
        name = "wxc-dhruva-antares-tcp-2"
        protocol = "tcp"
        to = 65535
        type = "ingress"
      }
    ]
    antares_media = [
      {
        cidr = "0.0.0.0/0"
        from = 5060
        name = "wxc-dhruva-antares-udp-1"
        protocol = "udp"
        to = 5061
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 19560
        name = "wxc-dhruva-antares-udp-2"
        protocol = "udp"
        to = 65535
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 5060
        name = "wxc-dhruva-antares-tcp-1"
        protocol = "tcp"
        to = 5061
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 19560
        name = "wxc-dhruva-antares-tcp-2"
        protocol = "tcp"
        to = 65535
        type = "ingress"
      }
    ]
    dhruva_ext = [
      {
        cidr = "0.0.0.0/0"
        from = 5060
        name = "wxc-dhruva-proxy-udp"
        protocol = "udp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 5060
        name = "wxc-dhruva-proxy-tcp"
        protocol = "tcp"
        to = 5061
        type = "ingress"
      }
    ]
    dhruva_media = [
      {
        cidr = "0.0.0.0/0"
        from = 5060
        name = "wxc-dhruva-proxy-udp"
        protocol = "udp"
        to = 5060
        type = "ingress"
      },
      {
        cidr = "0.0.0.0/0"
        from = 5060
        name = "wxc-dhruva-proxy-tcp"
        protocol = "tcp"
        to = 5061
        type = "ingress"
      }
    ]
    open_egress = [
      {
        cidr = "0.0.0.0/0"
        from = 0
        name = "all-egress"
        protocol = "all"
        to = 0
        type = "egress"
      }
    ]
  }
  vpc_name = "a5_cal2"
  
}

