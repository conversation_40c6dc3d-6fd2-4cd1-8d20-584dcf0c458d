# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.43
# template-version: 7.18.43
# module-version: v10.16.0
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/slido/cf/sidebar.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/slido/cf/sidebar.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/slido/cloudfront?ref=v10.16.0"
  aliases = [
    "slido-r.wbx2.com"
  ]
  default_cache_behavior_origin = "static-slido-lite-prod-us1"
  domain_name = "slido-r.wbx2.com"
  dynamic_origins = [
    {
      domain_name = "prod-aore-lite1-ds.wbx2.com"
      origin_id = "prod-aore-lite1-ds"
    }
  ]
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  ordered_cache_behaviors = [
    {
      origin_type = "static"
      path_pattern = "/static/*"
      target_origin_id = "static-slido-lite-prod-us1"
    },
    {
      origin_type = "dynamic"
      path_pattern = "/api/*"
      target_origin_id = "prod-aore-lite1-ds"
    },
    {
      origin_type = "dynamic"
      path_pattern = "/stream/*"
      target_origin_id = "prod-aore-lite1-ds"
    },
    {
      origin_type = "dynamic"
      path_pattern = "/compliance-api/*"
      target_origin_id = "prod-aore-lite1-ds"
    }
  ]
  resource_name = "slido_sidebar"
  s3_content_security_policy = "default-src 'self'; script-src 'self'; connect-src 'self'; style-src 'self' 'unsafe-inline'; frame-ancestors https://*.webex.com;"
  s3_origins = [
    {
      domain_name = "static-slido-lite-prod-us1.s3.amazonaws.com"
      origin_id = "static-slido-lite-prod-us1"
      origin_path = "/sidebar"
    }
  ]
  
}
output "cloudfront_distribution_arn" {
  value = module.infra.cloudfront_distribution_arn
}
output "cloudfront_distribution_domain_name" {
  value = module.infra.cloudfront_distribution_domain_name
}

