# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v10.13.2
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = "us-east-1"
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/wildcard-kms-role.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/wildcard-kms-role.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/iam_roles?ref="
  providers = {
    aws = aws
  }
  assume_role_policy_statements = [
    {
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        AWS = [
          "arn:aws:iam::527856644868:role/us-vaamtgb0-node.cluster-api",
          "arn:aws:iam::527856644868:role/us-oramtgp0-node.cluster-api",
          "arn:aws:iam::527856644868:role/us-vaamtgp0-node.cluster-api"
        ]
      }
      Sid = ""
    }
  ]
  iam_policies = [
    {
      Id = "default"
      Statement = [
        {
          Action = [
            "kms:Decrypt",
            "kms:Encrypt"
          ]
          Effect = "Allow"
          Resource = [
            "arn:aws:kms:*:*:key/mrk-52b5ec7563bc414aac8835cfd0dc9a4f"
          ]
          Sid = "VisualEditor0"
        }
      ]
      Version = "2012-10-17"
    }
  ]
  name = "wildcard-kms-role"
  
}
output "aws_iam_role" {
  value = module.infra.aws_iam_role
}

