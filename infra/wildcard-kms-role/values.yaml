# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v10.13.2
# wbx3-infra-version: <nil>
# resource: wildcard-kms-role
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    assume_role_policy_statements:
        - Action: sts:AssumeRole
          Effect: Allow
          Principal:
            AWS:
                - arn:aws:iam::527856644868:role/us-vaamtgb0-node.cluster-api
                - arn:aws:iam::527856644868:role/us-oramtgp0-node.cluster-api
                - arn:aws:iam::527856644868:role/us-vaamtgp0-node.cluster-api
          Sid: ""
    aws_infra_region: us-east-1
    iam_policies:
        - Id: default
          Statement:
            - Action:
                - kms:Decrypt
                - kms:Encrypt
              Effect: Allow
              Resource:
                - arn:aws:kms:*:*:key/mrk-52b5ec7563bc414aac8835cfd0dc9a4f
              Sid: VisualEditor0
          Version: "2012-10-17"
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    name: wildcard-kms-role
env_name: a-useast-1-mw
infractl_version: v7.18.36
module_commit: ""
module_path: modules/aws/iam_roles
module_version: v10.13.2
name: wildcard-kms-role
terraform_version: v1.5.7

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/wildcard-kms-role
    name: wildcard-kms-role
    workflow: standard
dir: infra
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/meeting-web/kms/manifest.yaml
s3_bucket_path: terraform-state/infra/wildcard-kms-role.tfstate
versioningData:
    infractlversion: 7.18.36
    templateversion: 7.18.36
workflow: standard