# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.20.2
# module-version: v10.16.7
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/d3p_s30-s3.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/d3p_s30-s3.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/network_vpc_endpoint?ref=v10.16.7"
  aws_infra_region = "eu-west-3"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "d3p_s30-s3"
  security_groups = {
    d3p_s30 = {
      rules = [
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          description = "Allow all egress to S3"
          from = 443
          name = "d3p_s30-s3"
          protocol = "tcp"
          to = 443
          type = "egress"
        },
        {
          cidrs = [
            "**********/16"
          ]
          description = "Allow all ingress from peer VPC"
          from = 443
          name = "d3p_s30-s3-ingress"
          protocol = "tcp"
          to = 443
          type = "ingress"
        }
      ]
    }
  }
  vpc_endpoint_list = [
    {
      security_groups = [
        "d3p_s30"
      ]
      service_name = "s3"
      vpc_endpoint_type = "Interface"
    }
  ]
  vpc_name = "d3p_s30"
  
}

