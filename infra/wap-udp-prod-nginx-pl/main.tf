# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.8
# template-version: EXPERIMENTAL
# module-version: v9.4.15
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/wap-udp-prod-nginx-pl.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/privatelink_endpoint?ref=v9.4.15"
  aws_infra_region = "us-east-1"
  endpoint_service_azs = [
    "use1-az1",
    "use1-az2",
    "use1-az4"
  ]
  endpoint_service_name = "com.amazonaws.vpce.us-east-1.vpce-svc-04d85fd6adbffbad8"
  ingress_rules = {
    http = {
      cidr_blocks = "100.64.0.0/14"
      from_port = 80
      protocol = "TCP"
      to_port = 80
    }
    https = {
      cidr_blocks = "100.64.0.0/14"
      from_port = 443
      protocol = "TCP"
      to_port = 443
    }
  }
  name = "wap-udp-pord-nginx-privatelink"
  private_dns_enabled = true
  subnet_tags = {
    Tier = "public"
  }
  vpc_name = "a-useast-1-wap-vpc-prod"
  
}

output "subnets_for_privatelink" {
  value = module.infra.subnets_for_privatelink
}
