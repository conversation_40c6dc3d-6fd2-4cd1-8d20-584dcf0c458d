# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.22
# template-version: 7.17.22
# module-version: v10.2.3
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/apdxwxt-wxp-int.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v10.2.3"
  command_and_control = "mccprod"
  destination_aws_infra_region = "us-west-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-int"
  destination_route_table_filter = [
    "*cluster*"
  ]
  destination_vpc_name = "aore-wxp-int-b"
  env_name = "apdxwxt-int-v2"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "apdxwxt-wxp-int"
  source_aws_infra_region = "us-west-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-int/aws"
  source_route_table_filter = [
    "*cluster*"
  ]
  source_vpc_name = "apdxwxt-int-v2"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

