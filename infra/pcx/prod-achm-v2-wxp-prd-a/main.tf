# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: 7.17.22
# module-version: v10.2.3
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/prod-achm-v2-wxp-prd-a.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v10.2.3"
  command_and_control = "mccprod"
  destination_aws_infra_region = "us-east-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_route_table_filter = [
    "*cluster*"
  ]
  destination_vpc_name = "achm-wxp-prod-a"
  env_name = "acmhwxt-prod-v2"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "prod-achm-v2-wxp-prd-a"
  source_aws_infra_region = "us-east-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_route_table_filter = [
    "*provider*"
  ]
  source_subnet_filter = [
    "provider*"
  ]
  source_vpc_name = "acmhwxt-prod-v2"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

