# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: no-release-EXPERIMENTAL-379b3cb
# module-version: v10.9.0
# wbx3-infra-version: <nil>
# resource: d30_app1-d31_app2
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    destination_aws_infra_region: eu-central-1
    destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    destination_route_table_filter:
        - '*workload_iso*'
    destination_subnet_filter:
        - '*workload_iso*'
    destination_vpc_name: d31_app2
    name: d30_app1-d31_app2
    source_aws_infra_region: eu-central-1
    source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    source_route_table_filter:
        - '*workload_iso*'
    source_subnet_filter:
        - '*workload_iso*'
    source_vpc_name: d30_app1
env_name: a-euce1-p0-0
module_path: modules/aws/wxt/peering_connection
module_version: v10.9.0
name: d30_app1-d31_app2
terraform_version: v1.5.0

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.19.0
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/pcx/d30_app1-d31_app2
    name: d30_app1-d31_app2
    workflow: standard
dir: infra/pcx
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/a-euce1-p0-0/a-euce1-p0-0-ha1/infra/manifest.yaml
s3_bucket_path: terraform-state/infra/pcx/d30_app1-d31_app2.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: no-release-EXPERIMENTAL-379b3cb
    templateversion: no-release-EXPERIMENTAL-379b3cb
workflow: standard