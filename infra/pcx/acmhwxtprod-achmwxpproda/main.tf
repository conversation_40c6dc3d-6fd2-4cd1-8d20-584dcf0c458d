# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.6
# template-version: 7.18.2
# module-version: v10.2.3
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/acmhwxtprod-achmwxpproda.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v10.2.3"
  command_and_control = "mccprod"
  destination_aws_infra_region = "us-east-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_route_table_filter = [
    "*cluster*"
  ]
  destination_vpc_name = "achm-wxp-prod-a"
  env_name = "acmhwxt-prod"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "acmhwxtprod-achmwxpproda"
  security_groups = [
    {
      inbound = [
        {
          from = 19530
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-07f522ab33db00b1e"
          to = 19530
        }
      ]
      name = "milvusdb"
      outbound = [
        {
          from = 0
          name = "default"
          protocol = "TCP"
          target_sg_id = "sg-09af4763289a13984"
          to = 65535
        }
      ]
    }
  ]
  source_aws_infra_region = "us-east-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_route_table_filter = [
    "*cluster*"
  ]
  source_subnet_filter = [
    "cluster*"
  ]
  source_vpc_name = "acmhwxt-prod"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

