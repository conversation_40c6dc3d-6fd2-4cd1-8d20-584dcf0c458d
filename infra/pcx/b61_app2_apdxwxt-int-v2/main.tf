# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v10.11.13
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/route53/credentials
# infra-credentials-path: secret/data/mccprod/infra/mpe-aws-int/aws
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/b61_app2_apdxwxt-int-v2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/pcx/b61_app2_apdxwxt-int-v2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v10.11.13"
  command_and_control = "mccprod"
  destination_aws_infra_region = "us-west-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-int/aws"
  destination_route_table_filter = [
    "*cluster*"
  ]
  destination_subnet_filter = [
    "*cluster*"
  ]
  destination_vpc_name = "apdxwxt-int-v2"
  env_name = "a-uswe2-i0-1"
  full_peering = true
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "b61_app2_apdxwxt-int-v2"
  source_aws_infra_region = "us-west-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-int/aws"
  source_route_table_filter = [
    "*service_cld-igw*"
  ]
  source_subnet_filter = [
    "*service-igw*"
  ]
  source_vpc_name = "b61_app2"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

