# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/anrtwxt-prod-anrt-v2.tfstate"
    region = "us-east-2"
  }
}


module "infra" {

  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v8.2.3"

  destination_aws_infra_region = "ap-northeast-1"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_vpc_name = "anrt-vpc"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "anrtwxt-prod-anrt-v2"
  security_groups = [
    {
      inbound = [
        {
              from = 9042
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-0b8417bc85537cc83"
          to = 9042
  
        },
        {
              from = 9160
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-0b8417bc85537cc83"
          to = 9160
  
        },
        {
              from = 6379
          name = "elasticache"
          protocol = "TCP"
          target_sg_id = "sg-0b5f079e562573f5e"
          to = 6379
  
        },
        {
              from = 5432
          name = "postgres"
          protocol = "TCP"
          target_sg_id = "sg-0d318061fa8502ea0"
          to = 5432
  
        },
        {
              from = 9092
          name = "kafka"
          protocol = "TCP"
          target_sg_id = "sg-0c4c77956405b1865"
          to = 9094
  
        },
        {
              from = 5671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-04c481c1aba41ac17"
          to = 5672
  
        },
        {
              from = 25671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-04c481c1aba41ac17"
          to = 25672
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-0a2aaca3f11364801"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-0a2aaca3f11364801"
          to = 58500
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-0a2aaca3f11364801"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-0a2aaca3f11364801"
          to = 58500
  
        },
        {
              from = 20000
          name = "splatrunner"
          protocol = "all"
          target_sg_id = "sg-0d374cb9318686dd0"
          to = 65535
  
        },
        {
              from = 2181
          name = "zookeeper"
          protocol = "TCP"
          target_sg_id = "sg-00da3aa012dbd6765"
          to = 2181
  
        },
        {
              from = 9093
          name = "kafkaproxy"
          protocol = "TCP"
          target_sg_id = "sg-0df87752e603f736a"
          to = 9093
  
        }
      ]
      name = "prod-anrt-v2"
      outbound = [
        {
              from = 0
          name = "default"
          protocol = "all"
          target_sg_id = "sg-07e02095a01d155c2"
          to = 65535
  
        }
      ]
  
    }
  ]
  source_aws_infra_region = "ap-northeast-1"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "anrtwxt-prod-v2"
  
}
