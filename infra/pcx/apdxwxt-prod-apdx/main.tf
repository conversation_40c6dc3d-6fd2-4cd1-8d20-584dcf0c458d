# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.16.3
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/apdxwxt-prod-apdx.tfstate"
    region = "us-east-2"
  }
}


module "infra" {

  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v8.2.3"

  destination_aws_infra_region = "us-west-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_vpc_name = "apdx-vpc"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "apdxwxt-prod-apdx"
  security_groups = [
    {
      inbound = [
        {
              from = 9042
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-919db6e1"
          to = 9042
  
        },
        {
              from = 9160
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-919db6e1"
          to = 9160
  
        },
        {
              from = 6379
          name = "elasticache"
          protocol = "TCP"
          target_sg_id = "sg-0ed88dbe213fe9d21"
          to = 6379
  
        },
        {
              from = 5432
          name = "postgres"
          protocol = "TCP"
          target_sg_id = "sg-0d04a5236afb6d3b9"
          to = 5432
  
        },
        {
              from = 9092
          name = "kafka"
          protocol = "TCP"
          target_sg_id = "sg-0c3f8b9568cfaf552"
          to = 9094
  
        },
        {
              from = 9092
          name = "kafkaproxy"
          protocol = "TCP"
          target_sg_id = "sg-4582a935"
          to = 9094
  
        },
        {
              from = 5671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-8b419ef6"
          to = 5672
  
        },
        {
              from = 25671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-8b419ef6"
          to = 25672
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-03846efc50a630617"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-03846efc50a630617"
          to = 58500
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-03846efc50a630617"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-03846efc50a630617"
          to = 58500
  
        },
        {
              from = 20000
          name = "splatrunner"
          protocol = "all"
          target_sg_id = "sg-9d35eae0"
          to = 65535
  
        }
      ]
      name = "prod-apdx"
      outbound = [
        {
              from = 0
          name = "default"
          protocol = "all"
          target_sg_id = "sg-394e9d44"
          to = 65535
  
        }
      ]
  
    }
  ]
  source_aws_infra_region = "us-west-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "apdxwxt-prod"
  
}
