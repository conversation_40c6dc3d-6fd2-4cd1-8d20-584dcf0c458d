# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/afrawxt-prod-afra-v2.tfstate"
    region = "us-east-2"
  }
}


module "infra" {

  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v8.2.3"

  destination_aws_infra_region = "eu-central-1"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_vpc_name = "afra-vpc"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "afrawxt-prod-afra-v2"
  security_groups = [
    {
      inbound = [
        {
              from = 9042
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-0636f615d44b9a786"
          to = 9042
  
        },
        {
              from = 9160
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-0636f615d44b9a786"
          to = 9160
  
        },
        {
              from = 6379
          name = "elasticache"
          protocol = "TCP"
          target_sg_id = "sg-06be1d2106f5397cc"
          to = 6379
  
        },
        {
              from = 5432
          name = "postgres"
          protocol = "TCP"
          target_sg_id = "sg-0ea01c49448915317"
          to = 5432
  
        },
        {
              from = 9092
          name = "kafka"
          protocol = "TCP"
          target_sg_id = "sg-f1aca099"
          to = 9094
  
        },
        {
              from = 5671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-410f0029"
          to = 5672
  
        },
        {
              from = 25671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-410f0029"
          to = 25672
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-0cbac6977c44404dd"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-0cbac6977c44404dd"
          to = 58500
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-0cbac6977c44404dd"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-0cbac6977c44404dd"
          to = 58500
  
        },
        {
              from = 20000
          name = "splatrunner"
          protocol = "all"
          target_sg_id = "sg-24b13749"
          to = 65535
  
        },
        {
              from = 2181
          name = "zookeeper"
          protocol = "TCP"
          target_sg_id = "sg-f0aca098"
          to = 2181
  
        },
        {
              from = 9093
          name = "kafkaproxy"
          protocol = "TCP"
          target_sg_id = "sg-679d080b"
          to = 9093
  
        },
        {
              from = 2223
          name = "cloudhsm"
          protocol = "TCP"
          target_sg_id = "sg-08f2a4f558d6fadc5"
          to = 2225
  
        }
      ]
      name = "prod-afra"
      outbound = [
        {
              from = 0
          name = "default"
          protocol = "all"
          target_sg_id = "sg-b82a77d0"
          to = 65535
  
        }
      ]
  
    }
  ]
  source_aws_infra_region = "eu-central-1"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "afrawxt-prod-v2"
  
}
