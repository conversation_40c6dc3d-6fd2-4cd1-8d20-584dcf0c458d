# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.16.3
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/acmhwxt-prod-achm.tfstate"
    region = "us-east-2"
  }
}


module "infra" {

  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v8.2.3"

  destination_aws_infra_region = "us-east-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_vpc_name = "achm-vpc"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "acmhwxt-prod-achm"
  security_groups = [
    {
      inbound = [
        {
              from = 9042
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-6f6cec07"
          to = 9042
  
        },
        {
              from = 9160
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-6f6cec07"
          to = 9160
  
        }
      ]
      name = "cassandra"
      outbound = [
        {
              from = 0
          name = "cassandra"
          protocol = "all"
          target_sg_id = "sg-6f6cec07"
          to = 65535
  
        }
      ]
  
    },
    {
      inbound = [
        {
              from = 6379
          name = "elasticache"
          protocol = "TCP"
          target_sg_id = "sg-0334c5bad1eead816"
          to = 6379
  
        }
      ]
      name = "elasticache"
      outbound = [
        {
              from = 0
          name = "elasticache"
          protocol = "all"
          target_sg_id = "sg-0334c5bad1eead816"
          to = 65535
  
        }
      ]
  
    },
    {
      inbound = [
        {
              from = 5432
          name = "postgres"
          protocol = "TCP"
          target_sg_id = "sg-9a39baf2"
          to = 5432
  
        }
      ]
      name = "postgres"
      outbound = [
        {
              from = 0
          name = "postgres"
          protocol = "all"
          target_sg_id = "sg-9a39baf2"
          to = 65535
  
        }
      ]
  
    },
    {
      inbound = [
        {
              from = 9092
          name = "kafka"
          protocol = "TCP"
          target_sg_id = "sg-d6b109be"
          to = 9094
  
        }
      ]
      name = "kafka"
      outbound = [
        {
              from = 0
          name = "kafka"
          protocol = "all"
          target_sg_id = "sg-d6b109be"
          to = 65535
  
        }
      ]
  
    },
    {
      inbound = [
        {
              from = 5671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-a0a21ac8"
          to = 5672
  
        },
        {
              from = 25671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-a0a21ac8"
          to = 25672
  
        }
      ]
      name = "rabbitmq"
      outbound = [
        {
              from = 0
          name = "rabbitmq"
          protocol = "all"
          target_sg_id = "sg-a0a21ac8"
          to = 65535
  
        }
      ]
  
    },
    {
      inbound = [
        {
              from = 20000
          name = "splatrunner"
          protocol = "all"
          target_sg_id = "sg-58bd0530"
          to = 65535
  
        }
      ]
      name = "splatrunner"
      outbound = [
        {
              from = 8080
          name = "splatrunner"
          protocol = "TCP"
          target_sg_id = "sg-58bd0530"
          to = 8080
  
        }
      ]
  
    },
    {
      inbound = [
        {
              from = 443
          name = "elasticsearch"
          protocol = "TCP"
          target_sg_id = "sg-2cb30b44"
          to = 443
  
        }
      ]
      name = "elasticsearch"
      outbound = [
        {
              from = 0
          name = "elasticsearch"
          protocol = "all"
          target_sg_id = "sg-2cb30b44"
          to = 65535
  
        }
      ]
  
    },
    {
      inbound = [
        {
              from = 58300
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 58500
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 58500
  
        }
      ]
      name = "consul-data-service"
      outbound = [
        {
              from = 0
          name = "consul-data-service"
          protocol = "all"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 65535
  
        }
      ]
  
    },
    {
      inbound = [
        {
              from = 9042
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-6f6cec07"
          to = 9042
  
        },
        {
              from = 9160
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-6f6cec07"
          to = 9160
  
        },
        {
              from = 6379
          name = "elasticache"
          protocol = "TCP"
          target_sg_id = "sg-0334c5bad1eead816"
          to = 6379
  
        },
        {
              from = 5432
          name = "postgres"
          protocol = "TCP"
          target_sg_id = "sg-9a39baf2"
          to = 5432
  
        },
        {
              from = 9092
          name = "kafka"
          protocol = "TCP"
          target_sg_id = "sg-d6b109be"
          to = 9094
  
        },
        {
              from = 5671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-a0a21ac8"
          to = 5672
  
        },
        {
              from = 25671
          name = "rabbitmq"
          protocol = "TCP"
          target_sg_id = "sg-a0a21ac8"
          to = 25672
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "TCP"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 58500
  
        },
        {
              from = 58300
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 58302
  
        },
        {
              from = 58500
          name = "consul-data-service"
          protocol = "UDP"
          target_sg_id = "sg-01286a23c9a4a2579"
          to = 58500
  
        },
        {
              from = 20000
          name = "splatrunner"
          protocol = "all"
          target_sg_id = "sg-58bd0530"
          to = 65535
  
        },
        {
              from = 2181
          name = "zookeeper"
          protocol = "TCP"
          target_sg_id = "sg-c9be06a1"
          to = 2181
  
        },
        {
              from = 9093
          name = "kafkaproxy"
          protocol = "TCP"
          target_sg_id = "sg-fb465090"
          to = 9093
  
        },
        {
              from = 2223
          name = "cloudhsm"
          protocol = "TCP"
          target_sg_id = "sg-025e442db935879ea"
          to = 2225
  
        }
      ]
      name = "prod-achm"
      outbound = [
        {
              from = 0
          name = "default"
          protocol = "all"
          target_sg_id = "sg-a8f34ac0"
          to = 65535
  
        }
      ]
  
    }
  ]
  source_aws_infra_region = "us-east-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "acmhwxt-prod"
  
}
