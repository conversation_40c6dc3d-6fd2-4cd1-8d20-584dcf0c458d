# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: v7.19.8-next
# module-version: v10.9.0
# wbx3-infra-version: <nil>
# resource: b8p_pst0-asfo-vpc
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    destination_aws_infra_region: us-west-1
    destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
    destination_route_table_filter:
        - '*private*'
    destination_subnet_filter:
        - '*private*'
    destination_vpc_name: asfo-vpc
    name: b8p_pst0-asfo-vpc
    source_aws_infra_region: us-west-2
    source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    source_route_table_filter:
        - '*workload_iso*'
        - '*service_cld-igw*'
    source_subnet_filter:
        - '*workload_iso*'
        - '*service-igw*'
    source_vpc_name: b8p_pst0
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
env_name: a-uswe2-p0-2
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.36
module_path: modules/aws/wxt/peering_connection
module_version: v10.9.0
name: b8p_pst0-asfo-vpc
s3_bucket_path: terraform-state/infra/pcx/b8p_pst0-asfo-vpc.tfstate
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.0
use_provider_template: true

#~~ No Blueprint Values


#~~ Environment Values
consul_host: consul.int.mcc01.prod.infra.webex.com

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/pcx/b8p_pst0-asfo-vpc
    name: b8p_pst0-asfo-vpc
    workflow: standard
dir: infra/pcx
external: false
include_defaults: false
include_environment: false
manifest_path: manifest.yaml
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: v7.19.8-next
    templateversion: v7.19.8-next
workflow: standard