# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.1
# module-version: v10.16.4
# wbx3-infra-version: <nil>
# resource: c20_app1-c2p_pst1-pcx
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    destination_aws_infra_region: us-east-2
    destination_infra_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
    destination_route_table_filter:
        - '*workload_iso*'
        - '*service_cld*'
    destination_subnet_filter:
        - '*workload_iso*'
        - '*service_cld*'
    destination_vpc_name: c2p_pst1
    full_peering: true
    source_aws_infra_region: us-east-2
    source_infra_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
    source_route_table_filter:
        - '*workload_iso*'
        - '*service_cld*'
    source_subnet_filter:
        - '*workload_iso*'
        - '*service_cld*'
    source_vpc_name: c20_app1
env_name: a-usea2-i3-2
module_path: modules/aws/wxt/peering_connection
name: c20_app1-c2p_pst1-pcx

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cloud_service_provider: aws
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: c20.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.19.0
module_version: v10.16.4
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.8.5
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/pcx/c2p-preprod/c20_app1-c2p_pst1-pcx
    name: c20_app1-c2p_pst1-pcx
    workflow: standard
dir: infra/pcx/c2p-preprod
external: false
include_defaults: true
include_environment: true
manifest_path: manifests/a-usea2-i3-2/a-usea2-i3-2-ha1/network/manifest.yaml
s3_bucket_path: terraform-state/infra/pcx/c2p-preprod/c20_app1-c2p_pst1-pcx.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.19.1
    templateversion: 7.19.1
workflow: standard