# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.1
# module-version: v10.16.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/286806641839/atlantis-c2p-pre-prod
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/c2p-preprod/c30_app1-c1p_pst1-pcx.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/pcx/c2p-preprod/c30_app1-c1p_pst1-pcx.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v10.16.4"
  command_and_control = "mccprod"
  destination_aws_infra_region = "us-east-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/286806641839/atlantis-c2p-pre-prod"
  destination_route_table_filter = [
    "*workload_iso*",
    "*service_cld*"
  ]
  destination_subnet_filter = [
    "*workload_iso*",
    "*service_cld*"
  ]
  destination_vpc_name = "c1p_pst1"
  env_name = "a-uswe2-i3-0"
  full_peering = true
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "c30_app1-c1p_pst1-pcx"
  source_aws_infra_region = "us-west-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/286806641839/atlantis-c2p-pre-prod"
  source_route_table_filter = [
    "*workload_iso*",
    "*service_cld*"
  ]
  source_subnet_filter = [
    "*workload_iso*",
    "*service_cld*"
  ]
  source_vpc_name = "c30_app1"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

