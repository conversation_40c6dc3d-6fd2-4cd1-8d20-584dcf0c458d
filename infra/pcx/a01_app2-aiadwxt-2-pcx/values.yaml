# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: 7.18.36
# module-version: v10.9.0
# wbx3-infra-version: <nil>
# resource: a01_app2-aiadwxt-2-pcx
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    destination_aws_infra_region: us-east-1
    destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    destination_route_table_filter:
        - '*cluster*'
    destination_subnet_filter:
        - '*cluster*'
    destination_vpc_name: aiadwxt-int-v2
    name: a01_app2-aiadwxt-int-v2-pcx
    source_aws_infra_region: us-east-1
    source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    source_route_table_filter:
        - '*service_cld-igw*'
        - '*workload_iso*'
    source_subnet_filter:
        - '*service-igw*'
        - '*workload_iso*'
    source_vpc_name: a01_app2
env_name: a-usea1-i0-0
module_path: modules/aws/wxt/peering_connection
module_version: v10.9.0
name: a01_app2-aiadwxt-2-pcx
terraform_version: v1.5.0

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.33
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/pcx/a01_app2-aiadwxt-2-pcx
    name: a01_app2-aiadwxt-2-pcx
    workflow: standard
dir: infra/pcx
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/a-usea1-i0-0/a-usea1-i0-0-ha2/infra/manifest.yaml
s3_bucket_path: terraform-state/infra/pcx/a01_app2-aiadwxt-2-pcx.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.36
    templateversion: 7.18.36
workflow: standard