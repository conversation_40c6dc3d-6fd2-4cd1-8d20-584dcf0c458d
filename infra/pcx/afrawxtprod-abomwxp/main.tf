# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.6
# template-version: 7.18.2
# module-version: v8.2.3
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/afrawxtprod-abomwxp.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v8.2.3"
  command_and_control = "mccprod"
  destination_aws_infra_region = "ap-south-1"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_vpc_name = "abom-wxp-prod-a"
  env_name = "afrawxt-prod"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "afrawxtprod-abomwxp"
  security_groups = [
    {
      inbound = [
        {
          from = 443
          name = "elasticsearch"
          protocol = "TCP"
          target_sg_id = "sg-08aaace8479d22390"
          to = 443
        }
      ]
      name = "elasticsearch"
      outbound = [
        {
          from = 0
          name = "default"
          protocol = "all"
          target_sg_id = "sg-01552dd91cc9c1f61"
          to = 65535
        }
      ]
    }
  ]
  source_aws_infra_region = "eu-central-1"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "afrawxt-prod"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

