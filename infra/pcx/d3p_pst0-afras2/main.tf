# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: no-release-EXPERIMENTAL-379b3cb
# module-version: v10.9.0
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/d3p_pst0-afras2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/pcx/d3p_pst0-afras2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v10.9.0"
  command_and_control = "mccprod"
  destination_aws_infra_region = "eu-central-1"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_route_table_filter = [
    "*cluster*"
  ]
  destination_subnet_filter = [
    "*cluster*"
  ]
  destination_vpc_name = "afra-wxp-prod-a"
  env_name = "a-euce1-p0-0"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "d3p_pst0-afras2"
  source_aws_infra_region = "eu-central-1"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_route_table_filter = [
    "*workload_iso*",
    "*media_prv-direct*"
  ]
  source_subnet_filter = [
    "*workload_iso*",
    "*media_prv-direct*"
  ]
  source_vpc_name = "d3p_pst0"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

