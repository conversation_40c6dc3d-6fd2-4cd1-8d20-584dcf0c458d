# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.19
# template-version: 7.18.19
# module-version: v10.7.10
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/au-01a-ha2-asyd-pcx.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/infra/pcx/au-01a-ha2-asyd-pcx.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v10.7.10"
  command_and_control = "mccprod"
  destination_aws_infra_region = "ap-southeast-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_route_table_filter = [
    "*private*"
  ]
  destination_subnet_filter = [
    "*private*"
  ]
  destination_vpc_name = "asyd-vpc"
  env_name = "au-01a-ha2"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "au-01a-ha2-asyd-pcx"
  source_aws_infra_region = "ap-southeast-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_route_table_filter = [
    "*workload_iso*"
  ]
  source_subnet_filter = [
    "*workload_iso*"
  ]
  source_vpc_name = "a2_mtg1"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

