# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.15
# template-version: 7.17.15
# module-version: v8.6.6
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/aiadwxt-int-to-wap.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v8.6.6"
  command_and_control = "mccprod"
  destination_aws_infra_region = "us-east-1"
  destination_infra_credentials_path = "secret/mccprod/infra/mpe-aws-prod/aws"
  destination_route_table_filter = [
    "*provider*"
  ]
  destination_vpc_name = "a-useast-1-wap-vpc-prod"
  env_name = "a-useast-1-wap"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "pcx/aiadwxt-int-to-wap"
  source_aws_infra_region = "us-east-1"
  source_infra_credentials_path = "secret/mccprod/infra/mpe-aws-prod/aws"
  source_route_table_filter = [
    "*cluster*"
  ]
  source_vpc_name = "aiadwxt-int"
  
}
output "command_and_control" {
  value = module.infra.command_and_control
}
output "env_name" {
  value = module.infra.env_name
}
output "infra_service_domain" {
  value = module.infra.infra_service_domain
}

