# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/aorewxt-wxp-prod-a-v2.tfstate"
    region = "us-east-2"
  }
}


module "infra" {

  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v8.2.3"

  destination_aws_infra_region = "us-west-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_vpc_name = "aore-wxp-prod-a"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "aorewxt-wxp-prod-a-v2"
  security_groups = [
    {
      inbound = [
        {
              from = 9042
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-07b9e04b6353b91c8"
          to = 9042
  
        },
        {
              from = 9160
          name = "cassandra"
          protocol = "TCP"
          target_sg_id = "sg-07b9e04b6353b91c8"
          to = 9160
  
        }
      ]
      name = "prod-aore-wxp-prod-a-v2"
      outbound = [
        {
              from = 0
          name = "default"
          protocol = "all"
          target_sg_id = "sg-02ead3f1e01cf1f0f"
          to = 65535
  
        }
      ]
  
    }
  ]
  source_aws_infra_region = "us-west-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "aorewxt-prod-v2"
  
}
