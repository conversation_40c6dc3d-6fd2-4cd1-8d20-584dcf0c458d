# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.4
# template-version: EXPERIMENTAL
# module-version: v8.1.3
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/infra/pcx/awxc-prod-achm.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/wxt/peering_connection?ref=v8.1.3"
  command_and_control = "mccprod"
  destination_aws_infra_region = "us-east-2"
  destination_infra_credentials_path = "secret/data/mccprod/infra/pcx/splat-prod"
  destination_vpc_name = "achm-vpc"
  env_name = "a-useast-2-wxc"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  name = "awxc-prod-achm"
  security_groups = [
    {
      destination_sg_id = "sg-07e6ead60826d688f"
      inbound = [
        {
          from = 9200
          protocol = "TCP"
          to = 9200
        },
        {
          from = 9300
          protocol = "TCP"
          to = 9300
        },
        {
          from = 5601
          protocol = "TCP"
          to = 5601
        }
      ]
      name = "opensearch-shared"
      outbound = [
        {
          from = 0
          protocol = "all"
          to = 65535
        }
      ]
    },
    {
      destination_sg_id = "sg-06ce0a6cf2811233c"
      inbound = [
        {
          from = 3306
          protocol = "TCP"
          to = 3306
        }
      ]
      name = "mysqlrds-shared"
      outbound = [
        {
          from = 0
          protocol = "all"
          to = 65535
        }
      ]
    },
    {
      destination_sg_id = "sg-0334c5bad1eead816"
      inbound = [
        {
          from = 6379
          protocol = "all"
          to = 6379
        }
      ]
      name = "elasticache-shared"
      outbound = [
        {
          from = 0
          protocol = "all"
          to = 65535
        }
      ]
    },
    {
      destination_sg_id = "sg-a0a21ac8"
      inbound = [
        {
          from = 5671
          protocol = "all"
          to = 5671
        }
      ]
      name = "rabbitmq-shared"
      outbound = [
        {
          from = 0
          protocol = "all"
          to = 65535
        }
      ]
    }
  ]
  source_aws_infra_region = "us-east-2"
  source_infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  source_vpc_name = "a-wxc-useast2-prod"
  
}
