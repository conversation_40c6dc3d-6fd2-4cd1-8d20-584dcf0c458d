# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.19
# template-version: 7.18.47
# module-version: v10.16.21
# wbx3-infra-version: <nil>
# resource: admin-batch-asyd-group
# resource-type: infra
# -----------------------------------------------

#~~ Resource Values
args:
    group_name: adminbatchasyd
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    policy_arns:
        - arn:aws:iam::527856644868:policy/admin-batch-asyd-policy
env_name: au-01a-ha1
module_path: modules/aws/aws_iam_group
module_version: v10.16.21
name: admin-batch-asyd-group
terraform_version: v1.5.7

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.19
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra/admin-batch-asyd-group
    name: admin-batch-asyd-group
    workflow: standard
dir: infra
external: false
include_defaults: false
include_environment: false
manifest_path: manifests/iam/admin-batch/manifest.yaml
s3_bucket_path: terraform-state/infra/admin-batch-asyd-group.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.47
    templateversion: 7.18.47
workflow: standard