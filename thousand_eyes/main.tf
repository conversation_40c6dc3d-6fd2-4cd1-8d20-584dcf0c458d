terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key    = "terraform-state/thousand-eyes.tfstate"
    region = "us-east-2"
  }
}
terraform {
  required_providers {
    thousandeyes = {
      source  = "thousandeyes/thousandeyes"
      version = "1.3.1"
    }
  }
  # Terraform version
  required_version = ">= 0.13.0"
}


# List out each TE node here
variable "agents" {
  type        = set(string)
  description = "Set of WebexKubed thousand eyes nodes"
  default = [
    "thousand-eyes0.miadm-a-2.prod.infra.webex.com",
    "thousand-eyes0.mlhrm-a-2.prod.infra.webex.com",
    "thousand-eyes0.msinm-a-2.prod.infra.webex.com",
    "thousand-eyes0.msydm-a-2.prod.infra.webex.com",
    "thousand-eyes0.wbomm-a-1.prod.infra.webex.com",
    "thousand-eyes0.wdfwm-a-3.prod.infra.webex.com",
    "thousand-eyes0.wfram-a-1.prod.infra.webex.com",
    "thousand-eyes0.wfram-a-2.prod.infra.webex.com",
    "thousand-eyes0.wsinm-a-1.prod.infra.webex.com",
    #"thousand-eyes0.wsinwxc-p-2.prod.infra.webex.com",
    "thousand-eyes0.wsydm-a-2.prod.infra.webex.com",
    "thousand-eyes0.wsjcm-a-9.prod.infra.webex.com",
    "thousand-eyes0.wjfkm-a-13.prod.infra.webex.com",
    "thousand-eyes0.wjfkwxc-p-2.prod.infra.webex.com",
    "thousand-eyes0.wdfwwxc-p-2.prod.infra.webex.com",
    "thousand-eyes0.wlhrm-a-3.prod.infra.webex.com",
    "thousand-eyes0.agrum-m-2.prod.infra.webex.com",
    "thousand-eyes0.adubm-m-1.prod.infra.webex.com",
    "thousand-eyes0.afram-m-1.prod.infra.webex.com",
    "thousand-eyes0.aiadm-m-2.prod.infra.webex.com",
    "thousand-eyes0.aiadm-m-3.prod.infra.webex.com",
    "thousand-eyes0.mamsm-a-2.prod.infra.webex.com",
    "thousand-eyes0.mseam-a-2.prod.infra.webex.com",
    "thousand-eyes0.wamswxc-p-2.prod.infra.webex.com",
    "thousand-eyes0.wfrawxc-p-2.prod.infra.webex.com",
    "thousand-eyes0.wicnwxc-p-1.prod.infra.webex.com",
    "thousand-eyes0.wnrtm-a-2.prod.infra.webex.com",
    "thousand-eyes0.wsjcm-a-11.prod.infra.webex.com",
  ]
}

# Reference each agent
data "thousandeyes_agent" "default" {
  for_each   = var.agents
  agent_name = each.value
}

# Obtain the git SHA
data "external" "git" {
  program = [
    "git",
    "log",
    "--pretty=format:{ \"sha\": \"%H\" }",
    "-1",
    "HEAD"
  ]
}

# Create full-mesh RTP stream voice tests between each agent and every other agent
resource "thousandeyes_voice" "default-WBX3" {
  for_each         = data.thousandeyes_agent.default
  test_name        = "[WBX3] ${each.value.agent_name}"
  target_agent_id  = each.value.agent_id
  interval         = 120
  bgp_measurements = true
  use_public_bgp   = true
  num_path_traces  = 3
  mtu_measurements = false
  # EF https://www.cisco.com/c/en/us/td/docs/ios/solutions_docs/qos_solutions/QoSVoIP/QoSVoIP.html
  dscp_id     = 46
  description = "Created by https://sqbu-github.cisco.com/WebexPlatform/prod-infra/tree/${data.external.git.result.sha}/thousand_eyes"

  dynamic "agents" {
    for_each = [for agent in data.thousandeyes_agent.default : agent if each.value.agent_id != agent.agent_id]
    content {
      # https://github.com/thousandeyes/terraform-provider-thousandeyes/blob/04ca6dce427f23d37604e45c3b5ad2c886365cf5/docs/resources/voice.md#nestedblock--agents
      agent_id = agents.value.agent_id
      enabled  = true
    }
  }
  groups {
    group_id = thousandeyes_label.WBX3.group_id
  }
}

# Label each of our agents
# https://github.com/thousandeyes/terraform-provider-thousandeyes/blob/main/docs/resources/label.md#thousandeyes_label-resource
resource "thousandeyes_label" "WBX3-agents" {
  name = "WBX3"
  type = "agents"

  dynamic "agents" {
    for_each = data.thousandeyes_agent.default
    content {
      agent_id = agents.value.agent_id
    }
  }
}

# Label each of our tests
resource "thousandeyes_label" "WBX3" {
  name = "WBX3"
  type = "tests"
}
