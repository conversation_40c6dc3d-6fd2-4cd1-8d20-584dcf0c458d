# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.44
# template-version: v7.19.8-next
# module-version: v10.16.15
# wbx3-infra-version: <nil>
# resource: wdfwgen-p-5
# resource-type: cluster
# -----------------------------------------------

#~~ Resource Values
admin_port: 6443
availability_zones:
    - compute: az1
      volume: az1
aws_infra_az: <replace-me>
aws_infra_region: us-east-1
backend: s3
base_image: <replace-me>
base_k8s_image: <replace-me>
bastion_count: 0
bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_pods: auto
cidr_svcs: auto
cidr_svcs_prefix: 22
cloud_provider: openstack
cloud_service_provider: openstack
cluster_api: true
cluster_chart_version: 0.10.2
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
control_plane_network_type: prv_kubed_network
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: prod.infra.webex.com
embed_provider_template: false
enable_credential_lookup: true
enable_lb_port_security: false
env_name: w-dfw03-p0-0
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
external_network: public-network
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: not-used
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
image_name: wbx3-capi-noble-1.31.5-c8d-amd64-v2.25.5.0
infra_credentials_path: secret/data/mccprod/infra/w-dfw03-p0-0/openstack
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.44
ingress_count: 3
ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_int_count: 2
ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
internal_network_type: iso_network
internal_provider_network: provider-network
kubernetes_version: v1.31.5
location: DFW
managed_by: us-txccnc2
master_count: 3
master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
mesh:
    enabled: true
metadata:
    annotations:
        helm3Only: true
    cloud_type: generic
    cluster_type: generic
    deployment_groups:
        - kubed-prod-gen
        - wbmaas-prod-wdfw-maas1
        - wbmaas-alpha-wdfw-maas1
        - mw-gen-prod-dfw03-dc
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
module_path: modules/openstack/capi-infra
module_version: v10.16.15
name: wdfwgen-p-5
nameservers:
    - ************
    - ************
network_mission_tag: meetings
network_name: w-dfw03-p0-0-a1
network_names:
    - w-dfw03-p0-0-a1
node_pool_capacity:
    cilium-egress: 4
    worker: 59
node_pools:
    - cluster_security_groups:
        - name: istio_public
      flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
      max_size: 4
      metadata:
        node_labels: type=worker,dedicated=istio-ingress,pool-name=istio-ingress-pool
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 4
      name: istio-ingress-pool
      type: worker
    - flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
      max_size: 5
      metadata:
        node_labels: type=worker,pool-name=platform-worker
      min_size: 5
      name: platform-worker
      type: worker
    - flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
      max_size: 50
      metadata:
        node_labels: type=worker,pool-name=platform-worker
      min_size: 50
      name: worker
      type: worker
    - egress_network_id: e8166960-31ad-4b35-b5dc-3416cd74c044
      flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
      max_size: 4
      metadata:
        node_labels: type=cilium-egress,infra.webex.com/egress-type=public,pool-name=cilium-pubgw
        node_taints: dedicated=cilium-pubgw:NoSchedule
      min_size: 4
      name: cilium-pubgw
      server_group_policy: anti-affinity
      type: cilium-egress
ocp_ownership_business_service: WBX3 Platform - Meetings
ocp_ownership_component: wxkb
ocp_ownership_server_type: wxkbsvr
ocp_ownership_support_group: wbx3-prod
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pipeline_bundles:
    - platform/post-provision.yaml
    - platform/openstack-cloud-controller.yaml
    - platform/base-apps.yaml
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet: **********/18
pod_subnet_pool: pods_2
pod_subnet_size: 18
provisioning_extra_args: ""
provisioning_module_version: master
route_reflector_name: dfw03-vlan687-rr
s3_bucket_path: terraform-state/cluster-wdfwgen-p-5.tfstate
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
security_groups:
    istio_public:
        rules:
            - cidrs:
                - vpc
                - ***********/23
              from: 443
              name: lb-tcp-443
              protocol: tcp
              to: 443
              type: ingress
            - cidrs:
                - vpc
                - ***********/23
              from: 8443
              name: https
              protocol: tcp
              to: 8443
              type: ingress
            - cidrs:
                - vpc
                - ***********/23
              from: 15020
              name: lb-tcp-15020
              protocol: tcp
              to: 15020
              type: ingress
            - cidrs:
                - ***********/23
              from: 9092
              name: lb-tcp-9092
              protocol: tcp
              to: 9092
              type: ingress
            - cidrs:
                - vpc
                - ***********/23
              from: 15021
              name: lb-tcp-15021
              protocol: tcp
              to: 15021
              type: ingress
server_group_policies:
    - anti-affinity
status: online
terraform_version: v1.10.4
thousand_eyes_count: 0
use_floating_ips: false
use_provider_template: true
volume_storage: true
vpc_name: public-network
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph

#~~ No Blueprint Values


#~~ No Environment Values


#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: clusters/wdfwgen-p-5
    name: wdfwgen-p-5
    workflow: standard
dir: clusters
external: false
include_defaults: true
manifest_path: manifest.yaml
release: v1.8.1
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: v7.19.8-next
    templateversion: v7.19.8-next
workflow: standard