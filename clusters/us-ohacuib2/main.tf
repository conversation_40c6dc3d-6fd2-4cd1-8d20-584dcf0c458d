# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.20.1
# template-version: 7.20.1
# module-version: v10.16.19
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/kcs/cluster-us-ohacuib2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/kcs/cluster-us-ohacuib2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/kcs?ref=v10.16.19"
  account_id = "a38389a2-633f-4fba-88a8-c7bbbfbb8e43"
  api_credentials_path = "secret/data/mccprod/infra/kcs-cluster-apigw"
  flavor = "preprod-wxt-small-1.0.0"
  location = "a-usea2-i3-2-ha1-teams"
  name = "us-ohacuib2"
  node_pools = [
    {
      metadata = {
        name = "worker-spot"
      }
      spec = {
        auto_scale = {
          max = 6
          min = 3
        }
        flavor = "c6a.2xlarge"
        node_labels = [
          {
            key = "type"
            value = "worker"
          },
          {
            key = "node.kubernetes.io/lifecycle"
            value = "spot"
          }
        ]
        use_spot_instances = true
      }
    },
    {
      metadata = {
        name = "istio-ciscoint"
      }
      spec = {
        auto_scale = {
          max = 3
          min = 2
        }
        flavor = "c6a.2xlarge"
        node_labels = [
          {
            key = "dedicated"
            value = "istio-ingress-ciscoint"
          },
          {
            key = "node.kubernetes.io/lifecycle"
            value = "spot"
          }
        ]
        node_taints = [
          {
            effect = "NoSchedule"
            key = "dedicated"
            value = "istio-ingress-ciscoint"
          }
        ]
        security_groups = {
          istio-ciscoint = {
            rules = [
              {
                cidrs = [
                  "10.0.0.0/8"
                ]
                from = 443
                name = "cisco-https"
                protocol = "tcp"
                to = 443
                type = "ingress"
              },
              {
                cidrs = [
                  "vpc"
                ]
                from = 15021
                name = "health"
                protocol = "tcp"
                to = 15021
                type = "ingress"
              }
            ]
            tags = [
              {
                key = "kubernetes.io/cluster/us-ohacuib2"
                value = "owned"
              }
            ]
          }
        }
        use_spot_instances = true
      }
    },
    {
      metadata = {
        name = "istio-public"
      }
      spec = {
        auto_scale = {
          max = 3
          min = 2
        }
        flavor = "c6a.2xlarge"
        node_labels = [
          {
            key = "dedicated"
            value = "istio-ingress"
          },
          {
            key = "node.kubernetes.io/lifecycle"
            value = "spot"
          }
        ]
        node_taints = [
          {
            effect = "NoSchedule"
            key = "dedicated"
            value = "istio-ingress"
          }
        ]
        security_groups = {
          istio-public = {
            rules = [
              {
                cidrs = [
                  "0.0.0.0/0"
                ]
                from = 443
                name = "https"
                protocol = "tcp"
                to = 443
                type = "ingress"
              },
              {
                cidrs = [
                  "vpc"
                ]
                from = 15021
                name = "health"
                protocol = "tcp"
                to = 15021
                type = "ingress"
              }
            ]
            tags = [
              {
                key = "kubernetes.io/cluster/us-ohacuib2"
                value = "owned"
              }
            ]
          }
        }
        use_spot_instances = true
      }
    }
  ]
  release = "boron-v1.9.1"
  
}
output "cluster_id" {
  value = module.infra.cluster_id
}
output "cluster_name" {
  value = module.infra.cluster_name
}
output "nodepool_ids" {
  value = module.infra.nodepool_ids
}
output "nodepool_names" {
  value = module.infra.nodepool_names
}

