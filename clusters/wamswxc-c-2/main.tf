# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.14.5
# template-version: EXPERIMENTAL
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------
terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wamswxc-c-2.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
     source  = "terraform-provider-openstack/openstack"
      version = "1.49.0"
    }
  }
}

data "vault_generic_secret" "aws_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "infra_credential" {
  path = replace("secret/data/mccprod/infra/wams2-prod/openstack", "/data/", "/")
}

provider "openstack" {
  # v3
  application_credential_id     = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_ID"], null)
  application_credential_secret = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_SECRET"], null)
  # v2
  tenant_name = try(data.vault_generic_secret.infra_credential.data["OS_TENANT_NAME"], null)
  user_name   = try(data.vault_generic_secret.infra_credential.data["OS_USERNAME"], null)
  password    = try(data.vault_generic_secret.infra_credential.data["OS_PASSWORD"], null)

  # common
  auth_url = data.vault_generic_secret.infra_credential.data["OS_AUTH_URL"]
  region   = data.vault_generic_secret.infra_credential.data["OS_REGION_NAME"]
}


variable "image-name" {
  type = string
  default = "wbx3-bionic-1.21.5-375_b321741"
}

module "cluster" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git////modules/openstack/cluster?ref=v7.14.5"
  name = "wamswxc-c-2"
  gateway_name = "wams2-prod"
  domain = "prod.infra.webex.com"
  ssh_user = "ubuntu"

  image_name = var.image-name
  scale_image_name = var.image-name
  command_and_control = "mccprod"
  env_name = "wams2-prod"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  use_floating_ips = false
  external_network = "public-wbx3-413"
  internal_network = "wams2-prod"
  management_network = "provider-412"
  force_delete = true

  # Bastions
  bastion_count = 1
  bastion_flavor = "4vcpu.8mem.80ssd.80eph"

  # Masters
  master_count = 3
  master_flavor = "8vcpu.16mem.80ssd.0eph"

  # Workers
  worker_count = 3
  worker_flavor = "8vcpu.16mem.80ssd.0eph"

  # Ingresses
  ingress_count = 2
  ingress_flavor = "8vcpu.16mem.80ssd.0eph"

  # Internal ingresses
  ingress_int_count = 2
  ingress_int_flavor = "8vcpu.16mem.80ssd.0eph"

  # cidr ranges
  cidr_pods = "auto"
  cidr_svcs = "auto"
  cidr_node_prefix = 27
  cidr_pods_prefix = 18
  cidr_svcs_prefix = 21

  health_checks = true
  dev_cluster = false
  external_media_sg_rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-tcp","protocol"="tcp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-udp","protocol"="udp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]

  ingress_sg_rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]

  thousand_eyes_node_count = 0
  thousand_eyes_node_flavor = "8vcpu.16mem.80ssd.0eph"

  optimized_storage_count = 0
  optimized_storage_flavor = "8vcpu.32mem.80ssd.320eph"

  internal_media_node_count = 0
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"

  internal_mini_media_node_count = 0
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  external_media_node_count = 0
  external_media_node_flavor = "8vcpu.16mem.80ssd.0eph"

  internal_provider_network = "provider-412"
  internal_provider_subnets = [
    "10.0.0.0/8",
  ]
  server_group_policies = [
    "anti-affinity",
  ]


  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
  ]
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
  ]

  node_pools = [
    {
      name = "optimized-storage"
      type = "worker"
      min_size = 3
      max_size = 3
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
    },

    {
      name = "sip-pool"
      type = "external-media"
      min_size = 2
      max_size = 2
      metadata = {
        dns_prefix = "sse"
        node_labels = "pool-name=sip-pool,type=external-media"
      }
      security_groups = {
        ext = [
          {
            name     = "sse-sip-internal-tcp-3"
            protocol = "tcp"
            from     = "5061"
            to       = "5061"
            cidr     = "0.0.0.0/0"
            type     = "ingress"
          },
          {
            name     = "sse-sip-internal-tcp-4"
            protocol = "tcp"
            from     = "5062"
            to       = "5062"
            cidr     = "0.0.0.0/0"
            type     = "ingress"
          },
          {
            name     = "sse-sip-internal-tcp-5"
            protocol = "tcp"
            from     = "8934"
            to       = "8934"
            cidr     = "0.0.0.0/0"
            type     = "ingress"
          },
        ]
        media = [
          {
            name     = "sse-sip-internal-udp-1"
            protocol = "udp"
            from     = "5060"
            to       = "5060"
            cidr     = "10.0.0.0/8"
            type     = "ingress"
          },
          {
            name     = "sse-sip-internal-udp-2"
            protocol = "udp"
            from     = "5060"
            to       = "5060"
            cidr     = "**********/12"
            type     = "ingress"
          },
          {
            name     = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            from     = "5060"
            to       = "5060"
            cidr     = "10.0.0.0/8"
            type     = "ingress"
          },
          {
            name     = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            from     = "5060"
            to       = "5060"
            cidr     = "**********/12"
            type     = "ingress"
          },
        ]
      }
    },

    {
      name = "media-pool"
      type = "external-media"
      min_size = 6
      max_size = 6
      metadata = {
        dns_prefix = "mse"
        node_labels = "pool-name=media-pool,type=external-media"
      }
      security_groups = {
        ext = [
          {
            name     = "mse-media-rtp-udp"
            protocol = "udp"
            from     = "19560"
            to       = "65535"
            cidr     = "0.0.0.0/0"
            type     = "ingress"
          },
          {
            name     = "mse-media-multiplexed"
            protocol = "udp"
            from     = "5004"
            to       = "5004"
            cidr     = "0.0.0.0/0"
            type     = "ingress"
          },
        ]
        media = [
          {
            name     = "mse-grpc-tcp-1"
            protocol = "tcp"
            from     = "9443"
            to       = "9443"
            cidr     = "10.0.0.0/8"
            type     = "ingress"
          },
          {
            name     = "mse-grpc-tcp-2"
            protocol = "tcp"
            from     = "9443"
            to       = "9443"
            cidr     = "**********/12"
            type     = "ingress"
          },
          {
            name     = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            from     = "19560"
            to       = "65535"
            cidr     = "10.0.0.0/8"
            type     = "ingress"
          },
          {
            name     = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            from     = "19560"
            to       = "65535"
            cidr     = "**********/12"
            type     = "ingress"
          },
        ]
      }
    },

  ]


  # Register with Webex Cloud API Gateway

}

output "ingress_address" {
  value = module.cluster.ingress_address
}

output "aws_zone_id" {
  value = module.cluster.aws_zone_id
}

output "cluster_pod_cidr" {
  value = module.cluster.cluster_pod_cidr
}

# Export the cluster cluster pod cidr
output "cluster_svc_cidr" {
  value = module.cluster.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.cluster.cluster_node_prefix
}

# export the gateway node hash
output "gateway_hash" {
  value = module.cluster.gateway_hash
}

output "cluster_hosted_zone" {
  value = module.cluster.cluster_hosted_zone
}

# export the gateway node hash
output "gateway_ips" {
  value = module.cluster.gateway_ips
}
