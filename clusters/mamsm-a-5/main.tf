# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.12.0
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-mamsm-a-5.tfstate"
    region = "us-east-2"
  }
}


variable "ARM_SUBSCRIPTION_ID" {
  description = "Azure subscription id"
}

variable "ARM_CLIENT_SECRET" {
  description = "Azure Client Secret"
}

variable "ARM_CLIENT_ID" {
  description = "Azure Client id"
}

variable "ARM_TENANT_ID" {
  description = "Azure Tenant id"
}

provider "azurerm" {
  subscription_id = var.ARM_SUBSCRIPTION_ID
  client_id       = var.ARM_CLIENT_ID
  client_secret   = var.ARM_CLIENT_SECRET
  tenant_id       = var.ARM_TENANT_ID
  version = "=2.48.0"
  features {}
}

variable "image-name" {
  type = string
  default = "wbx3-bionic-1.21.5-363_b321741"
}

variable "windows-image-name" {
  type = string
  default = "wbx3-win-20h2-1.21.5-397_e5d3793"
}

module "k8s" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/azure/k8s?ref=v7.11.2"
  image_name = var.image-name
  windows_image = var.windows-image-name
  scale_image_name = "${var.image-name}"
  command_and_control = "mccprod"
  env_name = "az-west-eu"
  bastion_count = 1
  domain = "prod.infra.webex.com"
  vpc_name     = "wbx3-westeurope"
  azure_vault_name = "prod-westeu-keyvault"
  azure_cert_vault_name = "prod-westeu-cert"
  worker_count = 3
  worker_flavor = "Standard_F8s_v2"
  ingress_flavor = "Standard_F8s_v2"
  master_count = 3
  ingress_count = 2
  ingress_int_count      = 1
  name = "mamsm-a-5"
  bastion_flavor = "Standard_B2s"
  master_flavor = "Standard_F8s_v2"
  ssh_user = "ubuntu"
  cidr_pods               = "172.22.32.0/19"
  cidr_svcs               = "172.21.64.0/22"
  cidr_node_prefix        = "27"
  datacenter              = "westeurope"

  health_checks           = true

  internal_media_node_count   = 0
  internal_media_node_flavor  = "Standard_F32s_v2"

  internal_mini_media_node_flavor  = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  external_media_node_count  = 0
  external_media_node_flavor  = "Standard_F8s_v2"
  windows_media_node_count  = 2
  windows_media_node_flavor  = "Standard_F8s_v2"
  optimized_storage_count = 3
  optimized_storage_flavor = "Standard_DS4_v2"
  thousand_eyes_node_count = 0
  thousand_eyes_node_flavor = "Standard_F8s_v2"
  eip_pool_id  = "public-ip-sub-b"

  mgmt_remote_ips = [
    "10.0.0.0/8",    "***********/16",    "**********/16",    "**********/14",    "**********/12",    "**********/14",    "**********/16",    "*************/23",    "************/24",    "***********/24",    "***********/16",    "***********/21",    "*************/19",    "*************/21",    "*************/24",    "**********/14",    "**********/16",    "**********/19",    "************/20",    "***********/20",    "*********/14",    "**********/16",
  ]
  
  # External media security group rules
  external_media_sg_rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-tcp","protocol"="tcp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-udp","protocol"="udp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]
  
  
  # Ingress security group rules
  ingress_sg_rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]
  
  
  # HTTPS internal ips
  https_internal_ips = [
    "10.0.0.0/8",    "***********/16",    "**********/16",    "**********/14",    "**********/12",    "**********/14",    "**********/16",    "*************/23",    "************/24",    "***********/24",    "***********/16",    "***********/21",    "*************/19",    "*************/21",    "*************/24",    "**********/14",    "**********/16",    "**********/19",    "************/20",    "***********/20",    "*********/14",    "**********/16",
  ]
  
  
  # Windows secuirty group rules
  windows_sg_rules = [{"name"="msteams-media","protocol"="tcp","from"="8445","to"="8446","cidr"="0.0.0.0/0","type"="ingress"},{"name"="msteams-signalling","protocol"="tcp","from"="9445","to"="9446","cidr"="0.0.0.0/0","type"="ingress"}]
  

  node_pools = [
    {
      name = "int-media"
      type = "internal-media"
      min_size = 1
      max_size = 5
      flavor = "Standard_F32s_v2"
      cpu = "32500m"
      memory = "62Gi"

      metadata = {}
    },
    {
      name = "ext-media"
      type = "external-media"
      min_size = 1
      max_size = 5
      flavor = "Standard_F8s_v2"
      cpu = "8500m"
      memory = "12Gi"

      metadata = {}
    },
    {
      name = "worker-pool"
      type = "worker"
      min_size = 0
      max_size = 5
      flavor = "Standard_F8s_v2"
      cpu = "8500m"
      memory = "12Gi"

      metadata = {}
    },

  ]

}

output "cert" {
  value = module.k8s.vault_cert
}

output "cluster_pod_cidr" {
  value = module.k8s.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.k8s.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.k8s.cluster_node_prefix
}

output "cluster_hosted_zone" {
  value = module.k8s.cluster_hosted_zone
}
