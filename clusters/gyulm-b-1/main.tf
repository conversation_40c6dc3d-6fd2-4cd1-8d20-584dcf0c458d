# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ________________ _       __     __              __         __             __
#   / ____/ ____/ __ \ |     / /__  / /_  ___  _  __/ /____  __/ /_  ___  ____/ /
#  / / __/ /   / /_/ / | /| / / _ \/ __ \/ _ \| |/_/ //_/ / / / __ \/ _ \/ __  /
# / /_/ / /___/ ____/| |/ |/ /  __/ /_/ /  __/>  </ ,< / /_/ / /_/ /  __/ /_/ /
# \____/\____/_/     |__/|__/\___/_.___/\___/_/|_/_/|_|\__,_/_.___/\___/\__,_/
#
#
# codegen-version: v7.8.0
# template-version: 0.4.42-EXPERIMENTAL-edb1c9e
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
  path=replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}


terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-gyulm-b-1.tfstate"
    region = "us-east-2"
  }
}


# DNS AWS provider
provider "aws" {
  alias = "dns"
  access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
  max_retries = 3
}

variable "image-name" {
  type = string
  default = "wbx3-bionic-1-21-5-02"
}

provider "google" {
  project     = "gcp-wxpiekubedprod-prd-66229"
  region      = "northamerica-northeast1"
  zone        = "northamerica-northeast1-b"
}

module "k8s" {
  source       = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/gcp/k8s?ref=more-gcp"
  //source = "/home/<USER>/workspace/federated-infra//modules/gcp/k8s"
  domain       = "prod.infra.webex.com"

  project-name = "gcp-wxpiekubedprod-prd-66229"
  region-name = "northamerica-northeast1"
  cluster-name = "gyulm-b-1"
  image-name   = var.image-name
  vault-path   = "secret/mccprod/infra/g-northamerica-northeast1/gyulm-b-1/k8s/ssh/public_key"
  vault-bastion-path   = "secret/mccprod/infra/g-northamerica-northeast1/gyulm-b-1/bastion/ssh/public_key"
  join-token-vault-path = "secret/mccprod/kubernetes/gyulm-b-1/kubeadm"
  kubeadmin-vault-path = "secret/mccprod/kubernetes/gyulm-b-1/kubeadmin"
  interconnect-project-name = "gcp-wxpiexpn-prd-37004"
  interconnect-network-name = "dc-extension"
  internal-network-name = "kubed-yul01-cluster-1"
  external-network-name = "yul01-private"
  private-network-name = "yul01-private"

  # byoip static allocations
  # private ************/23
  # public ***********/23
  # 2 bastion
  private-ip-bastion-cidr = "************/31"
  # 2 internal ingress
  private-ip-ingress-int-cidr = "************/31"
  # 4 ingress
  private-ip-ingress-cidr = "************/30"
  external-ip-ingress-cidr = "***********/30"

  # upto 64 internal media
  private-ip-internal-media-cidr = "*************/26"

  # upto 126 external media
  private-ip-external-media-cidr = "************/25"
  external-ip-external-media-cidr = "***********/25"

  # Masters
  k8s-master-count  = 3
  master-machine-type = "n1-standard-8"

  # Workers
  k8s-worker-count  = 5
  worker-machine-type = "n1-standard-8"

  # Ingress
  ingress-controller-count  = 3
  ingress-machine-type = "n1-standard-8"

  # Ingress Int
  ingress-internal-count  = 2
  ingress-internal-machine-type = "n1-standard-8"

  # cidr ranges
  cidr-k8s-pods = "**********/16"
  cidr-k8s-svcs = "**********/16"
  cidr-node-prefix = "26"

  thousand-eyes-node-machine-type = "n1-standard-2"

  optimized-storage-node-count = 3
  optimized-storage-machine-type = "n2-standard-8"

  internal-media-node-count = "3"
  internal-media-node-machine-type = "n2-standard-32"

  internal-mini-media-node-count = "0"
  internal-mini-media-node-machine-type = "n2-standard-8"

  external-media-node-count = "5"
  external-media-node-machine-type = "n2-standard-8"

  dev-cluster = false


  CSBEnvironment = "prod"

}


output "cluster_pod_cidr" {
  value = module.k8s.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.k8s.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.k8s.cluster_node_prefix
}

output "cluster_hosted_zone" {
  value = module.k8s.cluster_hosted_zone
}
