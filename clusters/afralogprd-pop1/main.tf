# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: EXPERIMENTAL
# module-version: v7.23.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-afralogprd-pop1.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cluster?ref=v7.23.4"
  aws_infra_az = "eu-central-1a"
  aws_infra_azs = [
    "eu-central-1a",
    "eu-central-1b",
    "eu-central-1c"
  ]
  aws_infra_region = "eu-central-1"
  bastion_count = 1
  bastion_flavor = "m5a.large"
  cidr_node_prefix = 27
  cidr_pods = "auto"
  cidr_svcs = "auto"
  command_and_control = "mccprod"
  create_eip = true
  dev_cluster = false
  dns_credentials_path = "secret/data/mccprod/infra/route53/credentials"
  domain = "prod.infra.webex.com"
  external_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  gateway_name = "not-used"
  health_checks = false
  https_internal_ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "**********/14",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "***********/16",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
    "*************/17",
    "**********/16",
    "**********/16",
    "*************/20",
    "*************/20",
    "***********/20",
    "************/19",
    "*************/19",
    "***********/20",
    "***********/20",
    "*********/16",
    "***********/16",
  ]
  image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  infra_credentials_path = "secret/data/mccprod/infra/lma/prd/aws-lma-wbx3"
  ingress_count = 2
  ingress_flavor = "c5n.2xlarge"
  ingress_int_count = 2
  ingress_int_flavor = "c5n.2xlarge"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  master_count = 3
  master_flavor = "m5d.2xlarge"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
  ]
  name = "afralogprd-pop1"
  nat_gateway = true
  network_name = "agglog-pop-prd-net-afra1a"
  network_names = [
    "agglog-pop-prd-net-afra1a",
    "agglog-pop-prd-net-afra1b",
    "agglog-pop-prd-net-afra1c"
  ]
  node_pools = [
    {
      flavor = "c5.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "pool-name=worker-pool"
      }
      min_size = 1
      name = "worker-pool"
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      type = "worker"
      use_spot_instances = false
    },
    {
      flavor = "c5.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "pool-name=agglog-general"
      }
      min_size = 1
      name = "agglog-general"
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      type = "worker"
      use_spot_instances = false
    },
    {
      flavor = "c5.4xlarge"
      max_size = 100
      metadata = {
        node_labels = "pool-name=agglog-logstash"
      }
      min_size = 1
      name = "agglog-logstash"
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      type = "worker"
      use_spot_instances = false
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "i3.2xlarge"
  scale_image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  vpc_name = "wbx3-agglog-pop-prd-afra"
  vpc_routing = true
  worker_count = 1
  worker_flavor = "c5.4xlarge"
  
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_iam_user_vault_path" {
  value = module.infra.cluster_iam_user_vault_path
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "vpc_routing" {
  value = module.infra.vpc_routing
}
