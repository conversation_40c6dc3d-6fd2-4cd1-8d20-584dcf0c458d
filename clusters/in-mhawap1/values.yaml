# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: 7.18.33
# module-version: v10.9.0
# wbx3-infra version: <nil>
# resource: in-mhawap1
# resource type: <nil>
# environment: a-apso1-p0-0
# -----------------------------------------------#~~ Resource Values
admin_port: 6443
apiserver_record_public: true
aws_infra_azs:
    - ap-south-1a
    - ap-south-1b
    - ap-south-1c
aws_infra_region: ap-south-1
base_os: jammy
bastion_flavor: t3a.small
bastion_image_name: wbx3-capi-focal-1.25.6-containerd-v1.26.6
cloud_service_provider: aws
cluster_api: true
datacenter: ap-south-1
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: b00.prod.infra.webex.com
env_name: a-apso1-p0-0
gateway_name: not-used
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
kubernetes_version: v1.30.9
local_dns_enabled: true
location: BOM
managed_by: mccprod06
master_count: 3
master_flavor: c6a.2xlarge
mesh:
    enabled: true
metadata:
    annotations:
        helm3Only: true
    cluster_type: wap
    deployment_groups:
        - kubed-prod-gen
        - wap-report-platform-prod-aind
        - wap-stmkafka-prod-aind
        - wap-tidb-prod-aind
        - wap-pinot-prod-aind
        - wap-custom-dashboard-prod-aind
        - wap-business-analytics-prod-aind
        - wap-udp-prod-aind
        - wap-trino-ext-prod-aind
        - wxcsa-prod-aind
        - wap-wapapiservice-prod-aind
        - wap-piiencryptionservice-prod-aind
        - wap-advanced-diagnostic-prod-aind
    platform_release_channel: stable-1
name: in-mhawap1
node_pools:
    - availability_zones:
        - ap-south-1a
      flavor: m5a.4xlarge
      max_size: 4
      metadata:
        node_labels: type=worker
      min_size: 1
      name: platform-worker-1a
      type: worker
    - availability_zones:
        - ap-south-1b
      flavor: m5a.4xlarge
      max_size: 4
      metadata:
        node_labels: type=worker
      min_size: 1
      name: platform-worker-1b
      type: worker
    - availability_zones:
        - ap-south-1c
      flavor: m5a.4xlarge
      max_size: 4
      metadata:
        node_labels: type=worker
      min_size: 1
      name: platform-worker-1c
      type: worker
    - availability_zones:
        - ap-south-1a
      cluster_security_groups:
        - name: istio_public
      flavor: m5a.2xlarge
      max_size: 4
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 1
      name: istio-pool-1a
      type: worker
    - availability_zones:
        - ap-south-1b
      cluster_security_groups:
        - name: istio_public
      flavor: m5a.2xlarge
      max_size: 4
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 1
      name: istio-pool-1b
      type: worker
    - availability_zones:
        - ap-south-1c
      cluster_security_groups:
        - name: istio_public
      flavor: m5a.2xlarge
      max_size: 4
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 1
      name: istio-pool-1c
      type: worker
    - cluster_security_groups:
        - name: istio_private
      flavor: m5a.2xlarge
      max_size: 4
      metadata:
        node_labels: dedicated=istio-ingress-ciscoint
        node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
      min_size: 1
      name: istio-ciscoint
      type: worker
    - availability_zones:
        - ap-south-1a
      flavor: r5a.2xlarge
      max_size: 1
      metadata:
        node_labels: type=storage-node
        node_taints: type=storage-node:NoSchedule
      min_size: 0
      name: storage-node-1a
      type: worker
    - availability_zones:
        - ap-south-1b
      flavor: r5a.2xlarge
      max_size: 1
      metadata:
        node_labels: type=storage-node
        node_taints: type=storage-node:NoSchedule
      min_size: 0
      name: storage-node-1b
      type: worker
    - availability_zones:
        - ap-south-1c
      flavor: r5a.2xlarge
      max_size: 1
      metadata:
        node_labels: type=storage-node
        node_taints: type=storage-node:NoSchedule
      min_size: 0
      name: storage-node-1c
      type: worker
    - cluster_security_groups:
        - name: strimzi_private
      flavor: m6a.2xlarge
      max_size: 6
      metadata:
        node_labels: dedicated=wap-strimzi-kafka-provider-gateway
        node_taints: dedicated=wap-strimzi-kafka-provider-gateway:NoSchedule
      min_size: 0
      name: strimzi-kafka-gateway
      type: worker
    - flavor: r6a.2xlarge
      max_size: 8
      metadata:
        node_labels: dedicated=wap-kafka
        node_taints: dedicated=wap-kafka:NoSchedule
      min_size: 7
      name: wap-pool-kafka
      type: worker
    - flavor: m6a.4xlarge
      max_size: 6
      metadata:
        node_labels: dedicated=wap-trino
        node_taints: dedicated=wap-trino:NoSchedule
      min_size: 4
      name: wap-pool-trino
      type: worker
    - flavor: i4i.4xlarge
      max_size: 6
      metadata:
        node_labels: dedicated=wap-pinot-server
        node_taints: dedicated=wap-pinot-server:NoSchedule
      min_size: 6
      name: wap-pinot-server
      suspended_processes:
        - AZRebalance
        - ReplaceUnhealthy
      type: worker
    - flavor: i4i.4xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=wap-pinot-minion
        node_taints: dedicated=wap-pinot-minion:NoSchedule
      min_size: 1
      name: wap-pinot-minion
      suspended_processes:
        - AZRebalance
        - ReplaceUnhealthy
      type: worker
    - flavor: m6a.4xlarge
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-broker
        node_taints: dedicated=wap-pinot-broker:NoSchedule
      min_size: 3
      name: wap-pinot-broker
      type: worker
    - flavor: m6a.4xlarge
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-controller
        node_taints: dedicated=wap-pinot-controller:NoSchedule
      min_size: 3
      name: wap-pinot-controller
      type: worker
    - flavor: m6a.8xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=wap-udp
        node_taints: dedicated=wap-udp:NoSchedule
      min_size: 1
      name: wap-pool-udp
      type: worker
    - flavor: m6a.8xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=wap-udp-job-noscale
        node_taints: dedicated=wap-udp-job-noscale:NoSchedule
      min_size: 1
      name: wap-udp-job-noscale
      type: worker
    - flavor: m6a.8xlarge
      max_size: 3
      metadata:
        node_labels: dedicated=wap-udp-job
        node_taints: dedicated=wap-udp-job:NoSchedule
      min_size: 0
      name: wap-udp-job
      type: worker
    - external_security_groups:
        - name: wap-b00-wap1-sg
      flavor: m6a.8xlarge
      max_size: 2
      metadata:
        node_labels: dedicated=wap-udp-job-storage
        node_taints: dedicated=wap-udp-job-storage:NoSchedule
      min_size: 0
      name: wap-udp-job-storage
      type: worker
    - flavor: m6a.8xlarge
      max_size: 2
      metadata:
        node_labels: dedicated=wap-udp-job-storage-moderate
        node_taints: dedicated=wap-udp-job-storage-moderate:NoSchedule
      min_size: 0
      name: wap-udp-sto-moderate
      type: worker
    - flavor: m6a.8xlarge
      max_size: 4
      metadata:
        node_labels: dedicated=wap-udp-gdpr
        node_taints: dedicated=wap-udp-gdpr:NoSchedule
      min_size: 0
      name: wap-udp-gdpr
      type: worker
    - flavor: m6a.8xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=wap-udp-job-cpu-optimized
        node_taints: dedicated=wap-udp-job-cpu-optimized:NoSchedule
      min_size: 0
      name: wap-udp-cpu-optimized
      type: worker
    - flavor: m6a.8xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=wap-udp-amoro
        node_taints: dedicated=wap-udp-amoro:NoSchedule
      min_size: 0
      name: wap-udp-amoro
      type: worker
    - flavor: m6a.4xlarge
      max_size: 3
      metadata:
        node_labels: dedicated=wap-tidb-tidb
        node_taints: dedicated=wap-tidb-tidb:NoSchedule
      min_size: 3
      name: wap-tidb-tidb
      type: worker
    - flavor: m6a.4xlarge
      max_size: 6
      metadata:
        node_labels: dedicated=wap-tidb-tikv
        node_taints: dedicated=wap-tidb-tikv:NoSchedule
      min_size: 6
      name: wap-tidb-tikv
      type: worker
    - flavor: m6a.2xlarge
      max_size: 3
      metadata:
        node_labels: dedicated=wap-tidb-pd
        node_taints: dedicated=wap-tidb-pd:NoSchedule
      min_size: 3
      name: wap-tidb-pd
      type: worker
    - flavor: m6a.2xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=wap-tidb-prometheus
        node_taints: dedicated=wap-tidb-prometheus:NoSchedule
      min_size: 1
      name: wap-tidb-prometheus
      type: worker
    - external_security_groups:
        - name: wap-b00-wap1-sg
      flavor: m6a.4xlarge
      max_size: 17
      metadata:
        node_labels: dedicated=wap-micro-services
        node_taints: dedicated=wap-micro-services:NoSchedule
      min_size: 9
      name: wap-micro-services
      type: worker
    - external_security_groups:
        - name: wap-b00-wap1-sg
      flavor: m5a.2xlarge
      max_size: 4
      metadata:
        node_labels: infra.webex.com/reserved-wxcsa=true
      min_size: 2
      name: wxc-sa-pool
      type: worker
pipeline_bundles:
    - platform/post-provision.yaml
    - platform/aws-cloud-controller.yaml
    - platform/base-apps.yaml
    - platform/kubed-mesh.yaml
    - platform/cluster-autoscaler.yaml
    - platform/keda.yaml
    - platform/synthetic-heart.yaml
    - platform/split-prometheus.yaml
release: v1.7.2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
security_groups:
    istio_private:
        rules:
            - cidrs:
                - 0.0.0.0/0
              from: 443
              name: https
              protocol: tcp
              to: 443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: healthcheck
              protocol: tcp
              to: 15021
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              from: 80
              name: http
              protocol: tcp
              to: 80
              type: ingress
        tags:
            - key: kubernetes.io/cluster/in-mhawap1
              value: owned
    istio_public:
        rules:
            - cidrs:
                - 0.0.0.0/0
              from: 443
              name: https
              protocol: tcp
              to: 443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: healthcheck
              protocol: tcp
              to: 15021
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              from: 80
              name: http
              protocol: tcp
              to: 80
              type: ingress
        tags:
            - key: kubernetes.io/cluster/in-mhawap1
              value: owned
    strimzi_private:
        rules:
            - cidrs:
                - 10.0.0.0/8
              from: 8443
              name: https
              protocol: tcp
              to: 8443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: healthcheck
              protocol: tcp
              to: 15021
              type: ingress
            - cidrs:
                - 10.0.0.0/8
              from: 9092
              name: tcp
              protocol: tcp
              to: 9092
              type: ingress
            - cidrs:
                - 10.0.0.0/8
              from: 10001
              name: tcp
              protocol: tcp
              to: 10047
              type: ingress
        tags:
            - key: kubernetes.io/cluster/in-mhawap1
              value: owned
status: online
vpc_mission_tag_app: wap
vpc_routing: true
worker_flavor: m5a.2xlarge
zone_type: public

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.33
module_version: v10.9.0
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
terraform_version: v1.5.3
use_provider_template: true

#~~ Default Values
address_pools:
    pods_0: **********/15
    pods_1: **********/13
    pods_2: **********/12
    pods_3: **********/14
aws_infra_az: <replace-me>
base_image: <replace-me>
base_k8s_image: <replace-me>
bastion_count: 1
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
dev_cluster: false
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
ingress_count: 3
ingress_flavor: 8vcpu.16mem.80ssd.0eph
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
provisioning_module_version: master
server_group_policies:
    - anti-affinity
thousand_eyes_count: 0
use_floating_ips: false
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12

#~~ Internally Generated Values
atlantis_options:
    dir: clusters/in-mhawap1
    name: in-mhawap1
    workflow: standard
dir: clusters
external: false
image_name: <replace-me>
include_defaults: true
manifest_path: manifests/a-apso1-p0-0/a-apso1-p0-0-ha1/cluster/manifest.yaml
module_path: modules/aws/capi-infra
network_name: a-apso1-p0-0-ha1
network_names:
    - a-apso1-p0-0-ha1
    - a-apso1-p0-0-ha2
    - a-apso1-p0-0-pst0
node_pool_capacity:
    worker: 113
s3_bucket_path: terraform-state/cluster-in-mhawap1.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.33
    templateversion: 7.18.33
workflow: standard