# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.10.2
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wnrtmw-by-1.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = string
  default = "wbx3-bionic-1.18.17-281_173d379"
}

module "k8s" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git////modules/openstack/k8s?ref=v7.10.6"
  cluster-name = "wnrtmw-by-1"
  project-name = "wnrt-mw-prod"
  domain = "prod.infra.webex.com"
  ssh-user = "ubuntu"
  image-flavor = "16vcpu.64mem.80ssd.0eph"
  image-name = var.image-name
  scale-image-name = var.image-name
  vault-path = "meetpaas/mccprod/ssh/k8s/wnrtmw-by-1"
  join-token-vault-path = "secret/mccprod/kubernetes/wnrtmw-by-1/kubeadm"
  kubeadmin-vault-path = "secret/mccprod/kubernetes/wnrtmw-by-1/kubeadmin"
  qualys-vault-path = "secret/mccprod/global/qualys-cloud-agent"
  use-floating-ip = false
  floating-ip-pool = "public-floating-3050"
  internal-network-name = "private-v3232"
  management-network-name = "provider-196"
  force_delete_enabled = true

  # Bastions
  bastion-node-count = 1
  bastion-flavor = "2vcpu.4mem.80ssd.0eph"

  # Masters
  k8s-master-count = 3

  # Workers
  k8s-node-count = 0
  worker-flavor = "16vcpu.64mem.80ssd.0eph"

  # Ingresses
  ingress-controller-count = 8
  ingress-controller-flavor = "16vcpu.64mem.80ssd.0eph"

  # Internal ingresses
  ingress-internal-count = 3

  # cidr ranges
  use-cidr-allocations = true
  cidr-k8s-pods = "**********/18"
  cidr-k8s-svcs = "**********/22"
  cidr-node-prefix = "26"

  health-checks = true
  dev-cluster = false
  worker-eth1-network = "provider-196"


  custom-external-media-sg-rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-tcp","protocol"="tcp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-udp","protocol"="udp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]
  custom-ingress-sg-rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]




  optimized-storage-node-count = 3
  optimized-storage-flavor = "gl.16vcpu.64mem.80ssd.160eph"


  internal-media-node-flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"


  internal-mini-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"


  external-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  media-provider-network-name = "provider-196"
  media-provider-network-subnets = [
    "************/22",
  ]

  server-group-policies = [
    "soft-anti-affinity",
  ]

  bastion_allowed_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/14",
    "**********/12",
    "*************/26",
    "**********/14",
    "**********/16",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/16",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "*********/14",
    "**********/16",
  ]


  https-internal-ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/14",
    "**********/12",
    "*************/26",
    "**********/14",
    "**********/16",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/16",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "*********/14",
    "**********/16",
  ]


  node-pools = [
    {
      name = "mw-p0-"
      type = "provider-worker"
      min_size = 117
      max_size = 117
      flavor = "16vcpu.64mem.80ssd.0eph"
      cpu = "8000m"
      memory = "12Gi"
      metadata = {
        node_labels = "pool-name=mw-p0,type=worker"
      }

    },
  ]


  # Register with Webex Cloud API Gateway






}

output "ingress_address" {
  value = module.k8s.ingress_address
}

output "aws_zone_id" {
  value = module.k8s.aws_zone_id
}

output "cluster_pod_cidr" {
  value = module.k8s.cluster_pod_cidr
}

# Export the cluster k8s pod cidr
output "cluster_svc_cidr" {
  value = module.k8s.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.k8s.cluster_node_prefix
}

# export the gateway node hash
output "gateway_hash" {
  value = module.k8s.gateway_hash
}

output "cluster_hosted_zone" {
  value = module.k8s.cluster_hosted_zone
}

# export the gateway node hash
output "gateway_ips" {
  value = module.k8s.gateway_ips
}
