# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.2
# template-version: v7.19.8-next
# module-version: v7.23.4-custom-routing
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/wdfw02-ocp4-prod/openstack
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wdfwwxc-p-2.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
     source  = "terraform-provider-openstack/openstack"
      version = "1.49.0"
    }
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/cluster?ref=a879a5252caf36664627b0a193388367aa94681b"
  bastion_block_storage = true
  bastion_count = 1
  bastion_flavor = "kubed.gv.4vcpu.8mem.0ssd.0eph"
  cidr_node_prefix = 26
  cidr_pods = "auto"
  cidr_svcs = "auto"
  cloud_service_provider = "openstack"
  command_and_control = "mccprod"
  dev_cluster = false
  domain = "prod.infra.webex.com"
  external_media_node_count = 0
  external_media_node_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  external_network = "public-floating-3090"
  force_delete = true
  gateway_name = "wdfw02-ocp4-prod"
  health_checks = true
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  image_name = "wbx3-focal-1.23.5-containerd-659_421a101"
  infra_credentials_path = "secret/data/mccprod/infra/wdfw02-ocp4-prod/openstack"
  ingress_block_storage = true
  ingress_count = 2
  ingress_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_int_block_storage = true
  ingress_int_count = 2
  ingress_int_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_count = 0
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"
  internal_mini_media_node_count = 0
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  internal_network = "wdfw02-ocp4-prod"
  internal_provider_network = "provider-3091"
  internal_provider_subnets = [
    "10.0.0.0/8"
  ]
  management_network = "provider-3091"
  master_block_storage = true
  master_count = 3
  master_flavor = "kubed.gv.8vcpu.32mem.0ssd.0eph"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  name = "wdfwwxc-p-2"
  node_pools = [
    {
      max_size = 6
      min_size = 6
      name = "worker-pool"
      root_block_storage = true
      type = "worker"
    },
    {
      flavor = "kubed.gv.16vcpu.64mem.0ssd.0eph"
      max_size = 3
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
      min_size = 3
      name = "prom-pool"
      root_block_storage = true
      type = "worker"
    },
    {
      flavor = "kubed.gv.2vcpu.4mem.0ssd.0eph"
      max_size = 1
      metadata = {
        dns_prefix = "thousand-eyes"
        node_labels = "type=thousand-eyes"
      }
      min_size = 1
      name = "thousand-eyes"
      public_network_name = "public-3005"
      root_block_storage = true
      type = "external-media"
    },
    {
      max_size = 51
      media_network_name = "provider-3004"
      metadata = {
        dns_prefix = "sse-a"
        node_labels = "pool-name=sip-pool-a,type=external-media,infra.webex.com/public-ipv6-addr=2a05-4200--5bc-10a"
      }
      min_size = 51
      name = "sip-pool-a"
      public_network_name = "public-3005"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5061
            name = "sse-sip-internal-tcp-3"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5062
            name = "sse-sip-internal-tcp-4"
            protocol = "tcp"
            to = 5062
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 8934
            name = "sse-sip-internal-tcp-5"
            protocol = "tcp"
            to = 8934
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      max_size = 54
      media_network_name = "provider-3004"
      metadata = {
        dns_prefix = "mse-a"
        node_labels = "pool-name=media-pool-a,type=external-media"
      }
      min_size = 54
      name = "media-pool-a"
      public_network_name = "public-3005"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "mse-media-rtp-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5004
            name = "mse-media-multiplexed"
            protocol = "udp"
            to = 5004
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      max_size = 34
      media_network_name = "provider-3004"
      metadata = {
        dns_prefix = "sse-b"
        node_labels = "pool-name=sip-pool-b,type=external-media,infra.webex.com/public-ipv6-addr=2a05-4200--5bc-10a"
      }
      min_size = 34
      name = "sip-pool-b"
      public_network_name = "provider-3258"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5061
            name = "sse-sip-internal-tcp-3"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5062
            name = "sse-sip-internal-tcp-4"
            protocol = "tcp"
            to = 5062
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 8934
            name = "sse-sip-internal-tcp-5"
            protocol = "tcp"
            to = 8934
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      max_size = 34
      media_network_name = "provider-3212"
      metadata = {
        dns_prefix = "sse-d"
        node_labels = "pool-name=sip-pool-d,type=external-media,infra.webex.com/public-ipv6-addr=2a05-4200--5bc-10a"
      }
      min_size = 34
      name = "sip-pool-d"
      public_network_name = "provider-3031"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5061
            name = "sse-sip-internal-tcp-3"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5062
            name = "sse-sip-internal-tcp-4"
            protocol = "tcp"
            to = 5062
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 8934
            name = "sse-sip-internal-tcp-5"
            protocol = "tcp"
            to = 8934
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 37
      media_network_name = "provider-1719"
      metadata = {
        dns_prefix = "sse-e"
        node_labels = "pool-name=sip-pool-e,type=external-media,infra.webex.com/public-ipv6-addr=2a05-4200--5bc-10a"
      }
      min_size = 37
      name = "sip-pool-e"
      public_network_name = "public-1706"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5061
            name = "sse-sip-internal-tcp-3"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5062
            name = "sse-sip-internal-tcp-4"
            protocol = "tcp"
            to = 5062
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 8934
            name = "sse-sip-internal-tcp-5"
            protocol = "tcp"
            to = 8934
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      custom_routing = true
      max_size = 6
      media_network_name = "provider-3004"
      metadata = {
        dns_prefix = "sse-p"
        node_labels = "pool-name=sip-peering-pool,infra.webex.com/dns-prefix=sse-p,type=external-media"
      }
      min_size = 6
      name = "sip-peering-pool"
      public_network_name = "provider-1704"
      root_block_storage = true
      routes = {
        eth1 = [
          "10.0.0.0/8"
        ]
        eth2 = [
          "***********/20",
          "************/20",
          "************/20"
        ]
      }
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5061
            name = "sse-sip-internal-tcp-3"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5062
            name = "sse-sip-internal-tcp-4"
            protocol = "tcp"
            to = 5062
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 8934
            name = "sse-sip-internal-tcp-5"
            protocol = "tcp"
            to = 8934
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      max_size = 45
      media_network_name = "provider-3004"
      metadata = {
        dns_prefix = "mse-b"
        node_labels = "pool-name=media-pool-b,type=external-media"
      }
      min_size = 45
      name = "media-pool-b"
      public_network_name = "provider-3258"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "mse-media-rtp-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5004
            name = "mse-media-multiplexed"
            protocol = "udp"
            to = 5004
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      max_size = 86
      media_network_name = "provider-3212"
      metadata = {
        dns_prefix = "mse-d"
        node_labels = "pool-name=media-pool-d,type=external-media"
      }
      min_size = 86
      name = "media-pool-d"
      public_network_name = "provider-3031"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "mse-media-rtp-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5004
            name = "mse-media-multiplexed"
            protocol = "udp"
            to = 5004
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 50
      media_network_name = "provider-1719"
      metadata = {
        dns_prefix = "mse-e"
        node_labels = "pool-name=media-pool-e,type=external-media"
      }
      min_size = 50
      name = "media-pool-e"
      public_network_name = "public-1706"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "mse-media-rtp-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5004
            name = "mse-media-multiplexed"
            protocol = "udp"
            to = 5004
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      custom_routing = true
      max_size = 43
      media_network_name = "provider-3004"
      metadata = {
        dns_prefix = "mse-p"
        node_labels = "pool-name=media-peering-pool,infra.webex.com/dns-prefix=mse-p,type=external-media"
      }
      min_size = 43
      name = "media-peering-pool"
      public_network_name = "provider-1704"
      root_block_storage = true
      routes = {
        eth1 = [
          "10.0.0.0/8"
        ]
        eth2 = [
          "***********/20",
          "************/20",
          "************/20"
        ]
      }
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "mse-media-rtp-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5004
            name = "mse-media-multiplexed"
            protocol = "udp"
            to = 5004
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-media.nv.32vcpu.64mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhruva-proxy"
        node_labels = "pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod"
      }
      min_size = 2
      name = "wxc-dhruva-proxy"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-media.nv.32vcpu.64mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhruvaproxy-mno"
        node_labels = "pool-name=wxc-dhruvaproxy-mno,infra.webex.com/dns-prefix=wxc-dhruvaproxy-mno,deployment=prod"
      }
      min_size = 2
      name = "wxc-dhruvaproxy-mno"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-antares.nv.16vcpu.16mem.0ssd.0eph"
      max_size = 23
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhruva-antares"
        node_labels = "pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=prod"
      }
      min_size = 23
      name = "wxc-dhruva-antares"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-tcp"
            protocol = "tcp"
            to = 65535
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-tcp"
            protocol = "tcp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed.gv.16vcpu.16mem.0ssd.0eph"
      max_size = 3
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhruvantares-mno"
        node_labels = "pool-name=wxc-dhruvantares-mno,infra.webex.com/dns-prefix=wxc-dhruvantares-mno,deployment=prod"
      }
      min_size = 3
      name = "wxc-dhruvantares-mno"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-tcp"
            protocol = "tcp"
            to = 65535
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-tcp"
            protocol = "tcp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      custom_routing = true
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 1
      metadata = {
        dns_prefix = "sse-g"
        node_labels = "pool-name=sip-pool-g,type=external-media,infra.webex.com/dns-prefix=sse-g"
      }
      min_size = 1
      name = "sip-pool-g"
      public_network_name = "public-3074"
      root_block_storage = true
      routes = {
        eth1 = [
          "10.0.0.0/8"
        ]
        eth2 = [
          "***********/22"
        ]
      }
      security_groups = {
        ext = [
          {
            cidr = "*************/29"
            from = 1024
            name = "sse-sip-tango-tcp"
            protocol = "tcp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "*************/29"
            from = 1024
            name = "sse-sip-tango-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      custom_routing = true
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 2
      metadata = {
        dns_prefix = "mse-g"
        node_labels = "pool-name=media-pool-g,type=external-media,infra.webex.com/dns-prefix=mse-g"
      }
      min_size = 2
      name = "media-pool-g"
      public_network_name = "public-3074"
      root_block_storage = true
      routes = {
        eth1 = [
          "10.0.0.0/8"
        ]
        eth2 = [
          "***********/22"
        ]
      }
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "mse-media-rtp-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5004
            name = "mse-media-multiplexed"
            protocol = "udp"
            to = 5004
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-media.nv.32vcpu.64mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhr-proxy-1"
        node_labels = "pool-name=wxc-dhr-proxy-1,infra.webex.com/dns-prefix=wxc-dhr-proxy-1,deployment=prod"
      }
      min_size = 2
      name = "wxc-dhr-proxy-1"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-media.nv.32vcpu.64mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhr-proxy-2"
        node_labels = "pool-name=wxc-dhr-proxy-2,infra.webex.com/dns-prefix=wxc-dhr-proxy-2,deployment=prod"
      }
      min_size = 2
      name = "wxc-dhr-proxy-2"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-media.nv.32vcpu.64mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhr-proxy-3"
        node_labels = "pool-name=wxc-dhr-proxy-3,infra.webex.com/dns-prefix=wxc-dhr-proxy-3,deployment=prod"
      }
      min_size = 2
      name = "wxc-dhr-proxy-3"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-media.nv.32vcpu.64mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhr-proxy-4"
        node_labels = "pool-name=wxc-dhr-proxy-4,infra.webex.com/dns-prefix=wxc-dhr-proxy-4,deployment=prod"
      }
      min_size = 2
      name = "wxc-dhr-proxy-4"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-media.nv.32vcpu.64mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-3006"
      metadata = {
        dns_prefix = "wxc-dhr-proxy-5"
        node_labels = "pool-name=wxc-dhr-proxy-5,infra.webex.com/dns-prefix=wxc-dhr-proxy-5,deployment=prod"
      }
      min_size = 2
      name = "wxc-dhr-proxy-5"
      public_network_name = "public-3007"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "homer.12vcpu.24mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-1719"
      metadata = {
        dns_prefix = "xlm"
        node_labels = "type=external-large-media"
        node_taints = "type=media-node:NoSchedule"
      }
      min_size = 2
      name = "homer-pool"
      public_network_name = "public-1706"
      root_block_storage = true
      type = "external-large-media"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  server_group_policies = [
    "anti-affinity"
  ]
  thousand_eyes_node_count = 0
  thousand_eyes_node_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  use_floating_ips = false
  worker_block_storage = true
  worker_count = 1
  worker_eth1_network = "provider-3091"
  worker_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  
}

output "aws_zone_id" {
  value = module.infra.aws_zone_id
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "ingress_address" {
  value = module.infra.ingress_address
}

