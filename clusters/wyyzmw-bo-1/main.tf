# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.14.5
# template-version: EXPERIMENTAL
# module-version: v7.14.5
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/wyyz-mw-prod/openstack
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------
terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wyyzmw-bo-1.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
      source  = "terraform-provider-openstack/openstack"
      version = "1.49.0"
    }
  }
}

variable "image-name" {
  type = string
  default = "wbx3-focal-1.21.5-fy23q1"
}

module "cluster" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git////modules/openstack/cluster?ref=v7.14.5"
  name = "wyyzmw-bo-1"
  gateway_name = "wyyz-mw-prod"
  domain = "prod.infra.webex.com"
  ssh_user = "ubuntu"

  image_name = var.image-name
  scale_image_name = var.image-name
  command_and_control = "mccprod"
  env_name = "wyyz-mw-prod"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  use_floating_ips = false
  external_network = "provider-414"
  internal_network = "provider-v101"
  management_network = "provider-v423"
  force_delete = true

  # Bastions
  bastion_count = 1
  bastion_flavor = "gv.2vcpu.4mem.0ssd.0eph"
  bastion_block_storage = true

  # Masters
  master_count = 3
  master_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  master_block_storage = true

  # Workers
  worker_count = 0
  worker_flavor = "kubed.gv.16vcpu.64mem.0ssd.0eph"
  worker_block_storage = true

  # Ingresses
  ingress_count = 6
  ingress_flavor = "kubed.gv.16vcpu.16mem.0ssd.0eph"
  ingress_block_storage = true

  # Internal ingresses
  ingress_int_count = 2
  ingress_int_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_int_block_storage = true

  # cidr ranges
  cidr_pods = "auto"
  cidr_svcs = "auto"
  cidr_node_prefix = 26
  cidr_pods_prefix = 18
  cidr_svcs_prefix = 22

  health_checks = true
  dev_cluster = false
  worker_eth1_network = "provider-v423"

  external_media_sg_rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]

  ingress_sg_rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]

  optimized_storage_count = 0
  optimized_storage_flavor = "8vcpu.16mem.512ssd.0eph"

  internal_provider_network = "provider-v423"
  internal_provider_subnets = [
    "************/23",
  ]
  server_group_policies = [
    "soft-anti-affinity",
  ]


  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
  ]
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
  ]

  node_pools = [
    {
      name = "mw-p0-"
      type = "provider-worker"
      min_size = 40
      max_size = 40
      flavor = "kubed.gv.16vcpu.64mem.0ssd.0eph"
      root_block_storage = true
      metadata = {
        node_labels = "pool-name=mw-p0,type=worker"
      }
    },
    {
      name = "mw-p1-"
      type = "provider-worker"
      min_size = 0
      max_size = 0
      flavor = "kubed.gv.16vcpu.128mem.0ssd.0eph"
      root_block_storage = true
      metadata = {
        node_labels = "pool-name=mw-p1,type=worker"
      }
    },
    {
      name = "optimized-storage"
      type = "worker"
      min_size = 3
      max_size = 3
      flavor = "kubed.gv.8vcpu.64mem.0ssd.0eph"
      root_block_storage = true
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
    },
    {
      flavor = "kubed.gv.16vcpu.64mem.0ssd.0eph"
      max_size = 4
      metadata = {
        node_labels = "pool-name=worker,type=worker"
      }
      min_size = 4
      name = "worker"
      root_block_storage = true
      type = "provider-worker"
    },

  ]


  # Register with Webex Cloud API Gateway

}

output "ingress_address" {
  value = module.cluster.ingress_address
}

output "aws_zone_id" {
  value = module.cluster.aws_zone_id
}

output "cluster_pod_cidr" {
  value = module.cluster.cluster_pod_cidr
}

# Export the cluster cluster pod cidr
output "cluster_svc_cidr" {
  value = module.cluster.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.cluster.cluster_node_prefix
}

# export the gateway node hash
output "gateway_hash" {
  value = module.cluster.gateway_hash
}

output "cluster_hosted_zone" {
  value = module.cluster.cluster_hosted_zone
}

# export the gateway node hash
output "gateway_ips" {
  value = module.cluster.gateway_ips
}
