# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.44
# template-version: v7.19.8-next
# module-version: v10.7.17-fix-sg-attachment
# wbx3-infra-version: <nil>
# resource: us-nywrtm4
# resource-type: cluster
# -----------------------------------------------

#~~ Resource Values
admin_port: 6442
availability_zones:
    - compute: az1
      volume: az1
aws_infra_az: <replace-me>
backend: s3
base_image: wbx3-focal-1.27.12-containerd-202501-5
base_k8s_image: wbx3-focal-1.27.12-containerd-202501-5
bastion_block_storage: true
bastion_count: 1
bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_pods: auto
cidr_svcs: auto
cidr_svcs_prefix: 22
cloud_service_provider: openstack
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
env_name: wjfk02-ocp4-prod
external_media_node_count: 0
external_media_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
external_network: public-431
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: wjfk02-ocp4-prod
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
image: wbx3-focal-1.27.12-containerd-202501-5
image_flavor: 8vcpu.32mem.80ssd.0eph
image_name: wbx3-focal-1.27.12-containerd-202501-5
infra_credentials_path: secret/data/mccprod/infra/wjfk02-ocp4-prod/openstack
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.44
ingress_block_storage: true
ingress_count: 3
ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_int_block_storage: true
ingress_int_count: 2
ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_count: 0
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_count: 0
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
internal_network: wjfk02-ocp4-prod
internal_provider_network: provider-430
location: JFK
management_network: provider-430
master_block_storage: true
master_count: 3
master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
mesh:
    enabled: false
metadata:
    annotations:
        cluster_type: wxcedge-prod
        helm3Only: true
    cluster_type: wxcedge-prod
    deployment_groups:
        - kubed-prod-gen
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
module_commit: 2eb03eccd57363a164c66222c1c2fa093a45e324
module_path: modules/openstack/cluster
module_version: v10.7.17-fix-sg-attachment
name: us-nywrtm4
nameservers:
    - <replace-me>
network_name: wjfk02-ocp4-prod
network_names:
    - wjfk02-ocp4-prod
node_pool_capacity:
    external-media: 75
    worker: 17
node_pools:
    - flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
      max_size: 15
      metadata:
        node_labels: pool-name=worker,type=worker
      min_size: 5
      name: worker
      root_block_storage: true
      type: worker
    - flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
      max_size: 2
      metadata:
        node_labels: type=optimized-storage-node
      min_size: 2
      name: prom-pool
      root_block_storage: true
      type: worker
    - flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
      max_size: 32
      media_network_name: provider-485
      metadata:
        dns-prefix: sse-f
        node_labels: pool-name=sip-pool-f,type=external-media,infra.webex.com/dns-prefix=sse-f,infra.webex.com/public-ipv6-addr=2607-fcf0-9000-1--6
        node_taints: type=media-node:NoSchedule
      min_size: 32
      name: sip-pool-f
      public_network_name: public-491
      root_block_storage: true
      security_groups:
        ext:
            - cidr: 0.0.0.0/0
              from: 5061
              name: sse-sip-internal-tcp-3
              protocol: tcp
              to: 5061
              type: ingress
            - cidr: 0.0.0.0/0
              from: 5062
              name: sse-sip-internal-tcp-4
              protocol: tcp
              to: 5062
              type: ingress
            - cidr: 0.0.0.0/0
              from: 8934
              name: sse-sip-internal-tcp-5
              protocol: tcp
              to: 8934
              type: ingress
        media:
            - cidr: 10.0.0.0/8
              from: 5060
              name: sse-sip-internal-udp-1
              protocol: udp
              to: 5060
              type: ingress
            - cidr: **********/12
              from: 5060
              name: sse-sip-internal-udp-2
              protocol: udp
              to: 5060
              type: ingress
            - cidr: 10.0.0.0/8
              from: 5060
              name: sse-sip-internal-tcp-1
              protocol: tcp
              to: 5060
              type: ingress
            - cidr: **********/12
              from: 5060
              name: sse-sip-internal-tcp-2
              protocol: tcp
              to: 5060
              type: ingress
            - cidr: 10.0.0.0/8
              from: 3000
              name: sse-lb-udp
              protocol: udp
              to: 5000
              type: ingress
            - cidr: 10.0.0.0/8
              from: 3000
              name: sse-lb-tcp
              protocol: tcp
              to: 5000
              type: ingress
      type: external-media
    - flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
      max_size: 43
      media_network_name: provider-485
      metadata:
        dns-prefix: mse-f
        node_labels: pool-name=media-pool-f,type=external-media,infra.webex.com/dns-prefix=mse-f
        node_taints: type=media-node:NoSchedule
      min_size: 43
      name: media-pool-f
      public_network_name: public-491
      root_block_storage: true
      security_groups:
        ext:
            - cidr: 0.0.0.0/0
              from: 19560
              name: mse-media-rtp-udp
              protocol: udp
              to: 65535
              type: ingress
            - cidr: 0.0.0.0/0
              from: 5004
              name: mse-media-multiplexed
              protocol: udp
              to: 5004
              type: ingress
        media:
            - cidr: 10.0.0.0/8
              from: 9443
              name: mse-grpc-tcp-1
              protocol: tcp
              to: 9443
              type: ingress
            - cidr: **********/12
              from: 9443
              name: mse-grpc-tcp-2
              protocol: tcp
              to: 9443
              type: ingress
            - cidr: 10.0.0.0/8
              from: 19560
              name: mse-media-rtp-udp-media-1
              protocol: udp
              to: 65535
              type: ingress
            - cidr: **********/12
              from: 19560
              name: mse-media-rtp-udp-media-2
              protocol: udp
              to: 65535
              type: ingress
      type: external-media
ocp_ownership_business_service: WBX3 Platform - Meetings
ocp_ownership_component: wxkb
ocp_ownership_server_type: wxkbsvr
ocp_ownership_support_group: wbx3-prod
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 0
optimized_storage_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
pipeline_bundles:
    - platform/post-provision.yaml
    - platform/openstack-cloud-controller.yaml
    - platform/base-apps.yaml
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: kubelet_max_pods=56 container_manager='containerd'
provisioning_module_version: master
s3_bucket_path: terraform-state/cluster-us-nywrtm4.tfstate
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
server_group_policies:
    - anti-affinity
status: offline
terraform_version: v1.5.4
thousand_eyes_count: 0
thousand_eyes_node_count: 0
thousand_eyes_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
use_floating_ips: false
use_provider_template: true
volume_storage: true
vpc_name: public-431
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_block_storage: true
worker_count: 1
worker_eth1_network: provider-430
worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph

#~~ No Blueprint Values


#~~ No Environment Values


#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: clusters/us-nywrtm4
    name: us-nywrtm4
    workflow: standard
dir: clusters
external: false
manifest_path: manifest.yaml
release: v1.9.1
versioningData:
    infractlversion: v7.19.8-next
    templateversion: v7.19.8-next
workflow: standard