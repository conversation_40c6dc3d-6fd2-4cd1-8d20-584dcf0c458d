# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.44
# template-version: v7.19.8-next
# module-version: v10.16.15
# wbx3-infra-version: <nil>
# resource: wjfkgen-p-2
# resource-type: cluster
# -----------------------------------------------

#~~ Resource Values
admin_port: 6443
availability_zones:
    - compute: az1
      volume: az1
aws_infra_az: <replace-me>
aws_infra_region: us-east-1
backend: s3
base_image: <replace-me>
base_k8s_image: <replace-me>
bastion_count: 0
bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_pods: auto
cidr_svcs: auto
cidr_svcs_prefix: 22
cloud_provider: openstack
cloud_service_provider: openstack
cluster_api: true
cluster_chart_version: 0.10.2
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
control_plane_affinity_policy: anti-affinity
control_plane_network_type: prv_kubed_network
control_plane_volume_type: xlarge-data-wap
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: prod.infra.webex.com
embed_provider_template: false
enable_credential_lookup: true
enable_lb_port_security: false
env_name: w-jfk02-p0-0
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
external_network: public-network
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: not-used
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
image: wbx3-capi-jammy-1.31.5-c8d-amd64-v1.29.6
image_flavor: 8vcpu.32mem.80ssd.0eph
image_name: wbx3-capi-jammy-1.31.5-c8d-amd64-v1.29.6
infra_credentials_path: secret/data/mccprod/infra/wjfk02-ocp4-prod/openstack
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.44
ingress_count: 3
ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_int_count: 2
ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
internal_network_type: iso_network
internal_provider_network: provider-network
kubernetes_version: v1.31.5
location: JFK
managed_by: us-txccnc2
master_count: 3
master_flavor: kubed-wap.gl.16vcpu.64mem.0ssd.0eph
mesh:
    enabled: true
metadata:
    annotations:
        helm3Only: true
    cloud_type: generic
    deployment_groups:
        - kubed-prod-gen
        - wap-unified-telemetry-prod-wjfk
        - wap-unified-telemetry-sec-prod-wjfk
        - wap-stmkafka-prod-wjfk
        - wap-trino-int-prod-wjfk
        - wap-trino-ext-prod-wjfk
        - wap-trino-device-prod-wjfk
        - wap-piiencryptionservice-prod-wjfk
        - wap-wapapiservice-prod-wjfk
        - wap-waphelperservice-prod-wjfk
        - wap-sdwanapi-prod-wjfk
        - wap-activityreportservice-prod-wjfk
        - wap-profile-system-prod-wjfk
        - wap-ragaas-prod-wjfk
        - wap-radar-prod-wjfk
        - wap-custom-dashboard-prod-wjfk
        - wap-business-analytics-prod-wjfk
        - wap-mats-prod-wjfk
        - wap-anti-fraud-prod-wjfk
        - wap-advanced-diagnostic-prod-wjfk
        - wap-datahub-prod-wjfk
        - wap-personal-insights-prod-wjfk
        - wap-roma-prod-wjfk
        - wap-pinot-prod-01-wjfk
        - wap-pinot-prod-02-wjfk
        - wap-starrocks-prod-wjfk
        - wap-tidb-prod-wjfk
        - wap-udp-prod-wjfk
        - wap-coe-prod-wjfk
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
module_path: modules/openstack/capi-infra
module_version: v10.16.15
name: wjfkgen-p-2
nameservers:
    - ************
network_mission_tag: meetings
network_name: w-jfk02-p0-0-a1
network_names:
    - w-jfk02-p0-0-a1
node_pool_capacity:
    cilium-egress: 24
    worker: 546
node_pools:
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.64mem.0ssd.0eph
      max_size: 10
      metadata:
        node_labels: type=worker
      min_size: 10
      name: worker
      type: worker
      volume_type: xlarge-data-wap
    - cluster_security_groups:
        - name: istio_public
      enable_server_group: false
      flavor: kubed-wap.gl.8vcpu.32mem.0ssd.0eph
      max_size: 20
      metadata:
        node_labels: type=worker,dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 20
      name: istio-ingress
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.64mem.0ssd.0eph
      max_size: 41
      metadata:
        node_labels: dedicated=wap-micro-services
        node_taints: dedicated=wap-micro-services:NoSchedule
      min_size: 41
      name: wap-micro-services
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.32vcpu.160mem.0ssd.0eph
      max_size: 12
      metadata:
        node_labels: dedicated=wap-udp-calling
        node_taints: dedicated=wap-udp-calling:NoSchedule
      min_size: 12
      name: wap-udp-calling
      type: worker
      volume_size: 500
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.32vcpu.160mem.0ssd.0eph
      max_size: 12
      metadata:
        node_labels: dedicated=wap-udp-callanalyzer
        node_taints: dedicated=wap-udp-callanalyzer:NoSchedule
      min_size: 12
      name: wap-udp-callanalyzer
      type: worker
      volume_size: 500
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.32vcpu.256mem.0ssd.0eph
      max_size: 42
      metadata:
        node_labels: dedicated=wap-udp-maf
        node_taints: dedicated=wap-udp-maf:NoSchedule
      min_size: 42
      name: wap-udp-maf
      type: worker
      volume_size: 500
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.32vcpu.160mem.0ssd.0eph
      max_size: 29
      metadata:
        node_labels: dedicated=wap-udp-meeting
        node_taints: dedicated=wap-udp-meeting:NoSchedule
      min_size: 29
      name: wap-udp-meeting
      type: worker
      volume_size: 500
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.32vcpu.160mem.0ssd.0eph
      max_size: 30
      metadata:
        node_labels: dedicated=wap-udp-roma
        node_taints: dedicated=wap-udp-roma:NoSchedule
      min_size: 30
      name: wap-udp-roma
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.32vcpu.160mem.0ssd.0eph
      max_size: 13
      metadata:
        node_labels: dedicated=wap-udp-others
        node_taints: dedicated=wap-udp-others:NoSchedule
      min_size: 13
      name: wap-udp-others
      type: worker
      volume_size: 500
      volume_type: xlarge-data-wap
    - cluster_security_groups:
        - name: strimzi_kafka_public
      enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.0eph
      max_size: 15
      metadata:
        node_labels: dedicated=wap-strimzi-kafka-provider-gateway
        node_taints: dedicated=wap-strimzi-kafka-provider-gateway:NoSchedule
      min_size: 15
      name: strimzi-kafka-gateway
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed.gv.8vcpu.64mem.0ssd.0eph
      max_size: 15
      metadata:
        node_labels: dedicated=wap-kafka
        node_taints: dedicated=wap-kafka:NoSchedule
      min_size: 15
      name: wap-pool-kafka
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.8vcpu.64mem.0ssd.5000eph
      max_size: 50
      metadata:
        node_labels: dedicated=wap-kafkakbr
        node_taints: dedicated=wap-kafkakbr:NoSchedule
      min_size: 50
      name: wap-pool-kafkakbr
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.64mem.0ssd.0eph
      max_size: 24
      metadata:
        node_labels: dedicated=wap-trino
        node_taints: dedicated=wap-trino:NoSchedule
      min_size: 24
      name: wap-pool-trino
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.3500eph
      max_size: 36
      metadata:
        node_labels: dedicated=wap-pinot-server-01
        node_taints: dedicated=wap-pinot-server-01:NoSchedule
      min_size: 36
      name: wap-pinot-server-01
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.3500eph
      max_size: 158
      metadata:
        node_labels: dedicated=wap-pinot-server-02
        node_taints: dedicated=wap-pinot-server-02:NoSchedule
      min_size: 158
      name: wap-pinot-server-02
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.3500eph
      max_size: 6
      metadata:
        node_labels: dedicated=wap-pinot-minion-01
        node_taints: dedicated=wap-pinot-minion-01:NoSchedule
      min_size: 6
      name: wap-pinot-minion-01
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.3500eph
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-minion-02
        node_taints: dedicated=wap-pinot-minion-02:NoSchedule
      min_size: 3
      name: wap-pinot-minion-02
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.500eph
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-controller-01
        node_taints: dedicated=wap-pinot-controller-01:NoSchedule
      min_size: 3
      name: wap-pinot-controller-01
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.500eph
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-controller-02
        node_taints: dedicated=wap-pinot-controller-02:NoSchedule
      min_size: 3
      name: wap-pinot-controller-02
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.500eph
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-broker-01
        node_taints: dedicated=wap-pinot-broker-01:NoSchedule
      min_size: 3
      name: wap-pinot-broker-01
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.500eph
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-broker-02
        node_taints: dedicated=wap-pinot-broker-02:NoSchedule
      min_size: 3
      name: wap-pinot-broker-02
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.500eph
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-zookeeper-01
        node_taints: dedicated=wap-pinot-zookeeper-01:NoSchedule
      min_size: 3
      name: wap-pinot-zookeeper-01
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.500eph
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-zookeeper-02
        node_taints: dedicated=wap-pinot-zookeeper-02:NoSchedule
      min_size: 3
      name: wap-pinot-zookeeper-02
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.3500eph
      max_size: 0
      metadata:
        node_labels: dedicated=wap-starrocks-fe
        node_taints: dedicated=wap-starrocks-fe:NoSchedule
      min_size: 0
      name: wap-starrocks-fe
      type: worker
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.3500eph
      max_size: 0
      metadata:
        node_labels: dedicated=wap-starrocks-be
        node_taints: dedicated=wap-starrocks-be:NoSchedule
      min_size: 0
      name: wap-starrocks-be
      type: worker
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.500eph
      max_size: 0
      metadata:
        node_labels: dedicated=wap-tidb-tidb
        node_taints: dedicated=wap-tidb-tidb:NoSchedule
      min_size: 0
      name: wap-tidb-tidb
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.3500eph
      max_size: 0
      metadata:
        node_labels: dedicated=wap-tidb-tikv
        node_taints: dedicated=wap-tidb-tikv:NoSchedule
      min_size: 0
      name: wap-tidb-tikv
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.3500eph
      max_size: 0
      metadata:
        node_labels: dedicated=wap-tidb-pd
        node_taints: dedicated=wap-tidb-pd:NoSchedule
      min_size: 0
      name: wap-tidb-pd
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.16vcpu.128mem.0ssd.500eph
      max_size: 5
      metadata:
        node_labels: dedicated=wap-coe
        node_taints: dedicated=wap-coe:NoSchedule
      min_size: 5
      name: wap-coe
      type: worker
      volume_type: xlarge-data-wap
    - enable_server_group: false
      flavor: kubed-wap.gl.32vcpu.256mem.0ssd.0eph
      max_size: 7
      metadata:
        node_labels: dedicated=wap-udp-glaucus
        node_taints: dedicated=wap-udp-glaucus:NoSchedule
      min_size: 7
      name: wap-udp-glaucus
      type: worker
      volume_size: 500
      volume_type: xlarge-data-wap
    - egress_network_id: 2cf0f987-f51a-4e16-b4b5-7dbad425b1b8
      enable_server_group: false
      flavor: kubed-wap.gl.8vcpu.16mem.0ssd.0eph
      max_size: 24
      metadata:
        node_labels: type=cilium-egress,infra.webex.com/egress-type=public,pool-name=cilium-pubgw
        node_taints: dedicated=cilium-pubgw:NoSchedule
      min_size: 24
      name: cilium-pubgw
      type: cilium-egress
ocp_ownership_business_service: WBX3 Platform - Meetings
ocp_ownership_component: wxkb
ocp_ownership_server_type: wxkbsvr
ocp_ownership_support_group: wbx3-prod
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pipeline_bundles:
    - platform/post-provision.yaml
    - platform/openstack-cloud-controller.yaml
    - platform/base-apps.yaml
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet: **********/16
pod_subnet_pool: pods_2
pod_subnet_size: 16
provisioning_extra_args: ""
provisioning_module_version: master
route_reflector_name: jfk02-vlan684-rr
s3_bucket_path: terraform-state/cluster-wjfkgen-p-2.tfstate
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
security_groups:
    istio_public:
        rules:
            - cidrs:
                - vpc
              from: 8443
              name: https
              protocol: tcp
              to: 8443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: healthcheck
              protocol: tcp
              to: 15021
              type: ingress
            - cidrs:
                - ***********/23
              from: 443
              name: lb-tcp-443
              protocol: tcp
              to: 443
              type: ingress
            - cidrs:
                - ***********/23
              from: 15020
              name: lb-tcp-15020
              protocol: tcp
              to: 15020
              type: ingress
            - cidrs:
                - ***********/23
              from: 15021
              name: lb-tcp-15021
              protocol: tcp
              to: 15021
              type: ingress
            - cidrs:
                - ***********/23
              from: 4000
              name: lb-tcp-4000
              protocol: tcp
              to: 4000
              type: ingress
    strimzi_kafka_public:
        rules:
            - cidrs:
                - vpc
              from: 15021
              name: stm-15021
              protocol: tcp
              to: 15021
              type: ingress
            - cidrs:
                - ***********/23
              from: 9092
              name: stm-9092
              protocol: tcp
              to: 9092
              type: ingress
            - cidrs:
                - ***********/23
              from: 10001
              name: stm-10001-10049
              protocol: tcp
              to: 10049
              type: ingress
            - cidrs:
                - ***********/23
              from: 443
              name: stm-443
              protocol: tcp
              to: 443
              type: ingress
            - cidrs:
                - ***********/23
              from: 15020
              name: stm-15020
              protocol: tcp
              to: 15020
              type: ingress
            - cidrs:
                - ***********/23
              from: 15021
              name: stm-15021
              protocol: tcp
              to: 15021
              type: ingress
server_group_policies:
    - anti-affinity
status: online
terraform_version: v1.10.4
thousand_eyes_count: 0
use_floating_ips: false
use_oidc: false
use_provider_template: true
volume_storage: true
vpc_name: public-network
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph

#~~ No Blueprint Values


#~~ No Environment Values


#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: clusters/wjfkgen-p-2
    name: wjfkgen-p-2
    workflow: standard
dir: clusters
external: false
include_defaults: true
manifest_path: manifest.yaml
release: v1.8.1
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: v7.19.8-next
    templateversion: v7.19.8-next
workflow: standard