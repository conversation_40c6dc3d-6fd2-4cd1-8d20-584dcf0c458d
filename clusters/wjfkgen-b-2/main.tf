# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.44
# template-version: v7.19.8-next
# module-version: v10.16.15
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wjfkgen-b-2.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
      source = "terraform-provider-openstack/openstack"
    }
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/cluster-wjfkgen-b-2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/capi-infra?ref=v10.16.15"
  providers = {
    aws = aws
    aws.pub-dns = aws.pub-dns
    openstack = openstack
  }
  admin_port = 6443
  availability_zones = [
    {
      compute = "az1"
      volume = "az1"
    }
  ]
  aws_infra_region = "us-east-1"
  bastion_count = 0
  bastion_flavor = "gv.2vcpu.4mem.0ssd.0eph"
  command_and_control = "mccprod"
  control_plane_network_type = "prv_kubed_network"
  dev_cluster = false
  domain = "prod.infra.webex.com"
  enable_lb_port_security = false
  force_delete = true
  image_name = "wbx3-capi-jammy-1.31.5-c8d-amd64-v1.29.6"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  internal_network_type = "iso_network"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  name = "wjfkgen-b-2"
  network_mission_tag = "meetings"
  network_name = "w-jfk02-a0-0-a1"
  node_pools = [
    {
      flavor = "kubed.gv.16vcpu.64mem.0ssd.0eph"
      max_size = 5
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 5
      name = "worker"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      cluster_security_groups = [
        {
          name = "istio_public"
        }
      ]
      flavor = "kubed.gv.8vcpu.32mem.0ssd.0eph"
      max_size = 8
      metadata = {
        node_labels = "type=worker,dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 8
      name = "istio-ingress"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.16vcpu.64mem.0ssd.0eph"
      max_size = 15
      metadata = {
        node_labels = "dedicated=wap-micro-services"
        node_taints = "dedicated=wap-micro-services:NoSchedule"
      }
      min_size = 15
      name = "wap-micro-services"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      cluster_security_groups = [
        {
          name = "strimzi_kafka_public"
        }
      ]
      flavor = "kubed-wap.gl.16vcpu.128mem.0ssd.0eph"
      max_size = 2
      metadata = {
        node_labels = "dedicated=wap-strimzi-kafka-provider-gateway"
        node_taints = "dedicated=wap-strimzi-kafka-provider-gateway:NoSchedule"
      }
      min_size = 2
      name = "strimzi-kafka-gateway"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.8vcpu.64mem.0ssd.0eph"
      max_size = 6
      metadata = {
        node_labels = "dedicated=wap-kafka"
        node_taints = "dedicated=wap-kafka:NoSchedule"
      }
      min_size = 6
      name = "wap-pool-kafka"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.8vcpu.64mem.0ssd.3500eph"
      max_size = 4
      metadata = {
        node_labels = "dedicated=wap-kafkakbr"
        node_taints = "dedicated=wap-kafkakbr:NoSchedule"
      }
      min_size = 4
      name = "wap-pool-kafkakbr"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.16vcpu.64mem.0ssd.0eph"
      max_size = 3
      metadata = {
        node_labels = "dedicated=wap-trino"
        node_taints = "dedicated=wap-trino:NoSchedule"
      }
      min_size = 3
      name = "wap-pool-trino"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.16vcpu.128mem.0ssd.3500eph"
      max_size = 3
      metadata = {
        node_labels = "dedicated=wap-starrocks"
        node_taints = "dedicated=wap-starrocks:NoSchedule"
      }
      min_size = 3
      name = "wap-starrocks"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.16vcpu.128mem.0ssd.3500eph"
      max_size = 3
      metadata = {
        node_labels = "dedicated=wap-pinot-server"
        node_taints = "dedicated=wap-pinot-server:NoSchedule"
      }
      min_size = 3
      name = "wap-pinot-server"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.16vcpu.128mem.0ssd.500eph"
      max_size = 3
      metadata = {
        node_labels = "dedicated=wap-pinot-minion"
        node_taints = "dedicated=wap-pinot-minion:NoSchedule"
      }
      min_size = 3
      name = "wap-pinot-minion"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.32vcpu.160mem.0ssd.0eph"
      max_size = 15
      metadata = {
        node_labels = "dedicated=wap-udp-job"
        node_taints = "dedicated=wap-udp-job:NoSchedule"
      }
      min_size = 15
      name = "wap-pool-udp-job"
      type = "worker"
      volume_size = 500
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.16vcpu.128mem.0ssd.500eph"
      max_size = 0
      metadata = {
        node_labels = "dedicated=wap-tidb-tidb"
        node_taints = "dedicated=wap-tidb-tidb:NoSchedule"
      }
      min_size = 0
      name = "wap-tidb-tidb"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.16vcpu.128mem.0ssd.3500eph"
      max_size = 0
      metadata = {
        node_labels = "dedicated=wap-tidb-tikv"
        node_taints = "dedicated=wap-tidb-tikv:NoSchedule"
      }
      min_size = 0
      name = "wap-tidb-tikv"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      flavor = "kubed-wap.gl.16vcpu.128mem.0ssd.500eph"
      max_size = 5
      metadata = {
        node_labels = "dedicated=wap-coe"
        node_taints = "dedicated=wap-coe:NoSchedule"
      }
      min_size = 5
      name = "wap-coe"
      type = "worker"
      volume_type = "xlarge-data-wap"
    },
    {
      egress_network_id = "2cf0f987-f51a-4e16-b4b5-7dbad425b1b8"
      flavor = "kubed-wap.gl.8vcpu.16mem.0ssd.0eph"
      max_size = 4
      metadata = {
        node_labels = "type=cilium-egress,infra.webex.com/egress-type=public,pool-name=cilium-pubgw"
        node_taints = "dedicated=cilium-pubgw:NoSchedule"
      }
      min_size = 4
      name = "cilium-pubgw"
      server_group_policy = "anti-affinity"
      type = "cilium-egress"
    }
  ]
  ocp_ownership_business_service = "WBX3 Platform - Meetings"
  ocp_ownership_component = "wxkb"
  ocp_ownership_server_type = "wxkbsvr"
  ocp_ownership_support_group = "wbx3-prod"
  pod_subnet = "**********/18"
  security_groups = {
    istio_public = {
      rules = [
        {
          cidrs = [
            "vpc"
          ]
          from = 8443
          name = "https"
          protocol = "tcp"
          to = 8443
          type = "ingress"
        },
        {
          cidrs = [
            "vpc"
          ]
          from = 15021
          name = "healthcheck"
          protocol = "tcp"
          to = 15021
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 443
          name = "lb-tcp-443"
          protocol = "tcp"
          to = 443
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 15020
          name = "lb-tcp-15020"
          protocol = "tcp"
          to = 15020
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 15021
          name = "lb-tcp-15021"
          protocol = "tcp"
          to = 15021
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 4000
          name = "lb-tcp-4000"
          protocol = "tcp"
          to = 4000
          type = "ingress"
        }
      ]
    }
    strimzi_kafka_public = {
      rules = [
        {
          cidrs = [
            "vpc"
          ]
          from = 15021
          name = "stm-15021"
          protocol = "tcp"
          to = 15021
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 9092
          name = "stm-9092"
          protocol = "tcp"
          to = 9092
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 10001
          name = "stm-10001-10047"
          protocol = "tcp"
          to = 10047
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 443
          name = "stm-443"
          protocol = "tcp"
          to = 443
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 15020
          name = "stm-15020"
          protocol = "tcp"
          to = 15020
          type = "ingress"
        },
        {
          cidrs = [
            "***********/23"
          ]
          from = 15021
          name = "stm-15021"
          protocol = "tcp"
          to = 15021
          type = "ingress"
        }
      ]
    }
  }
  
}
output "control_plane_server_group_id" {
  value = module.infra.control_plane_server_group_id
}
output "control_plane_server_group_name" {
  value = module.infra.control_plane_server_group_name
}
output "fixed_ip" {
  value = module.infra.fixed_ip
}
output "hosted_zone_id" {
  value = module.infra.hosted_zone_id
}
output "hosted_zone_name" {
  value = module.infra.hosted_zone_name
}
output "nodepool_server_group_ids" {
  value = module.infra.nodepool_server_group_ids
}
output "nodepool_server_group_names" {
  value = module.infra.nodepool_server_group_names
}

