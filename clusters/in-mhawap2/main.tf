# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.33
# template-version: 7.18.33
# module-version: v10.9.0
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}
data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}
data "vault_generic_secret" "aws_s3_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

# Infra AWS Provider
provider "aws" {
  access_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = "ap-south-1"
  use_fips_endpoint = false
}

# DNS AWS provider
provider "aws" {
  alias             = "dns"
  access_key        = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
  use_fips_endpoint = false
}

provider "aws" {
  alias             = "s3"
  access_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key        = data.vault_generic_secret.aws_s3_credential.data["AWS_SECRET_ACCESS_KEY"]
  region            = "us-east-2"
  use_fips_endpoint = false
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-in-mhawap2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/cluster-in-mhawap2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/capi-infra?ref=v10.9.0"
  providers = {
    aws = aws
    aws.dns = aws.dns
    aws.s3 = aws.s3
  }
  apiserver_record_public = true
  bastion_count = 1
  bastion_flavor = "t3a.small"
  bastion_image_name = "wbx3-capi-focal-1.25.6-containerd-v1.26.6"
  command_and_control = "mccprod"
  local_dns_enabled = true
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16"
  ]
  name = "in-mhawap2"
  network_name = "a-apso1-p0-0-ha2"
  security_groups = {
    istio_private = {
      rules = [
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          from = 443
          name = "https"
          protocol = "tcp"
          to = 443
          type = "ingress"
        },
        {
          cidrs = [
            "vpc"
          ]
          from = 15021
          name = "healthcheck"
          protocol = "tcp"
          to = 15021
          type = "ingress"
        },
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          from = 80
          name = "http"
          protocol = "tcp"
          to = 80
          type = "ingress"
        }
      ]
      tags = [
        {
          key = "kubernetes.io/cluster/in-mhawap2"
          value = "owned"
        }
      ]
    }
    istio_public = {
      rules = [
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          from = 443
          name = "https"
          protocol = "tcp"
          to = 443
          type = "ingress"
        },
        {
          cidrs = [
            "vpc"
          ]
          from = 15021
          name = "healthcheck"
          protocol = "tcp"
          to = 15021
          type = "ingress"
        },
        {
          cidrs = [
            "0.0.0.0/0"
          ]
          from = 80
          name = "http"
          protocol = "tcp"
          to = 80
          type = "ingress"
        }
      ]
      tags = [
        {
          key = "kubernetes.io/cluster/in-mhawap2"
          value = "owned"
        }
      ]
    }
  }
  vpc_mission_tag_app = "wap"
  zone_type = "public"
  
}
output "admin_private_dns" {
  value = module.infra.admin_private_dns
}
output "apiserver_ingress_nlb_dns" {
  value = module.infra.apiserver_ingress_nlb_dns
}
output "apiserver_lb_sg_id" {
  value = module.infra.apiserver_lb_sg_id
}
output "apiserver_private_nlb_dns" {
  value = module.infra.apiserver_private_nlb_dns
}
output "aws_account_id" {
  value = module.infra.aws_account_id
}
output "aws_kms_key_id" {
  value = module.infra.aws_kms_key_id
}
output "aws_region" {
  value = module.infra.aws_region
}
output "bastion_private_dns" {
  value = module.infra.bastion_private_dns
}
output "cert_manager_delegated_role_arn" {
  value = module.infra.cert_manager_delegated_role_arn
}
output "control_plane_sg_id" {
  value = module.infra.control_plane_sg_id
}
output "iso_dns_zone_id" {
  value = module.infra.iso_dns_zone_id
}
output "iso_zone_name" {
  value = module.infra.iso_zone_name
}
output "lb_sg_id" {
  value = module.infra.lb_sg_id
}
output "node_sg_id" {
  value = module.infra.node_sg_id
}
output "prv_dns_zone_id" {
  value = module.infra.prv_dns_zone_id
}
output "prv_zone_name" {
  value = module.infra.prv_zone_name
}
output "pub_dns_zone_id" {
  value = module.infra.pub_dns_zone_id
}
output "pub_zone_name" {
  value = module.infra.pub_zone_name
}

