# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  /
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/
#
#
# codegen-version: v7.10.12
# template-version: EXPERIMENTAL
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
  path=replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "aws_infra_credential" {
  path=replace("secret/data/mccprod/infra/lma/int/aws-lma-wbx3", "/data/", "/")
}



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-aiadlogint-agg1.tfstate"
    region = "us-east-2"
  }
}



variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "us-east-1"
}

# Infra AWS Provider
provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = var.AWS_INFRA_REGION
  max_retries = 3
}

# DNS AWS provider
provider "aws" {
  alias = "dns"
  access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
  max_retries = 3
 }


data "terraform_remote_state" "network" {
  backend = "s3"
  config = {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_agglog-int-net-aiad-1a.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = string
  default = "wbx3-bionic-1.21.5-361_4bca3d2"
}

module "k8s" {
  source       = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/k8s?ref=v7.10.19.2-patch"
  domain       = "prod.infra.webex.com"
  cluster-name = "aiadlogint-agg1"
  vpc-name = "wbx3-agglog-maz-int-aiad"
  image-name   = var.image-name
  scale-image-name = "wbx3-focal-1.21.5-docker-lma-patch"
  vault-path = "ssh/k8s/aiadlogint-agg1/config/ca"
  controller-config-path = "secret/mccprod/cloud-controller/aiadlogint-agg1/cloud"
  join-token-vault-path = "secret/mccprod/kubernetes/aiadlogint-agg1/kubeadm"
  kubeadmin-vault-path = "secret/mccprod/kubernetes/aiadlogint-agg1/kubeadmin"
  qualys-vault-path = "secret/mccprod/global/qualys-cloud-agent"

  providers = {
    aws.dns = aws.dns
    aws.pub-dns = aws.dns
  }

  # AWS infra credential passthrough
  aws_infra_credential = data.vault_generic_secret.aws_infra_credential

  # Masters
  master_count  = 3
  master_flavor = "m5d.2xlarge"

  # Workers
  worker_count  = 7
  worker_flavor = "c5.4xlarge"

  # Ingress
  ingress_count  = 2
  ingress_flavor = "c5n.2xlarge"

  # Ingress Int
  ingress_int_count  = 2
  ingress_int_flavor = "c5n.2xlarge"

  # Bastion
  bastion_count  = 1
  bastion_flavor = "m5a.large"

  # cidr ranges
  cidr-k8s-pods = "**********/13"
  cidr-k8s-svcs = "172.24.0.0/13"
  cidr-node-prefix = "27"

  gateway-name = "not-used"
  network-name = try(data.terraform_remote_state.network.outputs.name, "")
  
  # External media security group rules
  custom-external-media-sg-rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]
  
  # Ingress security group rules
  custom-ingress-sg-rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]
  




  optimized-storage-node-count = 3
  optimized-storage-flavor = "i3.2xlarge"


  internal-media-node-flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"


  internal-mini-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"


  external-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  health-checks = false
  dev-cluster = false
  aws-az = "us-east-1a"
  network-names = [
    "agglog-int-net-aiad-1a","agglog-int-net-aiad-1b","agglog-int-net-aiad-1c",
  ]
  aws-az-list = [
    "us-east-1a","us-east-1b","us-east-1c",
  ]

  nat-gateway  = true
  vpc-routing  = true


  create-eip = true


  mgmt-ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]

  https-internal-ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "**********/14",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "***********/16",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
    "*************/17",
    "**********/16",
    "**********/16",
    "*************/20",
    "*************/20",
    "***********/20",
    "************/19",
    "*************/19",
    "***********/20",
    "***********/20",
    "*********/16",
    "************/32",
    "*************/32",
    "*************/32",
    "*************/32",
    "*************/32",
    "*************/32",
    "*************/32",
    "**************/32",
    "*************/32",
    "**************/32",
    "***********/32",
    "***********/16"
  ]








  node-pools = [
    {
      name = "agglog-general"
      type = "worker"
      image = "wbx3-bionic-1.21.5-361_4bca3d2"
      min_size = 3
      max_size = 50
      flavor = "c5.4xlarge"
      cpu = "8000m"
      memory = "12Gi"
      volume_size = "64"
      volume_type = "io1"
      volume_iops = 1000
      use_spot_instances = false
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      metadata = {
        node_labels = "pool-name=agglog-general"
      }

    },
    {
      name = "agglog-prometheus"
      type = "worker"
      image = "wbx3-focal-1.21.5-docker-lma-202504-p"
      min_size = 0
      max_size = 1
      flavor = "c5.4xlarge"
      cpu = "8000m"
      memory = "12Gi"
      volume_size = "64"
      volume_type = "gp3"
      volume_iops = 1000
      use_spot_instances = false
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      metadata = {
        node_labels = "pool-name=agglog-prometheus"
      }

    },
    {
      name = "agglog-logstash"
      type = "worker"
      image = "wbx3-bionic-1.21.5-361_4bca3d2-pro-202505"
      min_size = 5
      max_size = 50
      flavor = "c5.4xlarge"
      cpu = "8000m"
      memory = "12Gi"
      volume_size = "64"
      volume_type = "io1"
      volume_iops = 1000
      use_spot_instances = false
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      metadata = {
        node_labels = "pool-name=agglog-logstash"
      }

    },
    {
      name = "agglog-loki"
      type = "worker"
      image = "wbx3-focal-1.21.5-docker-lma-pro-202505"
      min_size = 2
      max_size = 50
      flavor = "c5.4xlarge"
      cpu = "8000m"
      memory = "12Gi"
      volume_size = "64"
      volume_type = "gp3"
      volume_iops = 1000
      use_spot_instances = false
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      metadata = {
        node_labels = "pool-name=agglog-loki"
      }

    },
    {
      name = "agglog-loki-sbgcspe"
      type = "worker"
      image = "wbx3-bionic-1.21.5-361_4bca3d2"
      min_size = 2
      max_size = 50
      flavor = "c5.4xlarge"
      cpu = "8000m"
      memory = "12Gi"
      volume_size = "64"
      volume_type = "io1"
      volume_iops = 1000
      use_spot_instances = false
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      metadata = {
        node_labels = "pool-name=agglog-loki-sbgcspe"
      }

    },
    {
      name = "agglog-loki-sbgcost"
      type = "worker"
      image = "wbx3-jammy-1.21.5-docker-lma-patch-202506"
      min_size = 2
      max_size = 50
      flavor = "c5.4xlarge"
      cpu = "8000m"
      memory = "12Gi"
      volume_size = "64"
      volume_type = "gp3"
      volume_iops = 1000
      use_spot_instances = false
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      metadata = {
        node_labels = "pool-name=agglog-loki-sbgcost"
      }

    },
    {
      name = "agglog-splunk"
      type = "worker"
      image = "wbx3-jammy-1.21.5-docker-lma-patch-202506"
      min_size = 3
      max_size = 50
      flavor = "c5.4xlarge"
      cpu = "8000m"
      memory = "12Gi"
      volume_size = "64"
      volume_type = "io1"
      volume_iops = 1000
      use_spot_instances = false
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      metadata = {
        node_labels = "pool-name=agglog-splunk"
      }

    },
    {
      name = "agglog-splunk-ci"
      type = "worker"
      min_size = 3
      max_size = 50
      flavor = "i3.2xlarge"
      cpu = "8000m"
      memory = "12Gi"
      volume_size = "64"
      volume_type = "io1"
      volume_iops = 1000
      use_spot_instances = false
      spot_allocation_strategy = "lowest-price"
      spot_instance_pools = 2
      spot_max_price = ""
      metadata = {
        node_labels = "pool-name=agglog-splunk-ci"
      }

    },

  ]

  external-media-pool-max = 0
}

output "cluster_pod_cidr" {
  value = module.k8s.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.k8s.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.k8s.cluster_node_prefix
}

output "vpc_routing" {
  value = module.k8s.vpc_routing
}

output "gateway_hash" {
  value = module.k8s.gateway_hash
}

output "gateway_ips" {
  value = module.k8s.gateway_ips
}

output "cluster_hosted_zone" {
  value = module.k8s.cluster_hosted_zone
}

