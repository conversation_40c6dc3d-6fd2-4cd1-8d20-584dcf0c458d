# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.2
# template-version: v7.19.8-next
# module-version: v10.2.14-provider-fix
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/az-west-eu/azure
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-mamsm-b-6.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/azure/cluster?ref=49235ba278fe8fceefc36b4343d4d9a810d185e1"
  azure_cert_vault_name = "prod-westeu-cert"
  azure_vault_name = "prod-westeu-keyvault"
  azurerm_image_shared_version = {
    gallery_name = "WBX3ImageGallery"
    image_name = "WBX3ImageFocal"
    name = "2024.01.26"
    resource_group_name = "packertest"
  }
  azurerm_scale_shared_image_version = {
    gallery_name = "WBX3ImageGallery"
    image_name = "WBX3ImageJammy"
    name = "202506.10.37"
    resource_group_name = "packertest"
  }
  azurerm_windows_shared_image_version = {
    gallery_name = "WBX3ImageGallery"
    image_name = "WBX3ImageWindows"
    name = "202506.26.28"
    resource_group_name = "packertest"
  }
  bastion_count = 1
  bastion_flavor = "Standard_B2s"
  cidr_node_prefix = 26
  cidr_pods = "auto"
  cidr_svcs = "auto"
  command_and_control = "mccprod"
  datacenter = "westeurope"
  domain = "prod.infra.webex.com"
  eip_pool_id = "public-ip-sub-b"
  env_name = "az-west-eu"
  external_media_node_count = 0
  external_media_node_flavor = "Standard_F8s_v2"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  external_network = "public-0"
  health_checks = true
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  image_name = "/subscriptions/09e8097f-3300-4dbd-a5ff-7a28921a4bf2/resourceGroups/packertest/providers/Microsoft.Compute/images/wbx3-bionic-hardened-1"
  ingress_count = 2
  ingress_flavor = "Standard_F8s_v2"
  ingress_int_count = 1
  ingress_int_flavor = "Standard_F8s_v2"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_count = 0
  internal_media_node_flavor = "Standard_F32s_v2"
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  internal_network = "wbx3-westeurope-1"
  master_count = 3
  master_flavor = "Standard_F8s_v2"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  name = "mamsm-b-6"
  node_pools = [
    {
      cpu = "32500m"
      flavor = "Standard_F32s_v2"
      max_size = 5
      memory = "62Gi"
      min_size = 0
      name = "intcanb6"
      type = "internal-media"
    },
    {
      cpu = "8500m"
      flavor = "Standard_F8s_v2"
      max_size = 5
      memory = "12Gi"
      min_size = 0
      name = "extcanb6"
      type = "external-media"
    },
    {
      cpu = "8500m"
      flavor = "Standard_F8s_v2"
      max_size = 5
      memory = "12Gi"
      min_size = 0
      name = "worker-pool"
      type = "worker"
    },
    {
      cpu = "8500m"
      flavor = "Standard_F8s_v2"
      max_size = 4
      memory = "12Gi"
      metadata = {
        dns_prefix = "windows-media"
        node_labels = "type=windows-media-node"
      }
      min_size = 0
      name = "wcanb6"
      type = "windows-media"
    }
  ]
  optimized_storage_count = 3
  optimized_storage_flavor = "Standard_DS4_v2"
  run_windows = true
  thousand_eyes_node_count = 0
  thousand_eyes_node_flavor = "Standard_F8s_v2"
  use_floating_ips = false
  vpc_name = "wbx3-westeurope"
  windows_image = "/subscriptions/09e8097f-3300-4dbd-a5ff-7a28921a4bf2/resourceGroups/packertest/providers/Microsoft.Compute/images/wbx3-windows-1903-base-16"
  windows_media_node_count = 0
  windows_media_node_flavor = "Standard_F8s_v2"
  windows_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 8445
      name = "msteams-media"
      protocol = "tcp"
      to = 8446
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9445
      name = "msteams-signalling"
      protocol = "tcp"
      to = 9446
      type = "ingress"
    }
  ]
  worker_count = 3
  worker_flavor = "Standard_F8s_v2"
  
}
output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}
output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}
output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}
output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}


