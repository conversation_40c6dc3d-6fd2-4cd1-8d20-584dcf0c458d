# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.2
# template-version: EXPERIMENTAL
# module-version: v7.23.4
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wfram-a-3.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/cluster?ref=v7.23.4"
  bastion_block_storage = true
  bastion_count = 1
  bastion_flavor = "kubed.gv.2vcpu.4mem.0ssd.0eph"
  cidr_node_prefix = 26
  cidr_pods = "auto"
  cidr_svcs = "auto"
  cloud_service_provider = "openstack"
  command_and_control = "mccprod"
  dev_cluster = false
  domain = "prod.infra.webex.com"
  external_media_node_count = 0
  external_media_node_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  external_network = "Pub-Calliope-Telephony-cmr4-423"
  force_delete = true
  gateway_name = "wfra01-ocp4-prod-media"
  health_checks = true
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  image_name = "wbx3-focal-1.23.5-containerd-8800b2a"
  infra_credentials_path = "secret/data/mccprod/infra/wfra-meet-prod/openstack"
  ingress_block_storage = true
  ingress_count = 3
  ingress_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_int_block_storage = true
  ingress_int_count = 1
  ingress_int_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_count = 0
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"
  internal_mini_media_node_count = 0
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  internal_network = "wfra01-ocp4-prod-media"
  internal_provider_network = "Prv-Calliope-Telephony-cmr4-415"
  internal_provider_subnets = [
    "10.0.0.0/8"
  ]
  management_network = "Prv-Calliope-Telephony-cmr4-415"
  master_block_storage = true
  master_count = 3
  master_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  name = "wfram-a-3"
  node_pools = [
    {
      flavor = "kubed.gv.16vcpu.128mem.80ssd.0eph"
      max_size = 3
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
      min_size = 3
      name = "optimized-storage"
      root_block_storage = false
      type = "worker"
    },
    {
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 4
      metadata = {
        node_labels = "pool-name=worker,type=worker"
      }
      min_size = 4
      name = "worker"
      root_block_storage = true
      type = "worker"
    },
    {
      flavor = "Spark-Media.8vcpu.16mem.0ssd.0eph.numa"
      max_size = 4
      media_network_name = "Prv-Calliope-Telephony-cmr4-422"
      min_size = 4
      name = "injector-pool"
      root_block_storage = true
      type = "internal-mini-media"
    },
    {
      flavor = "PROD-Spark-Media.47vcpu.64mem.0ssd.0eph.numa"
      max_size = 27
      media_network_name = "Prv-Calliope-Telephony-cmr4-422"
      min_size = 27
      name = "internal-media-pool"
      root_block_storage = true
      type = "internal-media"
    },
    {
      flavor = "Spark-Media.8vcpu.16mem.0ssd.0eph.numa"
      max_size = 156
      min_size = 156
      name = "external-media-pool"
      root_block_storage = true
      type = "external-media"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  server_group_policies = [
    "anti-affinity"
  ]
  thousand_eyes_node_count = 0
  thousand_eyes_node_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  use_floating_ips = false
  worker_block_storage = true
  worker_count = 1
  worker_eth1_network = "Prv-Calliope-Telephony-cmr4-422"
  worker_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  
}

output "aws_zone_id" {
  value = module.infra.aws_zone_id
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "ingress_address" {
  value = module.infra.ingress_address
}
