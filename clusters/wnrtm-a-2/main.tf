# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.13.8
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wnrtm-a-2.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = string
  default = "wbx3-bionic-1.21.5-383_47ccec7"
}

module "cluster" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git////modules/openstack/cluster?ref=v7.12.0"
  name = "wnrtm-a-2"
  gateway_name = "wnrt-prod-v3231"
  domain = "prod.infra.webex.com"
  ssh_user = "ubuntu"

  image_name = var.image-name
  scale_image_name = "wbx3-bionic-1.21.5-383_47ccec7"
  command_and_control = "mccprod"
  use_floating_ips = false
  external_network = "public-wbx3-3097"
  internal_network = "wnrt-prod-v3231"
  management_network = "private-CCP-3095"
  force_delete = true

  # Bastions
  bastion_count = 1
  bastion_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Masters
  master_count = 3
  master_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Workers
  worker_count = 5
  worker_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Ingresses
  ingress_count = 3
  ingress_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Internal ingresses
  ingress_int_count = 2

  # cidr ranges
  cidr_pods = "************/18"
  cidr_svcs = "***********/22"
  cidr_node_prefix = "27"

  health_checks = true

  dev_cluster = false


  external_media_sg_rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]
  ingress_sg_rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]

  thousand_eyes_node_count = 0
  thousand_eyes_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  optimized_storage_count = 0
  optimized_storage_flavor = "8vcpu.16mem.512ssd.0eph"

  internal_media_node_count = 0
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"


  internal_mini_media_node_count = 0
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"


  external_media_node_count = 0
  external_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"


  internal_provider_network = "private-CCP-3095"
  internal_provider_subnets = [
    "10.0.0.0/8",
  ]

  server_group_policies = [
    "anti-affinity",
  ]

  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
  ]


  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
  ]


  node_pools = [
    {
      name = "optimized-storage"
      type = "worker"
      min_size = 3
      max_size = 3
      flavor = "8vcpu.16mem.512ssd.0eph"
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
    },    {
      name = "homer-pool"
      type = "external-large-media"
      min_size = 61
      max_size = 61
      flavor = "media.nl.12vcpu.32mem.80ssd.0eph"
    },
    {
      max_size = 6
      min_size = 6
      name = "injector-pool"
      type = "internal-mini-media"
    },
    {
      max_size = 100
      min_size = 100
      name = "linus-pool"
      type = "external-media"
    },
    {
      max_size = 41
      min_size = 41
      name = "mygdonus-pool"
      type = "internal-media"
    },
    {
      max_size = 2
      metadata = {
        dns_prefix = "internal-mini-media"
        node_labels = "type=hesiod"
      }
      min_size = 2
      name = "hesiod-pool"
      type = "internal-mini-media"
    },
    {
      flavor = "2vcpu.4mem.80ssd.0eph"
      max_size = 1
      metadata = {
        dns_prefix = "thousand-eyes"
        node_labels = "type=thousand-eyes"
      }
      min_size = 1
      name = "thousand-eyes"
      type = "external-media"
    }
  ]

  # Register with Webex Cloud API Gateway






}

output "ingress_address" {
  value = module.cluster.ingress_address
}

output "aws_zone_id" {
  value = module.cluster.aws_zone_id
}

output "cluster_pod_cidr" {
  value = module.cluster.cluster_pod_cidr
}

# Export the cluster cluster pod cidr
output "cluster_svc_cidr" {
  value = module.cluster.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.cluster.cluster_node_prefix
}

# export the gateway node hash
output "gateway_hash" {
  value = module.cluster.gateway_hash
}

output "cluster_hosted_zone" {
  value = module.cluster.cluster_hosted_zone
}

# export the gateway node hash
output "gateway_ips" {
  value = module.cluster.gateway_ips
}
