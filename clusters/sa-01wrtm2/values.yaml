# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.12
# template-version: v7.18.44-EXPERIMENTAL-c8204df
# module-version: v10.7.17-fix-sg-attachment
# wbx3-infra-version: <nil>
# resource: sa-01wrtm2
# resource-type: cluster
# -----------------------------------------------

#~~ Resource Values
backend: s3
cidr_node_prefix: 26
cidr_pods: auto
cidr_svcs: auto
datacenter: ruh
domain: prod.infra.webex.com
env_name: wruh01-ocp4-prod
external_network: provider-432
health_checks: true
ingress_count: 3
master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
metadata:
    annotations:
        calliope_group: wruhm
        calliope_org: wruhm
        helm3Only: true
        service_block: middle-east
    cluster_type: wxcedge-prod
    deployment_groups:
        - crc-prod-middle-east-ruh
        - wxc-edge-sse-sa-01wrtm2-prod
        - wxc-edge-mse-sa-01wrtm2-prod
        - wxc-edge-sbc-operator-sa-01wrtm2-prod
    platform_release_channel: stable-2
module_commit: e3830b10c2ccbd1aa787df315948895af853e5f1
module_version: v10.7.17-fix-sg-attachment
name: sa-01wrtm2
node_pools:
    - max_size: 9
      min_size: 9
      name: worker-pool
      root_block_storage: true
      type: worker
    - flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
      max_size: 2
      metadata:
        node_labels: type=optimized-storage-node
      min_size: 2
      name: prom-pool
      root_block_storage: true
      type: worker
    - flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
      max_size: 20
      media_network_name: provider-412
      metadata:
        dns_prefix: internal-mini-media
        node_labels: type=hesiod
      min_size: 20
      name: hesiod-pool
      public_network_name: provider-432
      root_block_storage: true
      type: internal-mini-media
    - flavor: kubed-homer.nv.12vcpu.24mem.0ssd.0eph
      max_size: 45
      media_network_name: provider-412
      metadata:
        dns_prefix: xlm
        node_labels: type=external-large-media
      min_size: 45
      name: homer-pool
      public_network_name: provider-432
      root_block_storage: true
      type: external-large-media
    - flavor: kubed-media.nv.42vcpu.64mem.0ssd.0eph
      max_size: 15
      media_network_name: provider-412
      min_size: 15
      name: mygdonus-pool
      root_block_storage: true
      type: internal-media
    - flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
      max_size: 3
      media_network_name: provider-412
      min_size: 3
      name: injector-pool
      root_block_storage: true
      type: internal-mini-media
    - flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
      max_size: 70
      media_network_name: provider-412
      metadata:
        dns_prefix: external-media
        node_labels: type=linus
      min_size: 70
      name: linus-pool
      public_network_name: provider-432
      root_block_storage: true
      type: external-media
    - flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
      max_size: 16
      media_network_name: provider-417
      metadata:
        dns_prefix: sse-a
        node_labels: pool-name=sip-pool-a,type=external-media
      min_size: 16
      name: sip-pool-a
      provider_security_group_names:
        - sse-media
      public_network_name: provider-433
      public_security_group_names:
        - sse-ext
      root_block_storage: true
      type: external-media
    - flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
      max_size: 22
      media_network_name: provider-417
      metadata:
        dns_prefix: mse-a
        node_labels: pool-name=media-pool-a,type=external-media
      min_size: 22
      name: media-pool-a
      provider_security_group_names:
        - mse-media
      public_network_name: provider-433
      public_security_group_names:
        - mse-ext
      root_block_storage: true
      type: external-media
pipeline_bundles:
    - platform/post-provision.yaml
    - platform/openstack-cloud-controller.yaml
    - platform/base-apps.yaml
provisioning_extra_args: kubelet_max_pods=56 container_manager='containerd'
security_groups:
    mse-ext:
        - cidr: 0.0.0.0/0
          from: 19560
          name: mse-media-rtp-udp
          protocol: udp
          to: 65535
          type: ingress
        - cidr: 0.0.0.0/0
          from: 5004
          name: mse-media-multiplexed
          protocol: udp
          to: 5004
          type: ingress
    mse-media:
        - cidr: 10.0.0.0/8
          from: 9443
          name: mse-grpc-tcp-1
          protocol: tcp
          to: 9443
          type: ingress
        - cidr: **********/12
          from: 9443
          name: mse-grpc-tcp-2
          protocol: tcp
          to: 9443
          type: ingress
        - cidr: 10.0.0.0/8
          from: 19560
          name: mse-media-rtp-udp-media-1
          protocol: udp
          to: 65535
          type: ingress
        - cidr: **********/12
          from: 19560
          name: mse-media-rtp-udp-media-2
          protocol: udp
          to: 65535
          type: ingress
    sse-ext:
        - cidr: 0.0.0.0/0
          from: 5061
          name: sse-sip-internal-tcp-3
          protocol: tcp
          to: 5061
          type: ingress
        - cidr: 0.0.0.0/0
          from: 5062
          name: sse-sip-internal-tcp-4
          protocol: tcp
          to: 5062
          type: ingress
        - cidr: 0.0.0.0/0
          from: 8934
          name: sse-sip-internal-tcp-5
          protocol: tcp
          to: 8934
          type: ingress
    sse-media:
        - cidr: 10.0.0.0/8
          from: 5060
          name: sse-sip-internal-udp-1
          protocol: udp
          to: 5060
          type: ingress
        - cidr: **********/12
          from: 5060
          name: sse-sip-internal-udp-2
          protocol: udp
          to: 5060
          type: ingress
        - cidr: 10.0.0.0/8
          from: 5060
          name: sse-sip-internal-tcp-1
          protocol: tcp
          to: 5060
          type: ingress
        - cidr: **********/12
          from: 5060
          name: sse-sip-internal-tcp-2
          protocol: tcp
          to: 5060
          type: ingress
server_group_policies:
    - soft-anti-affinity
service_block: middle-east
status: online

#~~ No Blueprint Values


#~~ Environment Values
admin_port: 6442
aws_infra_az: <replace-me>
base_image: wbx3-focal-1.25.6-containerd-v1.27.0
base_k8s_image: wbx3-focal-1.25.6-containerd-v1.27.0
bastion_block_storage: true
bastion_count: 1
bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
cloud_service_provider: openstack
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
embed_provider_template: true
enable_credential_lookup: true
external_media_node_count: 0
external_media_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
gateway_name: wruh01-ocp4-prod
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - **********/16
image_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
infra_credentials_path: secret/data/mccprod/infra/wruh01-ocp4-prod/openstack
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.12
ingress_block_storage: true
ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_int_block_storage: true
ingress_int_count: 2
ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_count: 0
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_count: 0
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
internal_network: wruh01-ocp4-prod
internal_provider_network: provider-414
internal_provider_subnets:
    - 10.0.0.0/8
location: RUH
management_network: provider-414
master_block_storage: true
master_count: 3
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
ocp_ownership_business_service: WBX3 Platform - Meetings
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 0
optimized_storage_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_module_version: v5.11.10-validation-fix
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.4
thousand_eyes_count: 0
thousand_eyes_node_count: 0
thousand_eyes_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
use_floating_ips: false
use_provider_template: true
volume_storage: true
vpc_name: public-413
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_block_storage: true
worker_count: 1
worker_eth1_network: provider-414
worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: clusters/sa-01wrtm2
    name: sa-01wrtm2
    workflow: standard
dir: clusters
external: false
image_name: wbx3-focal-1.25.6-containerd-v1.27.0
manifest_path: manifests/wruh01-ocp4-prod/sa-01wrtm2/manifest.yaml
module_path: modules/openstack/cluster
network_name: wruh01-ocp4-prod
network_names:
    - wruh01-ocp4-prod
node_pool_capacity:
    external-large-media: 45
    external-media: 108
    internal-media: 15
    internal-mini-media: 23
    worker: 11
release: v1.7.2
s3_bucket_path: terraform-state/cluster-sa-01wrtm2.tfstate
versioningData:
    infractlversion: v7.18.44-EXPERIMENTAL-c8204df
    templateversion: v7.18.44-EXPERIMENTAL-c8204df
workflow: standard