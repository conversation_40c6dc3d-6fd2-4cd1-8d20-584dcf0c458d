# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.12
# template-version: 7.18.12
# module-version: v10.8.9
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

provider "aws" {
  access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
}

data "vault_generic_secret" "infra_credential" {
  path = replace("secret/data/mccprod/infra/wjfk02-ocp4-prod/openstack", "/data/", "/")
}

provider "openstack" {
  # v3
  application_credential_id     = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_ID"], null)
  application_credential_secret = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_SECRET"], null)
  # v2
  tenant_name = try(data.vault_generic_secret.infra_credential.data["OS_TENANT_NAME"], null)
  user_name   = try(data.vault_generic_secret.infra_credential.data["OS_USERNAME"], null)
  password    = try(data.vault_generic_secret.infra_credential.data["OS_PASSWORD"], null)

  # common
  auth_url = data.vault_generic_secret.infra_credential.data["OS_AUTH_URL"]
  region   = data.vault_generic_secret.infra_credential.data["OS_REGION_NAME"]
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wjfkm-a-16.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
      source = "terraform-provider-openstack/openstack"
    }
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/cluster-wjfkm-a-16.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/cluster?ref=v10.8.9"
  providers = {
    aws = aws
    openstack = openstack
  }
  bastion_block_storage = true
  bastion_count = 1
  bastion_flavor = "kubed.gv.2vcpu.4mem.0ssd.0eph"
  cidr_node_prefix = 26
  cidr_pods = "auto"
  cidr_svcs = "auto"
  cloud_service_provider = "openstack"
  command_and_control = "mccprod"
  dev_cluster = false
  domain = "prod.infra.webex.com"
  external_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 443
      name = "external-media-https-v6"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 11000
      name = "external-media-cascade-ports-tcp-v6"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 11000
      name = "external-media-cascade-ports-udp-v6"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 49152
      name = "external-media-rtp-ports-tcp-v6"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 49152
      name = "external-media-rtp-ports-udp-v6"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp-v6"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 5004
      name = "external-media-shared-ports-1-udp-v6"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp-v6"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "::/0"
      from = 9000
      name = "external-media-shared-ports-3-udp-v6"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  external_network = "public-441"
  force_delete = true
  gateway_name = "wjfk02-ocp4-prod-media"
  health_checks = true
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "**********/16"
  ]
  image_name = "wbx3-focal-1.25.6-containerd-v1.27.0"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  ingress_block_storage = true
  ingress_count = 2
  ingress_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_int_block_storage = true
  ingress_int_count = 1
  ingress_int_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  internal_network = "wjfk02-ocp4-prod-media"
  internal_provider_network = "provider-444"
  internal_provider_subnets = [
    "10.0.0.0/8"
  ]
  management_network = "provider-444"
  master_block_storage = true
  master_count = 3
  master_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  name = "wjfkm-a-16"
  node_pools = [
    {
      flavor = "kubed.gv.8vcpu.32mem.0ssd.0eph"
      max_size = 3
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
      min_size = 3
      name = "storage"
      root_block_storage = true
      root_volume_size = 256
      type = "worker"
    },
    {
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 5
      metadata = {
        node_labels = "pool-name=worker,type=worker"
      }
      min_size = 5
      name = "worker"
      root_block_storage = true
      type = "worker"
    },
    {
      flavor = "homer.12vcpu.24mem.0ssd.0eph"
      max_size = 5
      metadata = {
        dns_prefix = "xlm"
        node_labels = "type=external-large-media,infra.webex.com/remove-ds-a-record=true,infra.webex.com/dual-stack-identifier=ds"
      }
      min_size = 5
      name = "homer-pool"
      root_block_storage = true
      type = "external-large-media"
    },
    {
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 10
      metadata = {
        node_labels = "dedicated=crc"
        node_taints = "dedicated=crc:NoSchedule"
      }
      min_size = 10
      name = "crc-pool"
      root_block_storage = true
      type = "worker"
    }
  ]
  ocp_ownership_business_service = "WBX3 Platform - Meetings"
  optimized_storage_count = 0
  optimized_storage_flavor = "8vcpu.16mem.512ssd.0eph"
  server_group_policies = [
    "anti-affinity"
  ]
  use_floating_ips = false
  worker_block_storage = true
  worker_count = 0
  worker_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  
}
output "aws_zone_id" {
  value = module.infra.aws_zone_id
}
output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}
output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}
output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}
output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}
output "gateway_hash" {
  value = module.infra.gateway_hash
}
output "gateway_ips" {
  value = module.infra.gateway_ips
}
output "ingress_address" {
  value = module.infra.ingress_address
}


