# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.43
# template-version: 7.18.43
# module-version: v10.13.2
# wbx3-infra-version: <nil>
# resource: jp-27artm1
# resource-type: cluster
# -----------------------------------------------

#~~ Resource Values
admin_port: 6443
apiserver_record_public: true
aws_infra_region: ap-northeast-3
bastion_count: 0
bastion_flavor: c5.large
bastion_image_name: WBX-Ubuntu-20-x86_64-harden-nonfips-v1.2-202211
cloud_service_provider: aws
cluster_api: true
datacenter: ap-northeast-3
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: b21.prod.infra.webex.com
env_name: a-apne3-p0-0
gateway_name: not-used
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infractl_version: v7.18.43
kubernetes_version: v1.31.5
local_dns_enabled: true
location: KIX
managed_by: mccprod06
master_count: 3
master_flavor: c6i.2xlarge
mesh:
    enabled: true
metadata:
    annotations:
        helm3Only: true
    cluster_type: wxcedge-prod
    deployment_groups:
        - kubed-prod-gen
        - kubed-prod-calling
        - wxc-edge-sse-jp-27artm1-prod
        - wxc-edge-mse-jp-27artm1-prod
        - wxc-edge-sbc-operator-jp-27artm1-prod
    platform_release_channel: stable-1
module_version: v10.13.2
name: jp-27artm1
node_pools:
    - availability_zones:
        - ap-northeast-3a
      cluster_security_groups:
        - name: istio_public
      flavor: c5.2xlarge
      max_size: 70
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 0
      name: istio-ingress-pool-1a
      type: worker
    - availability_zones:
        - ap-northeast-3b
      cluster_security_groups:
        - name: istio_public
      flavor: c5.2xlarge
      max_size: 70
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 0
      name: istio-ingress-pool-1b
      type: worker
    - availability_zones:
        - ap-northeast-3c
      cluster_security_groups:
        - name: istio_public
      flavor: c5.2xlarge
      max_size: 70
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 0
      name: istio-ingress-pool-1c
      type: worker
    - cluster_security_groups:
        - name: istio_private
      flavor: c5.large
      max_size: 10
      metadata:
        node_labels: dedicated=istio-ingress-ciscoint
        node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
      min_size: 0
      name: istio-ingress-cisco
      type: worker
    - max_size: 16
      metadata:
        node_labels: type=worker
      min_size: 3
      name: worker
      type: worker
    - availability_zones:
        - ap-northeast-3a
      flavor: c6i.4xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=thousand-eyes
        node_taints: dedicated=thousand-eyes:NoSchedule
      min_size: 1
      name: thousand-eyes-a
    - availability_zones:
        - ap-northeast-3b
      flavor: c6i.4xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=thousand-eyes
        node_taints: dedicated=thousand-eyes:NoSchedule
      min_size: 1
      name: thousand-eyes-b
    - availability_zones:
        - ap-northeast-3c
      flavor: c6i.4xlarge
      max_size: 1
      metadata:
        node_labels: dedicated=thousand-eyes
        node_taints: dedicated=thousand-eyes:NoSchedule
      min_size: 1
      name: thousand-eyes-c
    - flavor: c6i.2xlarge
      max_size: 5
      metadata:
        node_labels: type=optimized-storage-node
        node_taints: type=optimized-storage-node:NoSchedule
      min_size: 0
      name: optimized-storage
      type: worker
    - availability_zones: null
      flavor: c6i.2xlarge
      max_size: 12
      metadata:
        dns_prefix: sse-a
        node_labels: pool-name=sip-pool-a,type=external-media,infra.webex.com/public-ipv6-addr=2a05-4200-e--5bc-101
        node_taints: type=media-node:NoSchedule
      min_size: 12
      name: sip-pool-a
      type: worker
    - availability_zones: null
      flavor: c6i.2xlarge
      max_size: 12
      metadata:
        dns_prefix: sse-b
        node_labels: pool-name=sip-pool-b,type=external-media,infra.webex.com/public-ipv6-addr=2a05-4200-e--5bc-101
        node_taints: type=media-node:NoSchedule
      min_size: 12
      name: sip-pool-b
      type: worker
    - availability_zones:
        - ap-northeast-3a
      flavor: c6i.2xlarge
      max_size: 1
      metadata:
        dns_prefix: sse-p
        node_labels: pool-name=sip-peering-pool,type=external-media
        node_taints: type=media-node:NoSchedule
      min_size: 1
      name: sip-peering-pool
      type: worker
    - flavor: c6i.2xlarge
      max_size: 16
      metadata:
        dns_prefix: mse-a
        node_labels: pool-name=media-pool-a,type=external-media
        node_taints: type=media-node:NoSchedule
      min_size: 16
      name: media-pool-a
      type: worker
    - flavor: c6i.2xlarge
      max_size: 16
      metadata:
        dns_prefix: mse-b
        node_labels: pool-name=media-pool-b,type=external-media
        node_taints: type=media-node:NoSchedule
      min_size: 16
      name: media-pool-b
      type: worker
    - availability_zones:
        - ap-northeast-3a
        - ap-northeast-3b
      flavor: c6i.2xlarge
      max_size: 2
      metadata:
        dns_prefix: mse-p
        node_labels: pool-name=media-peering-pool,type=external-media
        node_taints: type=media-node:NoSchedule
      min_size: 2
      name: media-peering-pool
      type: worker
    - availability_zones:
        - ap-northeast-3a
      flavor: c6i.2xlarge
      max_size: 2
      metadata:
        dns_prefix: wxc-dhruva-proxy
        node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,type=external-media,deployment=prod
        node_taints: type=media-node:NoSchedule
      min_size: 2
      name: wxc-dhruva-proxy
      type: worker
    - availability_zones:
        - ap-northeast-3a
        - ap-northeast-3b
      flavor: c6i.4xlarge
      max_size: 5
      metadata:
        dns_prefix: wxc-dhruva-antares
        node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,type=external-media,deployment=prod
        node_taints: type=media-node:NoSchedule
      min_size: 5
      name: wxc-dhruva-antares
      type: worker
pipeline_bundles:
    - platform/post-provision.yaml
    - platform/aws-cloud-controller.yaml
    - platform/base-apps.yaml
region: ap
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
security_groups:
    istio_private:
        rules:
            - cidrs:
                - 10.0.0.0/8
              from: 8443
              name: https
              protocol: tcp
              to: 8443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: healthcheck
              protocol: tcp
              to: 15021
              type: ingress
        tags:
            - key: kubernetes.io/cluster/jp-27artm1
              value: owned
    istio_public:
        rules:
            - cidrs:
                - 0.0.0.0/0
              from: 8443
              name: https
              protocol: tcp
              to: 8443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: healthcheck
              protocol: tcp
              to: 15021
              type: ingress
        tags:
            - key: kubernetes.io/cluster/jp-27artm1
              value: owned
status: online
terraform_version: v1.8.5
use_provider_template: true
vpc_mission_tag_app: calling
vpc_routing: true
worker_flavor: c6i.4xlarge
zone_type: public

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
embed_provider_template: true
enable_credential_lookup: true
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2

#~~ Default Values
aws_infra_az: <replace-me>
base_image: <replace-me>
base_k8s_image: <replace-me>
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
dev_cluster: false
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
ingress_count: 3
ingress_flavor: 8vcpu.16mem.80ssd.0eph
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
provisioning_module_version: master
server_group_policies:
    - anti-affinity
thousand_eyes_count: 0
use_floating_ips: false
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12

#~~ Internally Generated Values
atlantis_options:
    dir: clusters/jp-27artm1
    name: jp-27artm1
    workflow: standard
dir: clusters
external: false
image_name: <replace-me>
include_defaults: true
manifest_path: manifests/a-apne3-p0-0/a-apne3-p0-0-ha2/calling/manifest.yaml
module_path: modules/aws/capi-infra
network_name: a-apne3-p0-0-ha2
network_names:
    - a-apne3-p0-0-ha2
node_pool_capacity:
    worker: 307
release: v1.8.1
s3_bucket_path: terraform-state/cluster-jp-27artm1.tfstate
terraform_templates:
    - path: providers.tf.tpl
versioningData:
    infractlversion: 7.18.43
    templateversion: 7.18.43
workflow: standard