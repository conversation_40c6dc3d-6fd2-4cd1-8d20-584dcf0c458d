# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v10.13.6
# wbx3-infra-version: <nil>
# resource: us-vaalog1
# resource-type: cluster
# -----------------------------------------------

#~~ Resource Values
admin_port: 6443
apiserver_record_public: true
aws_infra_region: us-east-1
bastion_count: 0
bastion_flavor: c6a.large
bastion_image_name: wbx3-capi-focal-1.27.12-containerd-2
cloud_service_provider: aws
cluster_api: true
delegate_to_main_account: true
env_name: a-usea1-p4-1
gateway_name: not-used
kubernetes_version: v1.31.5
local_dns_enabled: true
location: IAD
managed_by: us-txccnc1
master_count: 3
master_flavor: c6a.2xlarge
mesh:
    enabled: true
metadata:
    annotations:
        helm3Only: true
    cluster_type: agglog
    deployment_groups:
        - kubed-prod-gen
        - agglog-agg-us
name: us-vaalog1
node_pools:
    - availability_zones:
        - us-east-1a
      cluster_security_groups:
        - name: istio_public
      flavor: c6a.2xlarge
      max_size: 70
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 1
      name: istio-pub-pool-1a
      type: worker
    - availability_zones:
        - us-east-1b
      cluster_security_groups:
        - name: istio_public
      flavor: c6a.2xlarge
      max_size: 70
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 1
      name: istio-pub-pool-1b
      type: worker
    - availability_zones:
        - us-east-1c
      cluster_security_groups:
        - name: istio_public
      flavor: c6a.xlarge
      max_size: 70
      metadata:
        node_labels: dedicated=istio-ingress
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 1
      name: istio-pub-pool-1c
      type: worker
    - cluster_security_groups:
        - name: istio_private
      flavor: c6a.xlarge
      max_size: 6
      metadata:
        node_labels: dedicated=istio-ingress-ciscoint
        node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
      min_size: 0
      name: istio-ingress-cisco
      type: worker
    - availability_zones:
        - us-east-1a
      flavor: c5n.9xlarge
      max_size: 4
      metadata:
        node_labels: type=cilium-egress,infra.webex.com/egress-type=private
        node_taints: dedicated=cilium-egress:NoSchedule
      min_size: 1
      name: egress-prv-1a
      subnet_type: private_media
      type: worker
    - availability_zones:
        - us-east-1b
      flavor: c5n.9xlarge
      max_size: 4
      metadata:
        node_labels: type=cilium-egress,infra.webex.com/egress-type=private
        node_taints: dedicated=cilium-egress:NoSchedule
      min_size: 1
      name: egress-prv-1b
      subnet_type: private_media
      type: worker
    - availability_zones:
        - us-east-1c
      flavor: c5n.9xlarge
      max_size: 4
      metadata:
        node_labels: type=cilium-egress,infra.webex.com/egress-type=private
        node_taints: dedicated=cilium-egress:NoSchedule
      min_size: 1
      name: egress-prv-1c
      subnet_type: private_media
      type: worker
    - max_size: 100
      metadata:
        node_labels: type=worker
      min_size: 1
      name: worker
      type: worker
    - flavor: r6i.2xlarge
      max_size: 5
      metadata:
        node_labels: type=optimized-storage-node
        node_taints: type=optimized-storage-node:NoSchedule
      min_size: 0
      name: optimized-storage
      type: worker
    - flavor: m5.4xlarge
      max_size: 12
      metadata:
        node_labels: pool-name=agglog-general
      min_size: 0
      name: agglog-general
      spot_allocation_strategy: capacity-optimized
      spot_instance_pools: 0
      spot_max_price: ""
      type: worker
      use_spot_instances: true
    - flavor: c5.4xlarge
      max_size: 4
      metadata:
        node_labels: pool-name=agglog-prometheus
      min_size: 0
      name: agglog-prometheus
      spot_allocation_strategy: capacity-optimized
      spot_instance_pools: 0
      spot_max_price: ""
      type: worker
      use_spot_instances: true
    - flavor: c5.4xlarge
      max_size: 200
      metadata:
        node_labels: pool-name=agglog-logstash
      min_size: 0
      name: agglog-logstash
      type: worker
      use_spot_instances: true
    - flavor: m5n.xlarge
      max_size: 3
      metadata:
        node_labels: pool-name=agglog-kafka
      min_size: 0
      name: agglog-kafka
      spot_allocation_strategy: capacity-optimized
      spot_instance_pools: 0
      spot_max_price: ""
      type: worker
      use_spot_instances: true
    - flavor: c5.4xlarge
      max_size: 50
      metadata:
        node_labels: pool-name=agglog-loki
      min_size: 0
      name: agglog-loki
      spot_allocation_strategy: capacity-optimized
      spot_instance_pools: 0
      spot_max_price: ""
      type: worker
      use_spot_instances: true
pipeline_bundles:
    - platform/post-provision.yaml
    - platform/aws-cloud-controller.yaml
    - platform/base-apps.yaml
security_groups:
    istio_private:
        rules:
            - cidrs:
                - 10.0.0.0/8
              from: 8443
              name: https
              protocol: tcp
              to: 8443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: healthcheck
              protocol: tcp
              to: 15021
              type: ingress
        tags:
            - key: kubernetes.io/cluster/us-vaalog1
              value: owned
    istio_public:
        rules:
            - cidrs:
                - 0.0.0.0/0
              from: 8443
              name: https
              protocol: tcp
              to: 8443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: healthcheck
              protocol: tcp
              to: 15021
              type: ingress
        tags:
            - key: kubernetes.io/cluster/us-vaalog1
              value: owned
status: online
use_oidc: true
vpc_mission_tag: logging
vpc_mission_tag_app: logging
worker_flavor: c6a.2xlarge
zone_type: public

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: b70.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/lma/prd/aws-lma-wbx3
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.18.36
module_version: v10.13.6
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.3
use_provider_template: true

#~~ Default Values
address_pools:
    pods_0: **********/15
    pods_1: **********/13
    pods_2: **********/12
    pods_3: **********/14
aws_infra_az: <replace-me>
base_image: <replace-me>
base_k8s_image: <replace-me>
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
dev_cluster: false
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
ingress_count: 3
ingress_flavor: 8vcpu.16mem.80ssd.0eph
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
provisioning_module_version: master
server_group_policies:
    - anti-affinity
thousand_eyes_count: 0
use_floating_ips: false
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12

#~~ Internally Generated Values
atlantis_options:
    dir: clusters/us-vaalog1
    name: us-vaalog1
    workflow: standard
dir: clusters
external: false
image_name: <replace-me>
include_defaults: true
manifest_path: manifests/a-usea1-p4-1/a-usea1-p4-1-ha1/cluster/manifest.yaml
module_path: modules/aws/capi-infra
network_name: a-usea1-p4-1-ha1
network_names:
    - a-usea1-p4-1-ha1
node_pool_capacity:
    worker: 602
release: v1.7.2
s3_bucket_path: terraform-state/cluster-us-vaalog1.tfstate
terraform_templates:
    - path: providers.tf.tpl
versioningData:
    infractlversion: 7.18.36
    templateversion: 7.18.36
workflow: standard