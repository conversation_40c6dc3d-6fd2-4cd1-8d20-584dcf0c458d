# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.16.3 destroy
# template-version: EXPERIMENTAL 
# module-version: v5.5.0
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-aiadm-m-2.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cluster?ref=af96f80a2d1e6270110f004bf79f801d7f7c1730"
  aws_infra_az = "us-east-1a"
  aws_infra_azs = [
    "us-east-1a",
    "us-east-1b",
    "us-east-1c"
  ]
  aws_infra_region = "us-east-1"
  bastion_count = 1
  bastion_flavor = "t3a.large"
  cidr_node_prefix = 27
  cidr_pods = "auto"
  cidr_pods_prefix = 16
  cidr_svcs = "auto"
  cidr_svcs_prefix = 22
  command_and_control = "mccprod"
  dev_cluster = false
  dns_credentials_path = "secret/data/mccprod/infra/route53/credentials"
  domain = "prod.infra.webex.com"
  eip_on_external_media_dx = false
  env_name = "a-useast-1"
  external_media_dx = true
  external_media_node_flavor = "c6i.2xlarge"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  gateway_name = "not-used"
  health_checks = true
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  ingress_count = 2
  ingress_flavor = "c5n.2xlarge"
  ingress_int_count = 1
  ingress_int_flavor = "c5n.2xlarge"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_flavor = "c6i.8xlarge"
  internal_mini_media_node_flavor = "c6i.2xlarge"
  master_count = 3
  master_flavor = "m5ad.2xlarge"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  name = "aiadm-m-2"
  nat_gateway = true
  network_name = "a-useast-1a"
  network_names = [
    "a-useast-1a",
    "a-useast-1b",
    "a-useast-1c"
  ]
  node_pools = [
    {
      max_size = 3
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
      min_size = 3
      name = "optimized-storage-pool"
      suspended_processes = []
      type = "worker"
    },
    {
      max_size = 5
      min_size = 0
      name = "worker-pool"
      suspended_processes = []
      type = "worker"
    },
    {
      max_size = 3
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 1
      name = "istio-ingress-pool"
      suspended_processes = []
      type = "worker"
    },
    {
      flavor = "c6a.2xlarge"
      max_size = 10
      metadata = {
        node_labels = "type=anycast-proxy-node"
        node_taints = "dedicated=anycast-proxy-node:NoSchedule"
      }
      min_size = 3
      name = "anycast-proxy-pool"
      security_groups = {
        int = [
          {
            cidr = "0.0.0.0/0"
            from = 10511
            name = "anycast-proxy-tls"
            protocol = "tcp"
            to = 10511
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 15021
            name = "anycast-proxy-healthcheck"
            protocol = "tcp"
            to = 15021
            type = "ingress"
          }
        ]
      }
      suspended_processes = []
      type = "worker"
    },
    {
      flavor = "c6i.4xlarge"
      max_size = 20
      min_size = 0
      availability_zones = [
        "us-east-1a"
      ]
      name = "homer-pool"
      suspended_processes = []
      type = "external-large-media"
    },
    {
      flavor = "c6i.2xlarge"
      availability_zones = [
        "us-east-1a"
      ]
      max_size = 20
      min_size = 0
      name = "linus-pool"
      suspended_processes = []
      type = "external-media"
    },
    {
      container_manager = "docker"
      flavor = "g4dn.2xlarge"
      gpu_enabled = true
      image = "wbx3-focal-1.23.5-docker-01_59b750a"
      max_size = 10
      metadata = {
        dns_prefix = "internal-mini-media"
        node_labels = "type=argus"
      }
      min_size = 0
      name = "argus-pool"
      type = "internal-mini-media"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "i3.2xlarge"
  scale_image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  thousand_eyes_node_count = 1
  vpc_name = "aiadm3-vpc"
  vpc_routing = true
  worker_count = 1
  worker_flavor = "m5a.2xlarge"
  
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_iam_user_vault_path" {
  value = module.infra.cluster_iam_user_vault_path
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "vpc_routing" {
  value = module.infra.vpc_routing
}
