# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.22
# template-version: 7.18.22
# module-version: v10.8.0
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "aws_pub_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/sbg-pl/aws", "/data/", "/")
}

# Infra AWS Provider
provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = "us-east-1"

  use_fips_endpoint = false
  # default_tags {
  #   tags = {
  #     ApplicationName = var.csb_application_name
  #     CiscoMailAlias  = var.csb_cisco_mail_alias
  #     ResourceOwner   = var.csb_resource_owner
  #     Environment     = var.csb_environment
  #   }
  # }
}

# DNS AWS provider
provider "aws" {
  alias      = "dns"
  access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]

  use_fips_endpoint = false
  # default_tags {
  #   tags = {
  #     ApplicationName = var.csb_application_name
  #     CiscoMailAlias  = var.csb_cisco_mail_alias
  #     ResourceOwner   = var.csb_resource_owner
  #     Environment     = var.csb_environment
  #   }
  # }
}

provider "aws" {
  alias      = "pub-dns"
  access_key = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_DEFAULT_REGION"]

  use_fips_endpoint = false
  # default_tags {
  #   tags = {
  #     ApplicationName = var.csb_application_name
  #     CiscoMailAlias  = var.csb_cisco_mail_alias
  #     ResourceOwner   = var.csb_resource_owner
  #     Environment     = var.csb_environment
  #   }
  # }
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-aiadsbg-a-3.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/cluster-aiadsbg-a-3.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cluster?ref=v10.8.0"
  providers = {
    aws = aws
    aws.dns = aws.dns
    aws.pub-dns = aws.pub-dns
  }
  aws_infra_az = "us-east-1a"
  aws_infra_azs = [
    "us-east-1a",
    "us-east-1b",
    "us-east-1c"
  ]
  aws_infra_region = "us-east-1"
  bastion_count = 1
  bastion_flavor = "c5n.large"
  cidr_node_prefix = 26
  cidr_pods = "auto"
  cidr_svcs = "auto"
  command_and_control = "mccprod"
  create_eip = true
  dev_cluster = false
  domain = "prod.infra.webex.com"
  external_media_node_flavor = "c6i.2xlarge"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  gateway_name = "a-sbg-igw"
  health_checks = true
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "**********/16"
  ]
  image_name = "wbx3-focal-1.27.12-containerd-240814"
  infra_credentials_path = "secret/data/mccprod/infra/sbg-pl/aws"
  ingress_count = 6
  ingress_flavor = "c5n.2xlarge"
  ingress_int_count = 6
  ingress_int_flavor = "c5n.2xlarge"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_flavor = "c6i.8xlarge"
  internal_mini_media_node_flavor = "c6i.8xlarge"
  master_count = 3
  master_flavor = "m5a.2xlarge"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  name = "aiadsbg-a-3"
  nat_gateway = true
  network_name = "a-sbg-pl-1a"
  network_names = [
    "a-sbg-pl-1a",
    "a-sbg-pl-1b",
    "a-sbg-pl-1c"
  ]
  node_pools = [
    {
      flavor = "m5a.2xlarge"
      max_size = 16
      min_size = 3
      name = "worker-pool"
      type = "worker"
    },
    {
      flavor = "c6a.2xlarge"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 6
      name = "istio-ingress-pool"
      type = "worker"
    },
    {
      flavor = "c5a.large"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress-ciscoint"
        node_taints = "dedicated=istio-ingress-ciscoint:NoSchedule"
      }
      min_size = 6
      name = "istio-ingress-cisco"
      type = "worker"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "8vcpu.16mem.512ssd.0eph"
  scale_image_name = "wbx3-focal-1.27.12-containerd-240814"
  vpc_name = "kubed-sbg-pl"
  vpc_routing = true
  worker_count = 0
  worker_flavor = "m5a.2xlarge"
  
}
output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}
output "cluster_iam_user_vault_path" {
  value = module.infra.cluster_iam_user_vault_path
}
output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}
output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}
output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}
output "gateway_hash" {
  value = module.infra.gateway_hash
}
output "gateway_ips" {
  value = module.infra.gateway_ips
}
output "health_events_sqs_queue_url" {
  value = module.infra.health_events_sqs_queue_url
}
output "vpc_routing" {
  value = module.infra.vpc_routing
}

