# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  / 
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /  
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/   
#                                                                                   
#
# codegen-version: 0.2.3-EXPERIMENTAL-fabaad0
# template-version: 0.2.3-EXPERIMENTAL-fabaad0
# -----------------------------------------------


terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-aiadm-e-1.tfstate"
    region = "us-east-2"
  }
}



variable "AWS_ACCESS_KEY_ID" {
  description = "AWS Access Key"
}

variable "AWS_SECRET_ACCESS_KEY" {
  description = "AWS Secret Key"
}

variable "AWS_DEFAULT_REGION" {
  description = "AWS Region"
}

provider "aws" {
  access_key = "${var.AWS_ACCESS_KEY_ID}"
  secret_key = "${var.AWS_SECRET_ACCESS_KEY}"
  region     = "${var.AWS_DEFAULT_REGION}"
}

variable "image-name" {
  type = "string"
  default = "wbx3-bionic-hardened-1_df17d1a"
}
module "k8s" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git?ref=single-az-networking//modules/aws/k8s"
  domain       = "prod.infra.webex.com"
  cluster-name = "aiadm-e-1"
  aws-vpc-name = "meetpaas-prod-1e"
  image-name   = "${var.image-name}"
  vault-path   = "secret/mccprod/infra/a-useast-1e/aiadm-e-1/k8s/ssh/public_key"
  vault-bastion-path   = "secret/mccprod/infra/a-useast-1e/aiadm-e-1/bastion/ssh/public_key"

  # Masters
  master_count  = 3
  master_flavor = "m5ad.2xlarge"

  # Workers
  worker_count  = 6
  worker_flavor = "m5a.2xlarge"

  # Ingress
  ingress_count  = 2
  ingress_flavor = "c5n.2xlarge"

  # Ingress Int
  ingress_int_count  = 2
  ingress_int_flavor = "c5n.2xlarge"

  # Bastion
  bastion_count  = 1
  bastion_flavor = "m5a.large"

  # cidr ranges
  pod_cidr = "************/19"
  svc_cidr = "***********/20"



  optimized-storage-flavor   = "i3.2xlarge"
  internal-media-node-count   = "4"
  internal-media-node-flavor  = "c5.9xlarge"
  external-media-node-count  = "100"
  external-media-node-flavor  = "c5.2xlarge"

  mgmt-ips = [
    "**********/14",    "***********/16",    "************/24",    "***********/24",    "***********/24",    "10.0.0.0/8",    "*************/26",    "*************/23",    "***********/23",    "************/24",    "**********/16",    "**********/14",    "************/22",    "**********/16",    "************/21",    "************/20",    "************/21",    "*************/19",    "**********/16",    "**********/12",    "*************/21",    "***********/20",    "*************/19",
  ]
}
