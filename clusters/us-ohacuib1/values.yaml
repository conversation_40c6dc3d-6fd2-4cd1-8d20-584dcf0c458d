# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: v7.19.8-next
# module-version: v10.16.18
# wbx3-infra-version: <nil>
# resource: us-ohaccpb1
# resource-type: cluster
# -----------------------------------------------

#~~ Resource Values
admin_port: 6443
apiserver_nlb_subnet_type: public
apiserver_record_public: true
assign_eip: true
aws_infra_az: us-east-2a
aws_infra_azs:
    - us-east-2a
    - us-east-2b
    - us-east-2c
aws_infra_region: us-east-2
backend: s3
base_image: <replace-me>
base_k8s_image: <replace-me>
base_os: noble
bastion_count: 1
bastion_flavor: t3a.small
bastion_image_name: wbx3-capi-noble-1.31.5-c8d-amd64-v2.25.5.1-c2p
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
cloud_service_provider: aws
cluster_api: true
cluster_chart_version: 0.13.1
command_and_control: mccprod
delegate_to_main_account: true
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: c10.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
env_name: a-usea2-i3-0
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: not-used
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
image_name: <replace-me>
infra_credentials_path: secret/data/mccprod/infra/286806641839/atlantis-c2p-pre-prod
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infractl_version: v7.19.0
ingress_count: 3
ingress_flavor: 8vcpu.16mem.80ssd.0eph
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
kubernetes_version: v1.31.5
location: CMH
managed_by: us-txccnc2
master_count: 5
master_flavor: c6a.xlarge
mesh:
    enabled: true
metadata:
    annotations:
        helm3Only: true
    c2p: true
    c2p_cloud: pre-prod
    c2p_release: 1.1.0-rc
    cluster_type: generic
    deployment_groups:
        - kubed-prod-gen
        - ccp-preprod-1
    node_labels: CcpAccountId=32bc6b5d-84a0-4df6-9838-fb9a16d4cffb,CcpOrganizationId=9edd5e35-1f5a-4fbc-978d-8f250f3501b0
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
module_path: modules/aws/capi-infra
module_version: v10.16.18
name: us-ohaccpb1
nameservers:
    - <replace-me>
network_name: a-usea2-i3-0-ha1
network_names:
    - a-usea2-i3-0-ha1
nlb_subnet_purpose: service
nlb_vpc_type: ingress
node_pool_capacity:
    worker: 21
node_pools:
    - iam_instance_profile: c2p-pp-cond-node-role
      max_size: 15
      metadata:
        node_labels: type=worker,CcpAccountId=32bc6b5d-84a0-4df6-9838-fb9a16d4cffb,CcpOrganizationId=9edd5e35-1f5a-4fbc-978d-8f250f3501b0
      min_size: 3
      name: worker
      type: worker
    - cluster_security_groups:
        - name: istio-ciscoint
      max_size: 3
      metadata:
        node_labels: dedicated=istio-ingress-ciscoint,CcpAccountId=32bc6b5d-84a0-4df6-9838-fb9a16d4cffb,CcpOrganizationId=9edd5e35-1f5a-4fbc-978d-8f250f3501b0
        node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
      min_size: 1
      name: istio-ciscoint
      type: worker
    - cluster_security_groups:
        - name: istio-public
      max_size: 3
      metadata:
        node_labels: dedicated=istio-ingress,CcpAccountId=32bc6b5d-84a0-4df6-9838-fb9a16d4cffb,CcpOrganizationId=9edd5e35-1f5a-4fbc-978d-8f250f3501b0
        node_taints: dedicated=istio-ingress:NoSchedule
      min_size: 1
      name: istio-public
      type: worker
oidc_client_id: c2p
oidc_issuer_namespace: CiscoCloudPlatform/pre-prod
oidc_issuer_url: https://bootstrap.keeper.cisco.com/v1/CiscoCloudPlatform/pre-prod/identity/oidc
oidc_login_scope: not-applicable
oidc_provider_name: not-applicable
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
pipeline_bundles:
    - platform/post-provision.yaml
    - platform/aws-cloud-controller.yaml
    - platform/base-apps.yaml
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
provisioning_module_version: master
s3_bucket_path: terraform-state/cluster-us-ohaccpb1.tfstate
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
security_groups:
    istio-ciscoint:
        rules:
            - cidrs:
                - 10.0.0.0/8
              from: 443
              name: cisco-https
              protocol: tcp
              to: 443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: health
              protocol: tcp
              to: 15021
              type: ingress
        tags:
            - key: kubernetes.io/cluster/us-ohaccpb1
              value: owned
    istio-public:
        rules:
            - cidrs:
                - 0.0.0.0/0
              from: 443
              name: https
              protocol: tcp
              to: 443
              type: ingress
            - cidrs:
                - vpc
              from: 15021
              name: health
              protocol: tcp
              to: 15021
              type: ingress
        tags:
            - key: kubernetes.io/cluster/us-ohaccpb1
              value: owned
server_group_policies:
    - anti-affinity
status: online
terraform_version: v1.8.5
thousand_eyes_count: 0
use_floating_ips: false
use_oidc: true
use_provider_template: true
vpc_mission_tag_app: teams
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: c6a.2xlarge

#~~ No Blueprint Values


#~~ Environment Values
consul_host: consul.int.mcc01.prod.infra.webex.com

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: clusters/us-ohaccpb1
    name: us-ohaccpb1
    workflow: standard
dir: clusters
external: false
include_defaults: true
manifest_path: manifest.yaml
release: v1.9.1
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: v7.19.8-next
    templateversion: v7.19.8-next
workflow: standard