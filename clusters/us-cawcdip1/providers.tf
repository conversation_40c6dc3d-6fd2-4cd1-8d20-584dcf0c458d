


data "vault_generic_secret" "aws_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "aws_pub_dns_credential" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "infra_credential" {
  path = replace("secret/data/mccprod/infra/wsjc03-ocp4-prod/openstack", "/data/", "/")
}

provider "openstack" {
  # v3
  application_credential_id     = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_ID"], null)
  application_credential_secret = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_SECRET"], null)
  # v2
  tenant_name         = try(data.vault_generic_secret.infra_credential.data["OS_TENANT_NAME"], data.vault_generic_secret.infra_credential.data["OS_PROJECT_NAME"], null)
  user_name           = try(data.vault_generic_secret.infra_credential.data["OS_USERNAME"], null)
  password            = try(data.vault_generic_secret.infra_credential.data["OS_PASSWORD"], null)
  user_domain_name    = try(data.vault_generic_secret.infra_credential.data["OS_USER_DOMAIN_NAME"], null)
  project_domain_name = try(data.vault_generic_secret.infra_credential.data["OS_PROJECT_DOMAIN_NAME"], null)

  insecure = try(data.vault_generic_secret.infra_credential.data["OS_INSECURE"], false)

  # common
  auth_url = data.vault_generic_secret.infra_credential.data["OS_AUTH_URL"]
  region   = data.vault_generic_secret.infra_credential.data["OS_REGION_NAME"]
}


# DNS AWS provider
provider "aws" {
  access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]

    use_fips_endpoint = false
  # default_tags {
  #   tags = {
  #     ApplicationName = var.csb_application_name
  #     CiscoMailAlias  = var.csb_cisco_mail_alias
  #     ResourceOwner   = var.csb_resource_owner
  #     Environment     = var.csb_environment
  #   }
  # }
}

provider "aws" {
  alias      = "pub-dns"
  access_key = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = data.vault_generic_secret.aws_pub_dns_credential.data["AWS_DEFAULT_REGION"]

    use_fips_endpoint = false
  # default_tags {
  #   tags = {
  #     ApplicationName = var.csb_application_name
  #     CiscoMailAlias  = var.csb_cisco_mail_alias
  #     ResourceOwner   = var.csb_resource_owner
  #     Environment     = var.csb_environment
  #   }
  # }
}
