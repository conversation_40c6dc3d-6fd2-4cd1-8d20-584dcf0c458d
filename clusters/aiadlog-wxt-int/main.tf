# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  /
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/
#
#
# codegen-version: v7.4.2
# template-version: EXPERIMENTAL
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
  path=replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "aws_infra_credential" {
  path=replace("secret/data/mccprod/infra/lma/int/aws-lma-wbx3", "/data/", "/")
}




terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-aiadlog-wxt-int.tfstate"
    region = "us-east-2"
  }
}



variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "us-east-1"
}

# Infra AWS Provider
provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = var.AWS_INFRA_REGION
  max_retries = 3
}

# DNS AWS provider
provider "aws" {
  alias = "dns"
  access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
  max_retries = 3
 }


variable "image-name" {
  type = string
  default = "wbx3-bionic-hardened-134_e3acbda"
}

module "k8s" {
  source       = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/k8s?ref=v7.4.4"
  domain       = "prod.infra.webex.com"
  cluster-name = "aiadlog-wxt-int"
  vpc-name = "wbx3-logs-int-useast-1d"
  image-name   = var.image-name
  scale-image-name = "${var.image-name}"
  vault-path   = "secret/mccprod/infra/logs-int-useast-1d/aiadlog-wxt-int/k8s/ssh/public_key"
  vault-bastion-path   = "secret/mccprod/infra/logs-int-useast-1d/aiadlog-wxt-int/bastion/ssh/public_key"
  controller-config-path = "secret/mccprod/cloud-controller/aiadlog-wxt-int/cloud"
  join-token-vault-path = "secret/mccprod/kubernetes/aiadlog-wxt-int/kubeadm"
  kubeadmin-vault-path = "secret/mccprod/kubernetes/aiadlog-wxt-int/kubeadmin"

  # Masters
  master_count  = 3
  master_flavor = "m5d.2xlarge"

  # Workers
  worker_count  = 3
  worker_flavor = "m5a.2xlarge"

  # Ingress
  ingress_count  = 2
  ingress_flavor = "c5n.2xlarge"

  # Ingress Int
  ingress_int_count  = 2
  ingress_int_flavor = "c5n.2xlarge"

  # Bastion
  bastion_count  = 1
  bastion_flavor = "m5a.large"

  # cidr ranges
  cidr-k8s-pods = "172.24.8.0/22"
  cidr-k8s-svcs = "172.23.4.0/22"
  cidr-node-prefix = "26"

  
  # External media security group rules
  custom-external-media-sg-rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-tcp","protocol"="tcp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-udp","protocol"="udp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"}]
  
  # Ingress security group rules
  custom-ingress-sg-rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]
  




  optimized-storage-node-count = 3
  optimized-storage-flavor = "i3.2xlarge"


  internal-media-node-flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"


  internal-mini-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"


  external-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  health-checks = false
  dev-cluster = false
  aws-az = "us-east-1d"


  create-eip = "true"


  mgmt-ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "************/22",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "*************/21",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
  ]

  https-internal-ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "************/22",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "*************/19",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
    "*************/17",
    "**********/16",
    "************/19",
  ]








  external-media-pool-max = 0
}

output "cluster_pod_cidr" {
  value = module.k8s.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.k8s.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.k8s.cluster_node_prefix
}

output "gateway_hash" {
  value = module.k8s.gateway_hash
}

output "gateway_ips" {
  value = module.k8s.gateway_ips
}

output "cluster_hosted_zone" {
  value = module.k8s.cluster_hosted_zone
}
