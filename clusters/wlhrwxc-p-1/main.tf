# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.2
# template-version: EXPERIMENTAL
# module-version: v7.23.4-custom-routing
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/wlhr05-ocp4-prod/openstack
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wlhrwxc-p-1.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/cluster?ref=657afea1b2f19a134b7645ebaf9139518f34d835"
  bastion_block_storage = true
  bastion_count = 1
  bastion_flavor = "kubed.gv.4vcpu.8mem.0ssd.0eph"
  cidr_node_prefix = 26
  cidr_pods = "auto"
  cidr_svcs = "auto"
  cloud_service_provider = "openstack"
  command_and_control = "mccprod"
  dev_cluster = false
  domain = "prod.infra.webex.com"
  external_media_node_count = 0
  external_media_node_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  external_network = "public-402"
  force_delete = true
  gateway_name = "wlhr05-ocp4-prod"
  health_checks = true
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  image_name = "wbx3-focal-1.23.5-containerd-659_421a101"
  infra_credentials_path = "secret/data/mccprod/infra/wlhr05-ocp4-prod/openstack"
  ingress_block_storage = true
  ingress_count = 3
  ingress_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_int_block_storage = true
  ingress_int_count = 2
  ingress_int_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_count = 0
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"
  internal_mini_media_node_count = 0
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  internal_network = "wlhr05-ocp4-prod"
  internal_provider_network = "provider-400"
  internal_provider_subnets = [
    "***********/24"
  ]
  management_network = "provider-400"
  master_block_storage = true
  master_count = 3
  master_flavor = "kubed.gv.8vcpu.32mem.0ssd.0eph"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  name = "wlhrwxc-p-1"
  node_pools = [
    {
      max_size = 9
      min_size = 9
      name = "worker-pool"
      root_block_storage = true
      type = "worker"
    },
    {
      flavor = "kubed.gv.16vcpu.64mem.0ssd.0eph"
      max_size = 2
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
      min_size = 2
      name = "prom-pool"
      root_block_storage = true
      type = "worker"
    },
    {
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 16
      media_network_name = "provider-400"
      metadata = {
        dns_prefix = "sse-a"
        node_labels = "pool-name=sip-pool-a,type=external-media"
      }
      min_size = 16
      name = "sip-pool-a"
      public_network_name = "public-402"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5061
            name = "sse-sip-internal-tcp-3"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5062
            name = "sse-sip-internal-tcp-4"
            protocol = "tcp"
            to = 5062
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 8934
            name = "sse-sip-internal-tcp-5"
            protocol = "tcp"
            to = 8934
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      custom_routing = true
      max_size = 2
      media_network_name = "provider-400"
      metadata = {
        dns_prefix = "sse-p"
        node_labels = "pool-name=sip-peering-pool,infra.webex.com/dns-prefix=sse-p,type=external-media"
      }
      min_size = 2
      name = "sip-peering-pool"
      public_network_name = "provider-401"
      root_block_storage = true
      routes = {
        eth1 = [
          "10.0.0.0/8"
        ]
        eth2 = [
          "***********/20",
          "************/20",
          "************/20"
        ]
      }
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5061
            name = "sse-sip-internal-tcp-3"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5062
            name = "sse-sip-internal-tcp-4"
            protocol = "tcp"
            to = 5062
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 8934
            name = "sse-sip-internal-tcp-5"
            protocol = "tcp"
            to = 8934
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 22
      media_network_name = "provider-400"
      metadata = {
        dns_prefix = "mse-a"
        node_labels = "pool-name=media-pool-a,type=external-media"
      }
      min_size = 22
      name = "media-pool-a"
      public_network_name = "public-402"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "mse-media-rtp-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5004
            name = "mse-media-multiplexed"
            protocol = "udp"
            to = 5004
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      custom_routing = true
      max_size = 3
      media_network_name = "provider-400"
      metadata = {
        dns_prefix = "mse-p"
        node_labels = "pool-name=media-peering-pool,infra.webex.com/dns-prefix=mse-p,type=external-media"
      }
      min_size = 3
      name = "media-peering-pool"
      public_network_name = "provider-401"
      root_block_storage = true
      routes = {
        eth1 = [
          "10.0.0.0/8"
        ]
        eth2 = [
          "***********/20",
          "************/20",
          "************/20"
        ]
      }
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "mse-media-rtp-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5004
            name = "mse-media-multiplexed"
            protocol = "udp"
            to = 5004
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      custom_routing = true
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 1
      metadata = {
        dns_prefix = "sse-g"
        node_labels = "pool-name=sip-pool-g,type=external-media,infra.webex.com/dns-prefix=sse-g"
      }
      min_size = 1
      name = "sip-pool-g"
      public_network_name = "public-425"
      root_block_storage = true
      routes = {
        eth1 = [
          "10.0.0.0/8"
        ]
        eth2 = [
          "*************/29"
        ]
      }
      security_groups = {
        ext = [
          {
            cidr = "*************/29"
            from = 1024
            name = "sse-sip-tango-tcp"
            protocol = "tcp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "*************/29"
            from = 1024
            name = "sse-sip-tango-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-udp-1"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-udp-2"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 5060
            name = "sse-sip-internal-tcp-1"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 5060
            name = "sse-sip-internal-tcp-2"
            protocol = "tcp"
            to = 5060
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      custom_routing = true
      flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
      max_size = 2
      metadata = {
        dns_prefix = "mse-g"
        node_labels = "pool-name=media-pool-g,type=external-media,infra.webex.com/dns-prefix=mse-g"
      }
      min_size = 2
      name = "media-pool-g"
      public_network_name = "public-425"
      root_block_storage = true
      routes = {
        eth1 = [
          "10.0.0.0/8"
        ]
        eth2 = [
          "*************/29"
        ]
      }
      security_groups = {
        ext = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "10.0.0.0/8"
            from = 9443
            name = "mse-grpc-tcp-1"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 9443
            name = "mse-grpc-tcp-2"
            protocol = "tcp"
            to = 9443
            type = "ingress"
          },
          {
            cidr = "10.0.0.0/8"
            from = 19560
            name = "mse-media-rtp-udp-media-1"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "**********/12"
            from = 19560
            name = "mse-media-rtp-udp-media-2"
            protocol = "udp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed-media.nv.32vcpu.64mem.0ssd.0eph"
      max_size = 2
      media_network_name = "provider-401"
      metadata = {
        dns_prefix = "wxc-dhruva-proxy"
        node_labels = "pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod"
      }
      min_size = 2
      name = "wxc-dhruva-proxy"
      public_network_name = "public-403"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-udp"
            protocol = "udp"
            to = 5060
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 5060
            name = "wxc-dhruva-proxy-tcp"
            protocol = "tcp"
            to = 5061
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    },
    {
      flavor = "kubed.gv.16vcpu.16mem.0ssd.0eph"
      max_size = 3
      media_network_name = "provider-401"
      metadata = {
        dns_prefix = "wxc-dhruva-antares"
        node_labels = "pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=prod"
      }
      min_size = 3
      name = "wxc-dhruva-antares"
      public_network_name = "public-403"
      root_block_storage = true
      security_groups = {
        ext = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-tcp"
            protocol = "tcp"
            to = 65535
            type = "ingress"
          }
        ]
        media = [
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-udp"
            protocol = "udp"
            to = 65535
            type = "ingress"
          },
          {
            cidr = "0.0.0.0/0"
            from = 19560
            name = "wxc-dhruva-antares-tcp"
            protocol = "tcp"
            to = 65535
            type = "ingress"
          }
        ]
      }
      type = "external-media"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  server_group_policies = [
    "anti-affinity"
  ]
  thousand_eyes_node_count = 0
  thousand_eyes_node_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  use_floating_ips = false
  worker_block_storage = true
  worker_count = 1
  worker_eth1_network = "provider-400"
  worker_flavor = "kubed.gv.8vcpu.16mem.0ssd.0eph"
  
}

output "aws_zone_id" {
  value = module.infra.aws_zone_id
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "ingress_address" {
  value = module.infra.ingress_address
}

