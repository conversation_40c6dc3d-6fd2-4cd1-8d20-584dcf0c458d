# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  /
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/
#
#
# codegen-version: v7.14.10
# template-version: v7.14.10-EXPERIMENTAL-76383fc
# -----------------------------------------------
terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-agrumwxc-p-1.tfstate"
    region = "us-east-2"
  }
}


variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "sa-east-1"
}
data "terraform_remote_state" "network" {
  backend = "s3"
  config = {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_a-saeast-1a-wxc.tfstate"
    region = "us-east-2"
  }
}

variable "image-name" {
  type = string
  default = "wbx3-focal-1.21.5-447_daf97f6"
}

module "cluster" {
  source       = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cluster?ref=v7.16.1"
  domain       = "prod.infra.webex.com"
  name         = "agrumwxc-p-1"
  vpc_name     = "a-wxc-saeast-prod"
  image_name   = var.image-name
  scale_image_name = "${var.image-name}"
  command_and_control = "mccprod"

  aws_infra_region = var.AWS_INFRA_REGION

  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  dns_credentials_path   = "secret/data/mccprod/infra/route53/credentials"

  # Masters
  master_count  = 3
  master_flavor = "c6a.2xlarge"

  # Workers
  worker_count  = 0
  worker_flavor = "c6a.2xlarge"

  # Ingress
  ingress_count  = 2
  ingress_flavor = "c5n.2xlarge"

  # Ingress Int
  ingress_int_count  = 2
  ingress_int_flavor = "c5n.2xlarge"

  # Bastion
  bastion_count  = 1
  bastion_flavor = "c5n.large"

  # cidr ranges
  cidr_pods = "auto"
  cidr_svcs = "auto"
  cidr_node_prefix = "26"

  gateway_name = "a-wxc-saeast-igw"
  network_name = try(data.terraform_remote_state.network.outputs.name, "")
  
  # External media security group rules
  external_media_sg_rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]
  
  # Ingress security group rules
  ingress_sg_rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]
  
  optimized_storage_count = 0
  optimized_storage_flavor = "8vcpu.16mem.512ssd.0eph"

  internal_media_node_flavor = "c6i.8xlarge"

  internal_mini_media_node_flavor = "c6i.8xlarge"

  external_media_node_flavor = "c6i.2xlarge"

  health_checks = false
  dev_cluster = false

  aws_infra_az = "sa-east-1a"

  network_names = [
    "a-saeast-1a-wxc","a-saeast-1b-wxc","a-saeast-1c-wxc",
  ]
  aws_infra_azs = [
    "sa-east-1a","sa-east-1b","sa-east-1c",
  ]
  nat_gateway  = true

  vpc_routing  = true

  create_eip = true


  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
  ]

  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
  ]

  node_pools = [    {
      name = "worker-pool"
      type = "worker"
      min_size = 0
      max_size = 6
      flavor = "c6a.2xlarge"
      metadata = {}
    },    {
      name = "sip-pool"
      type = "worker"
      min_size = 0
      max_size = 0
      flavor = "c6a.2xlarge"
      metadata = {
        dns_prefix = "sse"
        node_labels = "pool-name=sip-pool,infra.webex.com/dns-prefix=sse,type=external-media"
        node_taints = "type=media-node:NoSchedule"
      }

      security_groups = {
        ext = [{"name"="sse-sip-internal-tcp-3","protocol"="tcp","from"="5061","to"="5061","cidr"="0.0.0.0/0","type"="ingress"},{"name"="sse-sip-internal-tcp-4","protocol"="tcp","from"="5062","to"="5062","cidr"="0.0.0.0/0","type"="ingress"},{"name"="sse-sip-internal-tcp-5","protocol"="tcp","from"="8934","to"="8934","cidr"="0.0.0.0/0","type"="ingress"}]
        media = [{"name"="sse-sip-internal-udp-1","protocol"="udp","from"="5060","to"="5060","cidr"="10.0.0.0/8","type"="ingress"},{"name"="sse-sip-internal-udp-2","protocol"="udp","from"="5060","to"="5060","cidr"="**********/12","type"="ingress"},{"name"="sse-sip-internal-tcp-1","protocol"="tcp","from"="5060","to"="5060","cidr"="10.0.0.0/8","type"="ingress"},{"name"="sse-sip-internal-tcp-2","protocol"="tcp","from"="5060","to"="5060","cidr"="**********/12","type"="ingress"}]
      }
      extra_enis = [
        {
          device_index = 1
          subnet_tags = {
            Tier = "provider"
          }
          assign_eip = false
          create_eip = false
          security_groups_name = "media"
          extra_security_groups = []
        },
        {
          device_index = 2
          subnet_tags = {
            Tier = "public"
          }
          assign_eip = true
          create_eip = false
          security_groups_name = "ext"
          extra_security_groups = []
        }
      ]
    },    {
      name = "media-pool"
      type = "worker"
      min_size = 0
      max_size = 0
      flavor = "c6a.2xlarge"
      metadata = {
        dns_prefix = "mse"
        node_labels = "pool-name=media-pool,infra.webex.com/dns-prefix=mse,type=external-media"
        node_taints = "type=media-node:NoSchedule"
      }

      security_groups = {
        ext = [{"name"="mse-media-rtp-udp","protocol"="udp","from"="19560","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="mse-media-multiplexed","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"}]
        media = [{"name"="mse-grpc-tcp-1","protocol"="tcp","from"="9443","to"="9443","cidr"="10.0.0.0/8","type"="ingress"},{"name"="mse-grpc-tcp-2","protocol"="tcp","from"="9443","to"="9443","cidr"="**********/12","type"="ingress"},{"name"="mse-media-rtp-udp-media-1","protocol"="udp","from"="19560","to"="65535","cidr"="10.0.0.0/8","type"="ingress"},{"name"="mse-media-rtp-udp-media-2","protocol"="udp","from"="19560","to"="65535","cidr"="**********/12","type"="ingress"}]
      }
      extra_enis = [
        {
          device_index = 1
          subnet_tags = {
            Tier = "provider"
          }
          assign_eip = false
          create_eip = false
          security_groups_name = "media"
          extra_security_groups = []
        },
        {
          device_index = 2
          subnet_tags = {
            Tier = "public"
          }
          assign_eip = true
          create_eip = false
          security_groups_name = "ext"
          extra_security_groups = []
        }
      ]
    },    {
      name = "sip-pool-b"
      type = "worker"
      min_size = 0
      max_size = 0
      flavor = "c6a.2xlarge"
      metadata = {
        dns_prefix = "sse"
        node_labels = "pool-name=sip-pool-b,infra.webex.com/dns-prefix=sse-b,type=external-media"
        node_taints = "type=media-node:NoSchedule"
      }

      security_groups = {
        ext = [{"name"="sse-sip-internal-tcp-3","protocol"="tcp","from"="5061","to"="5061","cidr"="0.0.0.0/0","type"="ingress"},{"name"="sse-sip-internal-tcp-4","protocol"="tcp","from"="5062","to"="5062","cidr"="0.0.0.0/0","type"="ingress"},{"name"="sse-sip-internal-tcp-5","protocol"="tcp","from"="8934","to"="8934","cidr"="0.0.0.0/0","type"="ingress"}]
        media = [{"name"="sse-sip-internal-udp-1","protocol"="udp","from"="5060","to"="5060","cidr"="10.0.0.0/8","type"="ingress"},{"name"="sse-sip-internal-udp-2","protocol"="udp","from"="5060","to"="5060","cidr"="**********/12","type"="ingress"},{"name"="sse-sip-internal-tcp-1","protocol"="tcp","from"="5060","to"="5060","cidr"="10.0.0.0/8","type"="ingress"},{"name"="sse-sip-internal-tcp-2","protocol"="tcp","from"="5060","to"="5060","cidr"="**********/12","type"="ingress"}]
      }
      extra_enis = [
        {
          device_index = 1
          subnet_tags = {
            Tier = "provider"
          }
          assign_eip = false
          create_eip = false
          security_groups_name = "media"
          extra_security_groups = []
        },
        {
          device_index = 2
          subnet_tags = {
            Tier = "public"
          }
          assign_eip = true
          create_eip = false
          security_groups_name = "ext"
          extra_security_groups = []
        }
      ]
    },    {
      name = "media-pool-b"
      type = "worker"
      min_size = 0
      max_size = 0
      flavor = "c6a.2xlarge"
      metadata = {
        dns_prefix = "mse"
        node_labels = "pool-name=media-pool-b,infra.webex.com/dns-prefix=mse-b,type=external-media"
        node_taints = "type=media-node:NoSchedule"
      }

      security_groups = {
        ext = [{"name"="mse-media-rtp-udp","protocol"="udp","from"="19560","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="mse-media-multiplexed","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"}]
        media = [{"name"="mse-grpc-tcp-1","protocol"="tcp","from"="9443","to"="9443","cidr"="10.0.0.0/8","type"="ingress"},{"name"="mse-grpc-tcp-2","protocol"="tcp","from"="9443","to"="9443","cidr"="**********/12","type"="ingress"},{"name"="mse-media-rtp-udp-media-1","protocol"="udp","from"="19560","to"="65535","cidr"="10.0.0.0/8","type"="ingress"},{"name"="mse-media-rtp-udp-media-2","protocol"="udp","from"="19560","to"="65535","cidr"="**********/12","type"="ingress"}]
      }
      extra_enis = [
        {
          device_index = 1
          subnet_tags = {
            Tier = "provider"
          }
          assign_eip = false
          create_eip = false
          security_groups_name = "media"
          extra_security_groups = []
        },
        {
          device_index = 2
          subnet_tags = {
            Tier = "public"
          }
          assign_eip = true
          create_eip = false
          security_groups_name = "ext"
          extra_security_groups = []
        }
      ]
    },
  ]
}

output "cluster_pod_cidr" {
  value = module.cluster.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.cluster.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.cluster.cluster_node_prefix
}

output "vpc_routing" {
  value = module.cluster.vpc_routing
}

output "gateway_hash" {
  value = module.cluster.gateway_hash
}

output "gateway_ips" {
  value = module.cluster.gateway_ips
}

output "cluster_hosted_zone" {
  value = module.cluster.cluster_hosted_zone
}

output "cluster_iam_user_vault_path" {
  value = module.cluster.cluster_iam_user_vault_path
}
