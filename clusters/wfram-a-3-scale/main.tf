# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.4.7
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wfram-a-3-scale.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = "string"
  default = "wbx3-bionic-hardened-1_ba15395"
}

data "terraform_remote_state" "main" {
  backend = "s3"
  config = {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wfram-a-3.tfstate"
    region = "us-east-2"
  }
}


module "k8s" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/scale?ref=v7.4.14"
  cluster-name = "wfram-a-3"
  project-name = "wfra-prod"
  domain = "prod.infra.webex.com"
  ssh-user = "ubuntu"
  image-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  image-name = "${var.image-name}"
  scale-image-name = "${var.image-name}"
  vault-path = "secret/mccprod/infra/wfra-prod/wfram-a-3/k8s/ssh/public_key"
  vault-bastion-path = "secret/mccprod/infra/wfra-prod/wfram-a-3/bastion/ssh/public_key"
  join-token-vault-path = "secret/mccprod/kubernetes/wfram-a-3/kubeadm"
  kubeadmin-vault-path = "secret/mccprod/kubernetes/wfram-a-3/kubeadmin"
  use-floating-ip = false
  floating-ip-pool = "Pub-Calliope-Telephony-cmr4-423"
  internal-network-name = "wfra-prod"
  management-network-name = "Prv-Calliope-Telephony-cmr4-422"
  force_delete_enabled = true

  # Bastions
  bastion-node-count = 1
  bastion-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Masters
  k8s-master-count = 3

  # Workers
  k8s-node-count = 6
  worker-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Ingresses
  ingress-controller-count = 2
  ingress-controller-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Internal ingresses
  ingress-internal-count = 2

  # cidr ranges
  use-cidr-allocations = true
  cidr-k8s-pods = "***********/19"
  cidr-k8s-svcs = "***********/22"
  cidr-node-prefix = "27"

  health-checks = true
  dev-cluster = false


  custom-external-media-sg-rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-tcp","protocol"="tcp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-udp","protocol"="udp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]
  custom-ingress-sg-rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]

  thousand-eyes-node-count = 0
  thousand-eyes-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  optimized-storage-node-count = 3
  optimized-storage-flavor = "8vcpu.16mem.2048ssd.0eph"

  internal-media-node-count = 0
  internal-media-node-flavor = "PROD-Spark-Media.47vcpu.64mem.80ssd.0eph.numa"

  internal-mini-media-node-count = 0
  internal-mini-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  external-media-node-count = 0
  external-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  media-provider-network-name = "Prv-Calliope-Telephony-cmr4-422"
  media-provider-network-subnets = [
    "************/23",
    "************/23",
  ]

  server-group-policies = [
    "anti-affinity",
  ]

  bastion_allowed_remote_ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "************/22",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "*************/21",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
  ]


  https-internal-ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "************/22",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "*************/19",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
    "*************/17",
    "**********/16",
    "************/19",
  ]
  k8s-node-pool-count = 0
  ingress-controller-pool-count = 0
  ingress-internal-pool-count = 0
  internal-media-pool-count = 50
  internal-mini-media-pool-count = 0
  external-media-pool-count = 156

  master-ips = data.terraform_remote_state.main.outputs.master_ips
  cluster-hosted-zone = data.terraform_remote_state.main.outputs.cluster_hosted_zone
  gateway-ips = data.terraform_remote_state.main.outputs.gateway_ips
}
