# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.0
# module-version: v10.1.4-patch-customised-storage-size-v6
# wbx3-infra-version: <nil>
# resource: wsjcm-a-8
# resource-type: cluster
# -----------------------------------------------

#~~ Resource Values
bastion_count: 1
bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
cidr_node_prefix: 26
cidr_pods: auto
cidr_svcs: auto
cloud_service_provider: openstack
env_name: wsjc03-ocp4-prod-media
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
    - cidr: ::/0
      from: 443
      name: external-media-https-v6
      protocol: tcp
      to: 444
      type: ingress
    - cidr: ::/0
      from: 11000
      name: external-media-cascade-ports-tcp-v6
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: ::/0
      from: 11000
      name: external-media-cascade-ports-udp-v6
      protocol: udp
      to: 33000
      type: ingress
    - cidr: ::/0
      from: 49152
      name: external-media-rtp-ports-tcp-v6
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: ::/0
      from: 49152
      name: external-media-rtp-ports-udp-v6
      protocol: udp
      to: 65535
      type: ingress
    - cidr: ::/0
      from: 5004
      name: external-media-shared-ports-1-tcp-v6
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: ::/0
      from: 5004
      name: external-media-shared-ports-1-udp-v6
      protocol: udp
      to: 5004
      type: ingress
    - cidr: ::/0
      from: 9000
      name: external-media-shared-ports-3-tcp-v6
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: ::/0
      from: 9000
      name: external-media-shared-ports-3-udp-v6
      protocol: udp
      to: 9000
      type: ingress
health_checks: true
infractl_version: v7.19.0
ingress_count: 3
ingress_flavor: kubed.gv.8vcpu.64mem.0ssd.0eph
ingress_int_count: 1
ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
master_count: 3
master_flavor: kubed.gv.8vcpu.64mem.0ssd.0eph
metadata:
    annotations:
        calliope_group: wsjcm
        calliope_org: wsjcm
        helm3Only: true
        service_block: america-a
    cluster_type: media
    platform_release_channel: stable-1
module_commit: 2e0bc4ffc5470fc82fb65aa891c82856e1681b95
module_version: v10.1.4-patch-customised-storage-size-v6
name: wsjcm-a-8
node_pools:
    - flavor: kubed.gv.16vcpu.128mem.0ssd.0eph
      max_size: 3
      metadata:
        node_labels: type=optimized-storage-node
      min_size: 3
      name: optimized-storage
      root_block_storage: true
      type: worker
    - flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
      max_size: 6
      metadata:
        node_labels: pool-name=worker,type=worker
      min_size: 6
      name: worker
      root_block_storage: true
      type: worker
    - flavor: PROD-Spark-Media.47vcpu.64mem.0ssd.0eph.numa
      max_size: 50
      media_network_name: provider-415
      min_size: 50
      name: mygdonus-pool
      root_block_storage: true
      type: internal-media
    - flavor: PROD-Spark-Media.8vcpu.16mem.0ssd.0eph.numa
      max_size: 250
      media_network_name: provider-415
      min_size: 250
      name: linus-pool
      public_network_name: provider-412
      root_block_storage: true
      type: external-media
    - flavor: gv.4vcpu.8mem.0ssd.0eph
      max_size: 1
      metadata:
        dns_prefix: thousand-eyes
        node_labels: type=thousand-eyes
      min_size: 1
      name: thousand-eyes
      public_network_name: provider-412
      root_block_storage: true
      type: external-media
    - flavor: homer.12vcpu.24mem.0ssd.0eph
      max_size: 100
      metadata:
        dns_prefix: xlm
        node_labels: type=external-large-media,infra.webex.com/remove-ds-a-record=true,infra.webex.com/dual-stack-identifier=ds
      min_size: 100
      name: homer-pool
      root_block_storage: true
      type: external-large-media
optimized_storage_count: 0
pipeline_bundles:
    - platform/ecr-deploy.yaml
    - platform/post-provision.yaml
    - platform/external-dns.yaml
    - platform/base-apps.yaml
    - media/init.yaml
    - media/lma-mirrors.yaml
    - media/calliope-media.yaml
    - platform/falco.yaml
provisioning_extra_args: container_manager='containerd'
service_block: america-a
status: online
worker_count: 0
worker_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph

#~~ No Blueprint Values


#~~ Environment Values
admin_port: 6442
aws_infra_az: <replace-me>
backend: s3
base_image: wbx3-focal-1.25.6-containerd-980646a
base_k8s_image: wbx3-focal-1.25.6-containerd-980646a
bastion_block_storage: true
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_network: provider-411
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: wsjc03-ocp4-prod-media
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
infra_credentials_path: secret/data/mccprod/infra/wsjc03-ocp4-prod/openstack
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
ingress_block_storage: true
ingress_int_block_storage: true
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
internal_network: wsjc03-ocp4-prod-media
internal_provider_network: provider-413
internal_provider_subnets:
    - 10.0.0.0/8
location: SJC
management_network: provider-413
master_block_storage: true
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
ocp_ownership_business_service: WBX3 Platform - Meetings
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_module_version: v5.11.2
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
server_group_policies:
    - anti-affinity
terraform_version: v1.5.4
thousand_eyes_count: 0
use_floating_ips: false
use_provider_template: true
volume_storage: true
vpc_name: provider-411
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_block_storage: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: clusters/wsjcm-a-8
    name: wsjcm-a-8
    workflow: standard
dir: clusters
external: false
image_name: wbx3-focal-1.25.6-containerd-980646a
manifest_path: manifests/wsjc03-ocp4-prod/media/wsjcm-a-8/manifest.yaml
module_path: modules/openstack/cluster
network_name: wsjc03-ocp4-prod-media
network_names:
    - wsjc03-ocp4-prod-media
node_pool_capacity:
    external-large-media: 100
    external-media: 251
    internal-media: 50
    worker: 9
s3_bucket_path: terraform-state/cluster-wsjcm-a-8.tfstate
versioningData:
    infractlversion: 7.19.0
    templateversion: 7.19.0
workflow: standard