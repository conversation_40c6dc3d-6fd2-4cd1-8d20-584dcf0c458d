# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.2
# template-version: EXPERIMENTAL
# module-version: allow-generic-import
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-afrasbg-a-1.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cluster?ref=7a32b4ef5b89dded95aa15af73de4ba4cfd3cdc3"
  aws_infra_azs = [
    "eu-central-1a",
    "eu-central-1b",
    "eu-central-1c"
  ]
  aws_infra_region = "eu-central-1"
  bastion_count = 1
  bastion_flavor = "c5n.large"
  cidr_node_prefix = 26
  cidr_pods = "auto"
  cidr_svcs = "auto"
  cidr_svcs_prefix = 22
  command_and_control = "mccprod"
  create_eip = true
  dev_cluster = false
  dns_credentials_path = "secret/data/mccprod/infra/route53/credentials"
  domain = "prod.infra.webex.com"
  env_name = "afra-sbg-pl"
  external_media_node_flavor = "c6i.2xlarge"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  gateway_name = "a-sbg-igw"
  health_checks = false
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  infra_credentials_path = "secret/data/mccprod/infra/sbg-pl/aws"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  ingress_count = 2
  ingress_flavor = "c5n.2xlarge"
  ingress_int_count = 2
  ingress_int_flavor = "c5n.2xlarge"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_flavor = "c6i.8xlarge"
  internal_mini_media_node_flavor = "c6i.8xlarge"
  master_count = 3
  master_flavor = "m5a.2xlarge"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  name = "afrasbg-a-1"
  nat_gateway = true
  network_name = "afra-sbg-pl-1a"
  network_names = [
    "afra-sbg-pl-1a",
    "afra-sbg-pl-1b",
    "afra-sbg-pl-1c"
  ]
  node_pools = [
    {
      flavor = "m5a.2xlarge"
      max_size = 16
      min_size = 3
      name = "worker-pool"
      type = "worker"
    },
    {
      flavor = "c6a.2xlarge"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 3
      name = "istio-ingress-pool"
      type = "worker"
    },
    {
      flavor = "c5a.large"
      max_size = 3
      metadata = {
        node_labels = "dedicated=istio-ingress-ciscoint"
        node_taints = "dedicated=istio-ingress-ciscoint:NoSchedule"
      }
      min_size = 1
      name = "istio-ingress-cisco"
      type = "worker"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "8vcpu.16mem.512ssd.0eph"
  scale_image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  vpc_name = "kubed-afra-sbg-pl"
  vpc_routing = true
  worker_count = 0
  worker_flavor = "m5a.2xlarge"
  
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_iam_user_vault_path" {
  value = module.infra.cluster_iam_user_vault_path
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "vpc_routing" {
  value = module.infra.vpc_routing
}
