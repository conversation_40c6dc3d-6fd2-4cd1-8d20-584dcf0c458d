# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: EXPERIMENTAL
# module-version: v7.23.4-patch3-anrt-TE
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-aiadwxt-int-1.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cluster?ref=a58fed1999b0f6af16568a027f07c73898ba8251"
  aws_infra_az = "us-east-1a"
  aws_infra_azs = [
    "us-east-1a",
    "us-east-1b",
    "us-east-1c"
  ]
  aws_infra_region = "us-east-1"
  bastion_count = 1
  bastion_flavor = "t3a.small"
  cidr_node_prefix = 26
  cidr_pods = "**********/16"
  cidr_svcs = "************/22"
  command_and_control = "mccprod"
  create_eip = true
  dev_cluster = false
  dns_credentials_path = "secret/data/mccprod/infra/route53/credentials"
  domain = "prod.infra.webex.com"
  external_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  gateway_name = "aiadwxt-int"
  health_checks = false
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  ingress_count = 1
  ingress_flavor = "c6a.xlarge"
  ingress_int_count = 1
  ingress_int_flavor = "c6a.xlarge"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  master_count = 3
  master_flavor = "m6a.4xlarge"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  name = "aiadwxt-int-1"
  nat_gateway = true
  network_name = "aiadwxt-int-az1"
  network_names = [
    "aiadwxt-int-az1",
    "aiadwxt-int-az2",
    "aiadwxt-int-az3"
  ]
  node_pools = [
    {
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "m6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker"
      type = "worker"
    },
    {
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "type=worker,node.kubernetes.io/lifecycle=spot"
      }
      min_size = 0
      name = "spot-worker"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      availability_zones = [
        "us-east-1a"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "type=worker,node.kubernetes.io/lifecycle=spot"
      }
      min_size = 0
      name = "spot-worker-1a"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      availability_zones = [
        "us-east-1b"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "type=worker,node.kubernetes.io/lifecycle=spot"
      }
      min_size = 0
      name = "spot-worker-1b"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      availability_zones = [
        "us-east-1c"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "type=worker,node.kubernetes.io/lifecycle=spot"
      }
      min_size = 0
      name = "spot-worker-1c"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      availability_zones = [
        "us-east-1a"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "m6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker-1a"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1b"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "m6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker-1b"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1c"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "m6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker-1c"
      type = "worker"
    },
    {
      flavor = "r6a.2xlarge"
      max_size = 5
      metadata = {
        node_labels = "type=optimized-storage-node"
        node_taints = "type=optimized-storage-node:NoSchedule"
      }
      min_size = 0
      name = "optimized-storage-pool"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1a"
      ]
      flavor = "r6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "type=optimized-storage-node"
        node_taints = "type=optimized-storage-node:NoSchedule"
      }
      min_size = 0
      name = "plat-prometheus-1a"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1b"
      ]
      flavor = "r6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "type=optimized-storage-node"
        node_taints = "type=optimized-storage-node:NoSchedule"
      }
      min_size = 0
      name = "plat-prometheus-1b"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1c"
      ]
      flavor = "r6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "type=optimized-storage-node"
        node_taints = "type=optimized-storage-node:NoSchedule"
      }
      min_size = 0
      name = "plat-prometheus-1c"
      type = "worker"
    },
    {
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker-4xlarge"
      type = "worker"
    },
    {
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "type=worker,node.kubernetes.io/lifecycle=spot"
        node_taints = "lifecycle=spot:NoSchedule"
      }
      min_size = 0
      name = "spot-worker-4xlarge"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "type=worker,node.kubernetes.io/lifecycle=spot"
        node_taints = "lifecycle=spot:NoSchedule"
      }
      min_size = 0
      name = "spot-worker-m-4xlarge"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "c5a.2xlarge"
      max_size = 18
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 3
      name = "istio-ingress-pool"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1a"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "c5a.2xlarge"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 1
      name = "istio-ingress-pool-1a"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1b"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "c5a.2xlarge"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 1
      name = "istio-ingress-pool-1b"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1c"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      flavor = "c5a.2xlarge"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 1
      name = "istio-ingress-pool-1c"
      type = "worker"
    },
    {
      flavor = "c5a.large"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress-ciscoint"
        node_taints = "dedicated=istio-ingress-ciscoint:NoSchedule"
      }
      min_size = 3
      name = "istio-ingress-cisco"
      type = "worker"
    },
    {
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      max_size = 8
      metadata = {
        node_labels = "type=anchore-node"
        node_taints = "dedicated=secops-anchore:NoSchedule"
      }
      min_size = 0
      name = "anchore-pool"
      type = "worker"
    },
    {
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m6i.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "asg-test"
      type = "worker"
    },
    {
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m6i.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "dedicated=int-first"
        node_taints = "dedicated=int-first-tests:NoSchedule"
      }
      min_size = 0
      name = "wxt-aiad-tap"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1a"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 70
      metadata = {
        node_labels = "dedicated=int-first,node.kubernetes.io/lifecycle=spot"
        node_taints = "dedicated=int-first:NoSchedule"
      }
      min_size = 0
      name = "spot-wxt-1a"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      availability_zones = [
        "us-east-1b"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 70
      metadata = {
        node_labels = "dedicated=int-first,node.kubernetes.io/lifecycle=spot"
        node_taints = "dedicated=int-first:NoSchedule"
      }
      min_size = 1
      name = "spot-wxt-1b"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      availability_zones = [
        "us-east-1c"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m7i.4xlarge",
        "m7a.4xlarge",
        "m6a.4xlarge",
        "m6i.4xlarge",
        "m6id.4xlarge",
        "m6in.4xlarge",
        "m5.4xlarge",
        "m5d.4xlarge",
        "m5n.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 70
      metadata = {
        node_labels = "dedicated=int-first,node.kubernetes.io/lifecycle=spot"
        node_taints = "dedicated=int-first:NoSchedule"
      }
      min_size = 0
      name = "spot-wxt-1c"
      spot_allocation_strategy = "capacity-optimized"
      spot_instance_pools = 0
      type = "worker"
      use_spot_instances = true
    },
    {
      availability_zones = [
        "us-east-1a"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m6i.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "zone1=us-east-1a,dedicated=int-first"
        node_taints = "dedicated=int-first:NoSchedule"
      }
      min_size = 0
      name = "wxt-1a"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1b"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m6i.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "zone2=us-east-1b,dedicated=int-first"
        node_taints = "dedicated=int-first:NoSchedule"
      }
      min_size = 0
      name = "wxt-1b"
      type = "worker"
    },
    {
      availability_zones = [
        "us-east-1c"
      ]
      extra_security_groups = [
        "sg-02cbce54a9ab50a01"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m6i.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 0
      metadata = {
        node_labels = "zone3=us-east-1c,dedicated=int-first"
        node_taints = "dedicated=int-first:NoSchedule"
      }
      min_size = 0
      name = "wxt-1c"
      type = "worker"
    },
    {
      fallback_flavors = [
        "c6a.xlarge",
        "c5a.xlarge",
        "c5.xlarge"
      ]
      flavor = "c6a.xlarge"
      max_size = 1
      metadata = {
        dns_prefix = "thousand-eyes"
        node_labels = "type=thousand-eyes"
      }
      min_size = 0
      name = "thousand-eyes"
      type = "external-media"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "r5a.2xlarge"
  scale_image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  thousand_eyes_node_count = 0
  vpc_name = "aiadwxt-int"
  vpc_routing = true
  worker_count = 2
  worker_flavor = "m6a.4xlarge"
  
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_iam_user_vault_path" {
  value = module.infra.cluster_iam_user_vault_path
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "vpc_routing" {
  value = module.infra.vpc_routing
}

