# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: EXPERIMENTAL
# module-version: v7.23.4-patch6
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# infra-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# dns-credentials-path: secret/data/mccprod/infra/route53/credentials
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-apdxwxt-prd-2.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/cluster?ref=v7.23.4-patch6"
  aws_infra_az = "us-west-2a"
  aws_infra_azs = [
    "us-west-2a",
    "us-west-2b",
    "us-west-2c"
  ]
  aws_infra_region = "us-west-2"
  bastion_count = 1
  bastion_flavor = "t3a.small"
  cidr_node_prefix = 26
  cidr_pods = "auto"
  cidr_svcs = "auto"
  command_and_control = "mccprod"
  create_eip = true
  dev_cluster = false
  dns_credentials_path = "secret/data/mccprod/infra/route53/credentials"
  domain = "prod.infra.webex.com"
  external_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  gateway_name = "apdxwxt-prod-v2"
  health_checks = false
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  ingress_count = 1
  ingress_flavor = "c6a.2xlarge"
  ingress_int_count = 1
  ingress_int_flavor = "c6a.xlarge"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  master_count = 3
  master_flavor = "r6a.4xlarge"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  name = "apdxwxt-prd-2"
  nat_gateway = true
  network_name = "apdxwxt-prod-v2-az1"
  network_names = [
    "apdxwxt-prod-v2-az1",
    "apdxwxt-prod-v2-az2",
    "apdxwxt-prod-v2-az3"
  ]
  node_pools = [
    {
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "c6a.2xlarge",
        "c5a.2xlarge",
        "c5.2xlarge"
      ]
      flavor = "c6a.2xlarge"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 0
      name = "istio-ingress-pool"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2a"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "c6a.2xlarge",
        "c5a.2xlarge",
        "c5.2xlarge"
      ]
      flavor = "c6a.2xlarge"
      max_size = 5
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 1
      name = "istio-ingress-pool-2a"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2b"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "c6a.2xlarge",
        "c5a.2xlarge",
        "c5.2xlarge"
      ]
      flavor = "c6a.2xlarge"
      max_size = 5
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 1
      name = "istio-ingress-pool-2b"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2c"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "c6a.2xlarge",
        "c5a.2xlarge",
        "c5.2xlarge"
      ]
      flavor = "c6a.2xlarge"
      max_size = 5
      metadata = {
        node_labels = "dedicated=istio-ingress"
        node_taints = "dedicated=istio-ingress:NoSchedule"
      }
      min_size = 1
      name = "istio-ingress-pool-2c"
      type = "worker"
    },
    {
      fallback_flavors = [
        "c5a.large",
        "c5.2xlarge"
      ]
      flavor = "c5a.large"
      max_size = 6
      metadata = {
        node_labels = "dedicated=istio-ingress-ciscoint"
        node_taints = "dedicated=istio-ingress-ciscoint:NoSchedule"
      }
      min_size = 3
      name = "istio-ingress-cisco"
      type = "worker"
    },
    {
      fallback_flavors = [
        "r6a.2xlarge",
        "c5a.2xlarge",
        "c5.2xlarge"
      ]
      flavor = "r6a.2xlarge"
      max_size = 5
      metadata = {
        node_labels = "type=optimized-storage-node"
        node_taints = "dedicated=prometheus:NoSchedule"
      }
      min_size = 0
      name = "optimized-storage-pool"
      type = "worker"
    },
    {
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 6
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2a"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 20
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker-2a"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2b"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 20
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker-2b"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2c"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 20
      metadata = {
        node_labels = "type=worker"
      }
      min_size = 0
      name = "worker-2c"
      type = "worker"
    },
    {
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.2xlarge",
        "m5.2xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 10
      metadata = {
        node_labels = "dedicated=prod-apdx"
        node_taints = "dedicated=prod-apdx-tests:NoSchedule"
      }
      min_size = 0
      name = "wxt-tap-apdx"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2a"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 20
      metadata = {
        node_labels = "zone1=us-west-2a,dedicated=prod-apdx"
        node_taints = "dedicated=prod-apdx:NoSchedule"
      }
      min_size = 1
      name = "wxt-us-west-2a"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2b"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 20
      metadata = {
        node_labels = "zone2=us-west-2b,dedicated=prod-apdx"
        node_taints = "dedicated=prod-apdx:NoSchedule"
      }
      min_size = 1
      name = "wxt-us-west-2b"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2c"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "m6a.4xlarge",
        "m5a.4xlarge",
        "m5.4xlarge"
      ]
      flavor = "m6a.4xlarge"
      max_size = 20
      metadata = {
        node_labels = "zone3=us-west-2c,dedicated=prod-apdx"
        node_taints = "dedicated=prod-apdx:NoSchedule"
      }
      min_size = 1
      name = "wxt-us-west-2c"
      type = "worker"
    },
    {
      fallback_flavors = [
        "c6a.xlarge",
        "c5a.xlarge",
        "c5.xlarge"
      ]
      flavor = "c6a.xlarge"
      max_size = 1
      metadata = {
        dns_prefix = "thousand-eyes"
        node_labels = "type=thousand-eyes"
      }
      min_size = 1
      name = "thousand-eyes"
      type = "external-media"
    },
    {
      availability_zones = [
        "us-west-2a"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "r5a.4xlarge",
        "r6i.4xlarge"
      ]
      flavor = "r6a.4xlarge"
      max_size = 5
      metadata = {
        node_labels = "type=optimized-storage-node"
        node_taints = "type=optimized-storage-node:NoSchedule"
      }
      min_size = 1
      name = "optimized-storage-2a"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2b"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "r5a.4xlarge",
        "r6i.4xlarge"
      ]
      flavor = "r6a.4xlarge"
      max_size = 5
      metadata = {
        node_labels = "type=optimized-storage-node"
        node_taints = "type=optimized-storage-node:NoSchedule"
      }
      min_size = 1
      name = "optimized-storage-2b"
      type = "worker"
    },
    {
      availability_zones = [
        "us-west-2c"
      ]
      extra_security_groups = [
        "sg-0dd13bac00988cdc3"
      ]
      fallback_flavors = [
        "r5a.4xlarge",
        "r6i.4xlarge"
      ]
      flavor = "r6a.4xlarge"
      max_size = 5
      metadata = {
        node_labels = "type=optimized-storage-node"
        node_taints = "type=optimized-storage-node:NoSchedule"
      }
      min_size = 1
      name = "optimized-storage-2c"
      type = "worker"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "c6a.2xlarge"
  scale_image_name = "wbx3-focal-1.23.5-containerd-450_aa19634"
  thousand_eyes_node_count = 0
  vpc_name = "apdxwxt-prod-v2"
  vpc_routing = true
  worker_count = 2
  worker_flavor = "c6a.2xlarge"
  
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_iam_user_vault_path" {
  value = module.infra.cluster_iam_user_vault_path
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "vpc_routing" {
  value = module.infra.vpc_routing
}

