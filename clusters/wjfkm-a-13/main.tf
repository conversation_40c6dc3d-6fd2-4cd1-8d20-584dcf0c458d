# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.16.3
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wjfkm-a-13.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/cluster?ref=v7.17.0"
  bastion_count = 1
  bastion_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  cidr_node_prefix = 27
  cidr_pods = "172.26.64.0/18"
  cidr_pods_prefix = 18
  cidr_svcs = "172.23.28.0/22"
  cidr_svcs_prefix = 22
  command_and_control = "mccprod"
  dev_cluster = false
  domain = "prod.infra.webex.com"
  env_name = "wjfk-prod"
  external_media_node_count = 0
  external_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  external_media_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 443
      name = "external-media-https"
      protocol = "tcp"
      to = 444
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-tcp"
      protocol = "tcp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "external-media-cascade-ports-udp"
      protocol = "udp"
      to = 33000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-tcp"
      protocol = "tcp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 49152
      name = "external-media-rtp-ports-udp"
      protocol = "udp"
      to = 65535
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-tcp"
      protocol = "tcp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 5004
      name = "external-media-shared-ports-1-udp"
      protocol = "udp"
      to = 5004
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-tcp"
      protocol = "tcp"
      to = 9000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 9000
      name = "external-media-shared-ports-3-udp"
      protocol = "udp"
      to = 9000
      type = "ingress"
    }
  ]
  external_network = "public-wbx3-3098"
  force_delete = true
  gateway_name = "wjfk-prod"
  health_checks = true
  https_internal_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  image_name = "wbx3-bionic-1.21.5-381_47ccec7"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  ingress_count = 5
  ingress_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  ingress_int_count = 1
  ingress_int_flavor = "8vcpu.16mem.80ssd.0eph"
  ingress_sg_rules = [
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-udp"
      protocol = "udp"
      to = 12000
      type = "ingress"
    },
    {
      cidr = "0.0.0.0/0"
      from = 11000
      name = "ingress-network-monitor-tcp"
      protocol = "tcp"
      to = 12000
      type = "ingress"
    }
  ]
  internal_media_node_count = 0
  internal_media_node_flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"
  internal_mini_media_node_count = 0
  internal_mini_media_node_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  internal_network = "wjfk-prod-v3231"
  internal_provider_network = "provider-wbx3-3096"
  internal_provider_subnets = [
    "10.0.0.0/8"
  ]
  management_network = "provider-wbx3-3096"
  master_count = 3
  master_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  name = "wjfkm-a-13"
  node_pools = [
    {
      flavor = "media.nl.12vcpu.32mem.80ssd.0eph"
      max_size = 15
      min_size = 15
      name = "homer-pool"
      type = "external-large-media"
    },
    {
      max_size = 3
      metadata = {
        node_labels = "type=optimized-storage-node"
      }
      min_size = 3
      name = "optimized-storage-pool"
      type = "worker"
    },
    {
      flavor = "2vcpu.4mem.80ssd.0eph"
      max_size = 1
      metadata = {
        dns_prefix = "thousand-eyes"
        node_labels = "type=thousand-eyes"
      }
      min_size = 1
      name = "thousand-eyes"
      type = "external-media"
    },
    {
      max_size = 24
      metadata = {
        dns_prefix = "internal-mini-media"
        node_labels = "type=hesiod"
      }
      min_size = 24
      name = "hesiod-pool"
      type = "internal-mini-media"
    }
  ]
  optimized_storage_count = 0
  optimized_storage_flavor = "8vcpu.16mem.512ssd.0eph"
  scale_image_name = "wbx3-bionic-1.21.5-381_47ccec7"
  server_group_policies = [
    "anti-affinity"
  ]
  use_floating_ips = false
  worker_count = 3
  worker_flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  
}

output "aws_zone_id" {
  value = module.infra.aws_zone_id
}

output "cluster_hosted_zone" {
  value = module.infra.cluster_hosted_zone
}

output "cluster_node_prefix" {
  value = module.infra.cluster_node_prefix
}

output "cluster_pod_cidr" {
  value = module.infra.cluster_pod_cidr
}

output "cluster_svc_cidr" {
  value = module.infra.cluster_svc_cidr
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}

output "gateway_ips" {
  value = module.infra.gateway_ips
}

output "ingress_address" {
  value = module.infra.ingress_address
}
