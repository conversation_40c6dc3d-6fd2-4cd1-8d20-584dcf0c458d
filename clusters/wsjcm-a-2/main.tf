# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.9.3
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wsjcm-a-2.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = string
  default = "wbx3-bionic-1.18.17-289_173d379"
}

module "k8s" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git////modules/openstack/k8s?ref=v7.9.3"
  cluster-name = "wsjcm-a-2"
  project-name = "wsjc2-prod-v3231"
  domain = "prod.infra.webex.com"
  ssh-user = "ubuntu"
  image-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
  image-name = var.image-name
  scale-image-name = var.image-name
  vault-path = "meetpaas/mccprod/ssh/k8s/wsjcm-a-2"
  join-token-vault-path = "secret/mccprod/kubernetes/wsjcm-a-2/kubeadm"
  kubeadmin-vault-path = "secret/mccprod/kubernetes/wsjcm-a-2/kubeadmin"
  use-floating-ip = false
  floating-ip-pool = "Public-CCP-3094"
  internal-network-name = "wsjc2-prod-v3231"
  management-network-name = "private-wbx3-3099"
  force_delete_enabled = true

  # Bastions
  bastion-node-count = 1
  bastion-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Masters
  k8s-master-count = 3

  # Workers
  k8s-node-count = 5
  worker-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Ingresses
  ingress-controller-count = 2
  ingress-controller-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  # Internal ingresses
  ingress-internal-count = 2

  # cidr ranges
  use-cidr-allocations = true
  cidr-k8s-pods = "***********/19"
  cidr-k8s-svcs = "***********/22"
  cidr-node-prefix = "27"

  health-checks = true
  dev-cluster = false


  custom-external-media-sg-rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]
  custom-ingress-sg-rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]


  thousand-eyes-node-flavor = "4vcpu.8mem.80ssd.0eph"

  optimized-storage-node-count = 3
  optimized-storage-flavor = "8vcpu.16mem.512ssd.0eph"

  internal-media-node-count = 0
  internal-media-node-flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"

  internal-mini-media-node-count = 0
  internal-mini-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  external-media-node-count = 0
  external-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  media-provider-network-name = "private-wbx3-3099"
  media-provider-network-subnets = [
    "10.0.0.0/8",
  ]

  server-group-policies = [
    "anti-affinity",
  ]

  bastion_allowed_remote_ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "************/22",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "*************/21",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
  ]


  https-internal-ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "************/22",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "*************/19",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
    "*************/17",
    "**********/16",
    "************/19",
  ]


  node-pools = [
    {
      name = "external-media-pool"
      type = "external-media"
      min_size = "156"
      max_size = "156"
      flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"
      cpu = "8000m"
      memory = "12Gi"
      metadata = {}
    },    {
      name = "injector-pool"
      type = "internal-mini-media"
      min_size = "4"
      max_size = "4"
      flavor = "<nil>"
      cpu = "8000m"
      memory = "12Gi"
      metadata = {}
    },
  ]


  # Register with Webex Cloud API Gateway






}

output "ingress_address" {
  value = module.k8s.ingress_address
}

output "aws_zone_id" {
  value = module.k8s.aws_zone_id
}

output "cluster_pod_cidr" {
  value = module.k8s.cluster_pod_cidr
}

# Export the cluster k8s pod cidr
output "cluster_svc_cidr" {
  value = module.k8s.cluster_svc_cidr
}

output "cluster_node_prefix" {
  value = module.k8s.cluster_node_prefix
}

# export the gateway node hash
output "gateway_hash" {
  value = module.k8s.gateway_hash
}

output "cluster_hosted_zone" {
  value = module.k8s.cluster_hosted_zone
}

# export the gateway node hash
output "gateway_ips" {
  value = module.k8s.gateway_ips
}
