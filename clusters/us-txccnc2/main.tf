# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.0
# module-version: v10.16.4
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-us-txccnc2.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
      source = "terraform-provider-openstack/openstack"
    }
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/cluster-us-txccnc2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/capi-infra?ref=v10.16.4"
  providers = {
    aws = aws
    aws.pub-dns = aws.pub-dns
    openstack = openstack
  }
  admin_port = 6443
  availability_zones = [
    {
      compute = "nova"
      volume = "nova"
    }
  ]
  aws_infra_region = "us-east-1"
  bastion_block_storage = true
  bastion_count = 1
  bastion_flavor = "2vCPUx4GB"
  bastion_use_fip_for_ssh = true
  command_and_control = "mccprod"
  control_network_type = "iso_network"
  control_plane_network_type = "iso_network"
  control_plane_provider = "amphora"
  dev_cluster = false
  domain = "prod.infra.webex.com"
  enable_availability_zone_hints = false
  floating_ip_pool = "tenant-internal-sm-fip-net"
  force_delete = true
  image_name = "wbx3-capi-noble-1.31.5-c8d-amd64-v2.25.5.0"
  infra_service_domain = "https://infra.int.mccprod.prod.infra.webex.com"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20",
    "***********/16",
    "**********/16"
  ]
  name = "us-txccnc2"
  network_mission_tag = "platform"
  network_name = "c-alln-p0-0-ha1"
  pod_subnet = "***********/18"
  security_groups = {
    istio_private = {
      rules = [
        {
          cidrs = [
            "**********/16"
          ]
          from = 8443
          name = "https"
          protocol = "tcp"
          to = 8443
          type = "ingress"
        },
        {
          cidrs = [
            "**********/16"
          ]
          from = 443
          name = "lb-tcp-443"
          protocol = "tcp"
          to = 443
          type = "ingress"
        },
        {
          cidrs = [
            "**********/16"
          ]
          from = 15020
          name = "lb-tcp-15020"
          protocol = "tcp"
          to = 15020
          type = "ingress"
        },
        {
          cidrs = [
            "**********/16"
          ]
          from = 15021
          name = "lb-tcp-15021"
          protocol = "tcp"
          to = 15021
          type = "ingress"
        }
      ]
    }
  }
  self_managed_lb_port_floating_ip = {
    enabled = true
    pool = "tenant-internal-sm-fip-net"
  }
  
}
output "fixed_ip" {
  value = module.infra.fixed_ip
}
output "hosted_zone_id" {
  value = module.infra.hosted_zone_id
}
output "hosted_zone_name" {
  value = module.infra.hosted_zone_name
}

