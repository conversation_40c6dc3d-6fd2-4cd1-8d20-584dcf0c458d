# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.6.5
# template-version: EXPERIMENTAL destroy
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wjfkm-a-14-scale.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = "string"
  default = "wbx3-bionic-1.18.17-206_3839538"
}

data "terraform_remote_state" "main" {
  backend = "s3"
  config = {
    bucket = "tfstate-mccprod"
    key = "terraform-state/cluster-wjfkm-a-14.tfstate"
    region = "us-east-2"
  }
}


module "k8s" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/scale?ref=v7.6.8-internal-media-fix"
  cluster-name = "wjfkm-a-14"
  project-name = "wjfk-prod"
  domain = "prod.infra.webex.com"
  ssh-user = "ubuntu"
  image-flavor = "8vcpu.32mem.80ssd.0eph"
  image-name = "${var.image-name}"
  scale-image-name = "${var.image-name}"
  vault-path = "secret/mccprod/infra/wjfk-prod/wjfkm-a-14/k8s/ssh/public_key"
  vault-bastion-path = "secret/mccprod/infra/wjfk-prod/wjfkm-a-14/bastion/ssh/public_key"
  join-token-vault-path = "secret/mccprod/kubernetes/wjfkm-a-14/kubeadm"
  kubeadmin-vault-path = "secret/mccprod/kubernetes/wjfkm-a-14/kubeadmin"
  use-floating-ip = false
  floating-ip-pool = "public-wbx3-3097"
  internal-network-name = "wjfk-prod-v3230"
  management-network-name = "Private-CCP-3095"
  force_delete_enabled = true

  # Bastions
  bastion-node-count = 1
  bastion-flavor = "2vcpu.4mem.80ssd.0eph"

  # Masters
  k8s-master-count = 3

  # Workers
  k8s-node-count = 6
  worker-flavor = "8vcpu.32mem.80ssd.0eph"

  # Ingresses
  ingress-controller-count = 2
  ingress-controller-flavor = "8vcpu.16mem.80ssd.0eph"

  # Internal ingresses
  ingress-internal-count = 2

  # cidr ranges
  use-cidr-allocations = true
  cidr-k8s-pods = "***********/19"
  cidr-k8s-svcs = "***********/22"
  cidr-node-prefix = "27"

  health-checks = true
  dev-cluster = false


  custom-external-media-sg-rules = [{"name"="external-media-https","protocol"="tcp","from"="443","to"="444","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-tcp","protocol"="tcp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-cascade-ports-udp","protocol"="udp","from"="11000","to"="33000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-tcp","protocol"="tcp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-rtp-ports-udp","protocol"="udp","from"="49152","to"="65535","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-tcp","protocol"="tcp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-1-udp","protocol"="udp","from"="5004","to"="5004","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-tcp","protocol"="tcp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-2-udp","protocol"="udp","from"="33434","to"="33434","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-tcp","protocol"="tcp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="external-media-shared-ports-3-udp","protocol"="udp","from"="9000","to"="9000","cidr"="0.0.0.0/0","type"="ingress"}]
  custom-ingress-sg-rules = [{"name"="ingress-network-monitor-udp","protocol"="udp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"},{"name"="ingress-network-monitor-tcp","protocol"="tcp","from"="11000","to"="12000","cidr"="0.0.0.0/0","type"="ingress"}]

  thousand-eyes-node-count = 0


  optimized-storage-node-count = 3
  optimized-storage-flavor = "8vcpu.16mem.512ssd.0eph"

  internal-media-node-count = 0
  internal-media-node-flavor = "PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa"

  internal-mini-media-node-count = 0
  internal-mini-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  external-media-node-count = 0
  external-media-node-flavor = "Spark-Media.8vcpu.16mem.80ssd.0eph.numa"

  media-provider-network-name = "Private-CCP-3095"
  media-provider-network-subnets = [
    "***********/23",
    "***********/22",
    "************/23",
  ]

  server-group-policies = [
    "anti-affinity",
  ]

  bastion_allowed_remote_ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "************/22",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "*************/21",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
  ]


  https-internal-ips = [
    "**********/14",
    "***********/16",
    "************/24",
    "***********/24",
    "10.0.0.0/8",
    "*************/23",
    "************/24",
    "**********/16",
    "**********/14",
    "************/22",
    "**********/16",
    "************/20",
    "*************/19",
    "**********/16",
    "**********/12",
    "*************/19",
    "***********/20",
    "*************/19",
    "**********/16",
    "**********/19",
    "*************/17",
    "**********/16",
    "************/19",
  ]


  node-pools = [
    {
      name = "internal-media-pool"
      type = "internal-media"
      min_size = "5"
      max_size = "5"
      flavor = "<nil>"
      cpu = "8000m"
      memory = "12Gi"
      metadata = {}
    },    {
      name = "injector-pool"
      type = "internal-mini-media"
      min_size = "2"
      max_size = "2"
      flavor = "<nil>"
      cpu = "8000m"
      memory = "12Gi"
      metadata = {}
    },    {
      name = "external-media-pool"
      type = "external-media"
      min_size = "12"
      max_size = "12"
      flavor = "<nil>"
      cpu = "8000m"
      memory = "12Gi"
      metadata = {}
    },    {
      name = "external-large-media-pool"
      type = "external-large-media"
      min_size = "2"
      max_size = "2"
      flavor = "PROD-Spark-Media.47vcpu.64mem.80ssd.0eph.numa"
      cpu = "8000m"
      memory = "12Gi"
      metadata = {}
    },
  ]

  k8s-node-pool-count = 0
  ingress-controller-pool-count = 0
  ingress-internal-pool-count = 0
  internal-media-pool-count = 5
  internal-mini-media-pool-count = 2
  external-media-pool-count = 12

  master-ips = data.terraform_remote_state.main.outputs.master_ips
  cluster-hosted-zone = data.terraform_remote_state.main.outputs.cluster_hosted_zone
  gateway-ips = data.terraform_remote_state.main.outputs.gateway_ips
}
