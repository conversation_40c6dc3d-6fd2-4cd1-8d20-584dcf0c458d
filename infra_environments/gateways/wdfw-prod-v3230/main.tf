# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  / 
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /  
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/   
#
# codegen-version: v0.3.6
# template-version: 0.4.17-EXPERIMENTAL-c45ef3d
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/gateway_wdfw-prod-v3230.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = string
  default = "wbx3-bionic-hardened-20200810"
}

module "infra_gateway" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/gateway?ref=0.12_openstack_move_around"
  domain                    = "prod.infra.webex.com"

  dev-cluster               = "false"

  ssh-user                  = "ubuntu"
  gateway-node-count        = "2"
  force_delete_enabled      = "true"
  project-name              = "wdfw-prod-v3230"
  external-network-name     = "Public-CCP-3094"
  internal-network-name     = "wdfw-prod-v3230"
  gateway-image-name                = "${var.image-name}"
  gateway-flavor            = "2vcpu.4mem.80ssd.0eph"
  use-floating-ip           = "false"
  vault-path                = "secret/mccprod/infra/wdfw-prod/gateway/ssh/public_key"
  mgmt-ips                  = [
    "**********/14",    "***********/16",    "************/24",    "***********/24",    "10.0.0.0/8",    "*************/23",    "************/24",    "**********/16",    "**********/14",    "************/22",    "**********/16",    "************/20",    "*************/19",    "**********/16",    "**********/12",    "*************/21",    "***********/20",    "*************/19",    "**********/16",    "**********/19",
  ]
}

output "gateway_hash" {
  value = module.infra_gateway.gateway_hash
}
