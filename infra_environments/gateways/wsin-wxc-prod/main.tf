# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.11.5
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/gateway_wsin-wxc-prod.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = string
  default = "wbx3-bionic-1.21.5-341_a67d77d"
}

module "infra_gateway" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/gateway?ref=ocp_gw_provider_routing"
  domain                    = "prod.infra.webex.com"

  dev-cluster               = false

  ssh-user                  = "ubuntu"
  gateway-node-count        = 3
  force_delete_enabled      = true
  project-name              = "wsin-wxc-prod"
  external-network-name     = "wbx3-public-3096"
  internal-network-name     = "wsin-wxc-prod-default"
  internal-provider-network-name     = "wbx3-provider-3097"
  gateway-image-name        = "${var.image-name}"
  gateway-flavor            = "4vcpu.8mem.80ssd.0eph"
  use-floating-ip           = false

  vault-path                = "ssh/gateway/wsin-wxc-prod/config/ca"
  mgmt-ips                  = [
    "**********/14",    "***********/16",    "************/24",    "***********/24",    "10.0.0.0/8",    "*************/23",    "************/24",    "**********/16",    "**********/14",    "************/22",    "**********/16",    "************/20",    "*************/19",    "**********/16",    "**********/12",    "*************/21",    "***********/20",    "*************/19",    "**********/16",    "**********/19",
  ]
}

output "gateway_hash" {
  value = module.infra_gateway.gateway_hash
}
