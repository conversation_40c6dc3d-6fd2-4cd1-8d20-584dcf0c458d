# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  / 
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /  
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/   
#                                                                                   
#
# codegen-version: v0.3.6
# template-version: 0.4.17
# -----------------------------------------------


terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/gateway_a-euwest-2c.tfstate"
    region = "us-east-2"
  }
}



variable "AWS_ACCESS_KEY_ID" {
  description = "AWS Access Key"
}

variable "AWS_SECRET_ACCESS_KEY" {
  description = "AWS Secret Key"
}

variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "eu-west-2"
}


provider "aws" {
  access_key = "${var.AWS_ACCESS_KEY_ID}"
  secret_key = "${var.AWS_SECRET_ACCESS_KEY}"
  region     = "${var.AWS_INFRA_REGION}"
}

variable "image-name" {
  type = "string"
  default = "wbx3-bionic-hardened-26032020-1_63b4395"
}
module "infra_gateway" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git?ref=v0.4.28//modules/aws/infra_gateway"
  domain             = "prod.infra.webex.com"

  gateway-name       = "a-euwest-2c"
  vpc-name       = "alhrm-vpc"
  gateway-node-count = "2"
  gateway-flavor     = "c5.2xlarge"
  gateway-image-name = "${var.image-name}"  
  vault-path         = "secret/mccprod/infra/a-euwest-2c/gateway/ssh/public_key"
  aws-az             = "eu-west-2c"
  # if eip-on-external-media-dx is true, the subnet' names will be "public-dx-private-{az}" and "cluster-dx-private-{az}"

  create-eip  = "true"
  eip-pool-id  = "ipv4pool-ec2-0758d63b81beacafe"

  mgmt-ips                  = [
    "**********/14",    "***********/16",    "************/24",    "***********/24",    "10.0.0.0/8",    "*************/23",    "************/24",    "**********/16",    "**********/14",    "************/22",    "**********/16",    "************/20",    "*************/19",    "**********/16",    "**********/12",    "*************/21",    "***********/20",    "*************/19",    "**********/16",    "**********/19",
  ]
}
