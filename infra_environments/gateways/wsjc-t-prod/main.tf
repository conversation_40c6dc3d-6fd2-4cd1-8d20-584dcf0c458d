# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.10.2
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/gateway_wsjc-t-prod.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = string
  default = "wbx3-bionic-1.18.17-281_173d379"
}

module "infra_gateway" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/gateway?ref=v7.10.6"
  domain                    = "prod.infra.webex.com"

  dev-cluster               = false

  ssh-user                  = "ubuntu"
  gateway-node-count        = 2
  force_delete_enabled      = true
  project-name              = "wsjc-t-prod"
  external-network-name     = "public-floating-3086"
  internal-network-name     = "wsjc-t-prod"
  internal-provider-network-name     = "provider-200"
  gateway-image-name        = "${var.image-name}"
  gateway-flavor            = "8vcpu.16mem.80ssd.0eph"
  use-floating-ip           = false

  vault-path                = "meetpaas/mccprod/ssh/gateway/wsjc-t-prod"
  mgmt-ips                  = [
    "**********/14",    "***********/16",    "************/24",    "***********/24",    "10.0.0.0/8",    "*************/23",    "************/24",    "**********/16",    "**********/14",    "************/22",    "**********/16",    "************/20",    "*************/19",    "**********/16",    "**********/12",    "*************/21",    "***********/20",    "*************/19",    "**********/16",    "**********/19",
  ]
}

output "gateway_hash" {
  value = module.infra_gateway.gateway_hash
}
