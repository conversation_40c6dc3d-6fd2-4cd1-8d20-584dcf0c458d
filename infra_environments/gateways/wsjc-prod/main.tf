# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: EXPERIMENTAL
# template-version: v0.1-beta-9
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/gateway_wsjc-prod.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = "string"
  default = "kube-ubuntu-18.04-new-hardened-41_8d54267"
}

module "infra_gateway" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git?ref=v0.2.3//modules/infra_gateway"
  domain                    = "prod.infra.webex.com"
  ssh-user                  = "ubuntu"
  gateway-node-count        = "2"
  force_delete_enabled      = "true"
  project-name              = "wsjc-prod"
  external-network-name     = "public-floating-3050"
  internal-network-name     = "wsjc-prod-default"
  gateway-image-name                = "${var.image-name}"
  gateway-flavor            = "4vcpu.8mem.80ssd.0eph"
  use-floating-ip           = "false"
  vault-path                = "secret/mccprod/infra/wsjc-prod/gateway/ssh/public_key"
  mgmt-ips                  = [
    "**********/14",    "***********/16",    "************/24",    "***********/24",    "***********/24",    "10.0.0.0/8",    "*************/26",    "*************/23",    "***********/23",    "************/24",    "**********/16",    "**********/14",    "************/22",    "**********/16",    "************/21",    "************/20",    "************/21",    "*************/19",    "**********/16",
  ]
}
