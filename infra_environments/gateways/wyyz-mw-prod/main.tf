# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.14.5
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/gateway_wyyz-mw-prod.tfstate"
    region = "us-east-2"
  }
}


variable "image-name" {
  type = string
  default = "wbx3-focal-1.21.5-fy23q1"
}

module "gateway" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/gateway?ref=v7.14.5"
  domain                    = "prod.infra.webex.com"

  dev_cluster               = false

  ssh_user                  = "ubuntu"
  gateway_count             = 2
  force_delete              = true
  name                      = "wyyz-mw-prod"
  external_network          = "provider-414"
  internal_network          = "provider-v101"
  internal_provider_network = "provider-v423"
  gateway_image_name        = "${var.image-name}"
  gateway_flavor            = "gv.4vcpu.8mem.0ssd.0eph"
  use_floating_ips          = false
  gateway_block_storage = true

  mgmt_remote_ips           = [
    "10.0.0.0/8",    "***********/16",    "**********/16",    "**********/12",    "**********/14",    "*************/23",    "************/24",    "***********/24",    "***********/21",    "*************/19",    "*************/21",    "*************/24",    "**********/14",    "**********/16",    "**********/19",    "************/20",    "***********/20",    "**********/16",    "***********/20",    "***********/20",
  ]
}

output "gateway_hash" {
  value = module.gateway.gateway_hash
}
