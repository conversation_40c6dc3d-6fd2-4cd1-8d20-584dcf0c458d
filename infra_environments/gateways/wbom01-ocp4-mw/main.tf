# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.2
# template-version: EXPERIMENTAL
# module-version: v7.23.4
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/gateway_wbom01-ocp4-mw.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/gateway?ref=v7.23.4"
  dev_cluster = false
  domain = "prod.infra.webex.com"
  external_network = "Pub-Calliope-Telephony-cmr4-191"
  force_delete = true
  gateway_count = 2
  gateway_flavor = "gv.4vcpu.8mem.0ssd.0eph"
  gateway_image_name = "wbx3-focal-1.23.5-containerd-mw_20230508"
  internal_network = "wbom-mw-prod"
  internal_provider_network = "provider-v990"
  mgmt_remote_ips = [
    "10.0.0.0/8",
    "***********/16",
    "**********/16",
    "**********/12",
    "**********/14",
    "*************/23",
    "************/24",
    "***********/24",
    "***********/21",
    "*************/19",
    "*************/21",
    "*************/24",
    "**********/14",
    "**********/16",
    "**********/19",
    "************/20",
    "***********/20",
    "**********/16",
    "***********/20",
    "***********/20"
  ]
  name = "wbom01-ocp4-mw"
  server_group_policies = [
    "anti-affinity"
  ]
  use_floating_ips = false
  gateway_block_storage = true
  
}

output "gateway_hash" {
  value = module.infra.gateway_hash
}
