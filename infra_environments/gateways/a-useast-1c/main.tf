# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  / 
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /  
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/   
#                                                                                   
#
# codegen-version: v7.0.9
# template-version: EXPERIMENTAL destroy
# -----------------------------------------------

data "vault_generic_secret" "aws_dns_credential" {
  path=replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

data "vault_generic_secret" "aws_infra_credential" {
  path=replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}


terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/gateway_a-useast-1c.tfstate"
    region = "us-east-2"
  }
}



variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "us-east-1"
}

# Infra Provider
provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = var.AWS_INFRA_REGION
}

# DNS Provider
provider "aws" {
  alias = "dns"
  access_key = data.vault_generic_secret.aws_dns_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_dns_credential.data["AWS_SECRET_ACCESS_KEY"]
  region = data.vault_generic_secret.aws_dns_credential.data["AWS_DEFAULT_REGION"]
  max_retries = 100
 }


variable "image-name" {
  type = string
  default = "wbx3-bionic-hardened-26032020-1_63b4395"
}
module "infra_gateway" {
  source             = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/infra_gateway?ref=v7.1.1"
  domain             = "prod.infra.webex.com"

  dev-cluster        = "false"

  gateway-name       = "a-useast-1c"
  vpc-name           = "aiadm3-vpc"
  gateway-node-count = "2"
  gateway-flavor     = "c5n.2xlarge"
  gateway-image-name = var.image-name
  vault-path         = "secret/mccprod/infra/a-useast-1c/gateway/ssh/public_key"
  aws-az             = "us-east-1c"
  # if eip-on-external-media-dx is true, the subnet' names will be "public-dx-private-{az}" and "cluster-dx-private-{az}"




  mgmt-ips                  = [
    "**********/14",    "***********/16",    "************/24",    "***********/24",    "10.0.0.0/8",    "*************/23",    "************/24",    "**********/16",    "**********/14",    "************/22",    "**********/16",    "************/20",    "*************/19",    "**********/16",    "**********/12",    "*************/21",    "***********/20",    "*************/19",    "**********/16",    "**********/19",
  ]








}

output "gateway_hash" {
  value = module.infra_gateway.gateway_hash
}
