# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.32
# template-version: 7.18.32
# module-version: v10.7.16
# wbx3-infra version: <nil>
# -----------------------------------------------

name: afso1-production-ha2
env_name: afso1-ha2

# Resource Values
absorb_public_root_domain: true
availability_zones:
    az1:
        availability_zone: af-south-1a
    az2:
        availability_zone: af-south-1b
    az3:
        availability_zone: af-south-1c
aws_infra_az: af-south-1a
core_tgw_amazon_asn: **********
dx_amazon_owner_account_id: ************
egress_vpc:
    vpc_index: 2
    webx_dc_nat_range: 10.249.84.128/26
enable_dx_association: true
environment: p2
ha_deployment_side: second
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
ingress_vpc:
    cidr_private_services: 10.249.84.0/25
    cidr_public_services: disabled
    vpc_index: 2
inspection_vpc:
    aws_gateway_asn: **********
    cidr_prv_direct_management: disabled
    enabled: false
    vpc_index: 2
media_vpcs:
    calling:
        aws_gateway_asn: **********
        cidr_access_private: 10.249.103.0/24
        cidr_access_public: 144.196.116.128/25
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled
        cidr_trunk_private: 10.249.85.128/25
        cidr_trunk_public: 144.196.117.128/25
        enabled: true
        subnets:
            access-private:
                nat_private: true
            access-public:
                nat_private: true
            isolated:
                routes:
                    10.0.0.0/8:
                        nat_gateway_subnet: access-private
                        nat_gateway_type: private
                    144.196.116.128/27:
                        nat_gateway_subnet: access-public
                        nat_gateway_type: private
                    144.196.116.160/27:
                        nat_gateway_subnet: access-public
                        nat_gateway_type: private
                    144.196.116.192/27:
                        nat_gateway_subnet: access-public
                        nat_gateway_type: private
                    144.196.117.128/27:
                        nat_gateway_subnet: access-public
                        nat_gateway_type: private
                    ***************/27:
                        nat_gateway_subnet: access-public
                        nat_gateway_type: private
                    ***************/27:
                        nat_gateway_subnet: access-public
                        nat_gateway_type: private
        vpc_index: 2
module_version: v10.7.16
outbound_resolver_rules:
    - domain_name: broadcloud.org
      resolver_rule_name: broadcloud-resolver
      targets:
        - target_ip: *************
        - target_ip: *************
        - target_ip: ************
        - target_ip: ************
    - domain_name: cisco.com
      resolver_rule_name: cisco-com_outbound
      targets:
        - target_ip: ************
        - target_ip: ************
    - domain_name: webex.com
      resolver_rule_name: webex-com_outbound
      targets:
        - target_ip: ************
        - target_ip: ************
primary_cidr_prefix_length: 17
private_root_domain: disabled
public_ipv4_pool: ipv4pool-ec2-089b51642b206b103
public_root_domain: prod.infra.webex.com
region: af-south-1
root_domain: webex.com
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
tgw_dx_gateway_id: adc9a3d8-0ac1-4f1c-819b-e55c8097093a
vgw_dx_gateway_id: d48d9fb9-7ae5-4d53-bf25-70f0e275544e

# No Blueprint defined values used


# Environment Values
address_pools:
    pods_0: **********/15
    pods_1: **********/13
    pods_2: **********/12
    pods_3: **********/14
admin_port: 6442
backend: s3
base_image: <replace-me>
base_k8s_image: <replace-me>
bastion_count: 1
bastion_flavor: 2vcpu.4mem.80ssd.0eph
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cidr_node_prefix: 26
cidr_nodes: <replace-me>
cidr_svcs_prefix: 22
cloud_service_provider: aws
command_and_control: mccprod
consul_host: consul.int.mcc01.prod.infra.webex.com
dev_cluster: false
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
external_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
external_media_sg_rules:
    - cidr: 0.0.0.0/0
      from: 443
      name: external-media-https
      protocol: tcp
      to: 444
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-tcp
      protocol: tcp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: external-media-cascade-ports-udp
      protocol: udp
      to: 33000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-tcp
      protocol: tcp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 49152
      name: external-media-rtp-ports-udp
      protocol: udp
      to: 65535
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-tcp
      protocol: tcp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 5004
      name: external-media-shared-ports-1-udp
      protocol: udp
      to: 5004
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-tcp
      protocol: tcp
      to: 9000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9000
      name: external-media-shared-ports-3-udp
      protocol: udp
      to: 9000
      type: ingress
force_delete: true
gateway_count: 2
gateway_flavor: 4vcpu.8mem.80ssd.0eph
gateway_name: <not-used>
gluster_count: 0
gluster_flavor: 8vcpu.16mem.512ssd.0eph
health_checks: true
https_internal_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - **********/16
image_flavor: 8vcpu.32mem.80ssd.0eph
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infractl_version: v7.18.32
ingress_count: 3
ingress_flavor: 8vcpu.16mem.80ssd.0eph
ingress_int_count: 2
ingress_int_flavor: 8vcpu.16mem.80ssd.0eph
ingress_sg_rules:
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-udp
      protocol: udp
      to: 12000
      type: ingress
    - cidr: 0.0.0.0/0
      from: 11000
      name: ingress-network-monitor-tcp
      protocol: tcp
      to: 12000
      type: ingress
internal_media_node_flavor: PROD-Spark-Media.35vcpu.64mem.80ssd.0eph.numa
internal_mini_media_node_flavor: Spark-Media.8vcpu.16mem.80ssd.0eph.numa
master_count: 3
mgmt_remote_ips:
    - 10.0.0.0/8
    - ***********/16
    - **********/16
    - **********/12
    - **********/14
    - *************/23
    - ************/24
    - ***********/24
    - ***********/21
    - *************/19
    - *************/21
    - *************/24
    - **********/14
    - **********/16
    - **********/19
    - ************/20
    - ***********/20
    - **********/16
    - ***********/20
    - ***********/20
    - ***********/16
    - **********/16
nameservers:
    - <replace-me>
oidc_client_id: CVvR9wNunKmQSniMRmO5gMbOUGw4oYbm
oidc_issuer_namespace: meetpaas
oidc_issuer_url: https://keeper.cisco.com/v1/meetpaas/identity/oidc/provider/mccprod
oidc_login_scope: kubernetes-prod
oidc_provider_name: mccprod
optimized_storage_count: 3
optimized_storage_flavor: 8vcpu.16mem.512ssd.0eph
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
pki_roles:
    - k8s-admin
    - k8s-read-only
pod_subnet_pool: pods_2
provisioning_extra_args: ""
provisioning_module_version: master
s3_bucket_region: us-east-2
server_group_policies:
    - anti-affinity
terraform_version: v1.5.0
thousand_eyes_count: 0
use_floating_ips: false
use_provider_template: true
windows_sg_rules:
    - cidr: 0.0.0.0/0
      from: 8445
      name: msteams-media
      protocol: tcp
      to: 8446
      type: ingress
    - cidr: 0.0.0.0/0
      from: 9445
      name: msteams-signalling
      protocol: tcp
      to: 9446
      type: ingress
worker_count: 12
worker_flavor: 8vcpu.32mem.80ssd.0eph

# No Manifest default values used
