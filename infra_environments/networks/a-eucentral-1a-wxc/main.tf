# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.4
# template-version: EXPERIMENTAL
# module-version: v7.23.4
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_a-eucentral-1a-wxc.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/network?ref=v7.23.4"
  aws_infra_az = "eu-central-1a"
  aws_infra_region = "eu-central-1"
  cidr_nodes = "**********/18"
  create_eip = true
  create_provider_subnet = true
  create_vpc = true
  eip_pool_id = "ipv4pool-ec2-0ac4d3516f9afce14"
  ext_cidr_nodes = "**********/18"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  name = "a-eucentral-1a-wxc"
  nameservers = [
    "AmazonProvidedDNS"
  ]
  nat_gateway = true
  provider_cidr_nodes = "************/26"
  vpc_additional_cidrs = [
    "**********/16",
    "**********/16"
  ]
  vpc_cidr_block = "************/24"
  vpc_name = "a-wxc-eucentral-prod"
  
}

output "name" {
  value = module.infra.name
}
