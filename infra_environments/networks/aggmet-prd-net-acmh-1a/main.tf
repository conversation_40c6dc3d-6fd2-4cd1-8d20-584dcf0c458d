# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  /
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/
#
#
# codegen-version: v7.10.12
# template-version: EXPERIMENTAL
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/lma/prd/aws-metrics-wbx3", "/data/", "/")
}


terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key    = "terraform-state/network_aggmet-prd-net-acmh-1a.tfstate"
    region = "us-east-2"
  }
}


variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default     = "us-east-2"
}

# Infra AWS Provider
provider "aws" {
  access_key  = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key  = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region      = var.AWS_INFRA_REGION
  max_retries = 100
}

module "infra_network" {
  source          = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/infra_network?ref=v7.10.14"
  domain          = "prod.infra.webex.com"
  vpc-name        = "wbx3-aggmet-maz-prd-acmh"
  igw-name        = "wbx3-aggmet-maz-prd-acmh-igw"
  aws-az          = "us-east-2a"
  name            = "aggmet-prd-net-acmh-1a"
  cidr-nodes      = "*********/16"
  vpc-public-cidr = "*********/18"
  vpc-cidr-block  = "*********/16"
  vpc-additional-cidrs = [
    "*********/16",
    "*********/16",
    "*********/16",
  ]
  create-vpc  = true
  create-eip  = true
  nat-gateway = true
  # if eip-on-external-media-dx is true, the subnet' names will be "public-dx-private-{az}" and "cluster-dx-private-{az}"







}

output "name" {
  value = module.infra_network.name
}
