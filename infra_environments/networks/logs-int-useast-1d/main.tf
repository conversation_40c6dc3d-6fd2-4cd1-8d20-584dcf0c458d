# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  / 
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /  
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/   
#                                                                                   
#
# codegen-version: v0.3.6
# template-version: EXPERIMENTAL
# -----------------------------------------------


terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_logs-int-useast-1d.tfstate"
    region = "us-east-2"
  }
}


variable "AWS_ACCESS_KEY_ID" {
  description = "AWS Access Key"
}

variable "AWS_SECRET_ACCESS_KEY" {
  description = "AWS Secret Key"
}

variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "us-east-1"
}

variable "AWS_INFRA_AZ" {
  description = "AWS Infra AZ"
  default = "us-east-1d"
}

provider "aws" {
  access_key = var.AWS_ACCESS_KEY_ID
  secret_key = var.AWS_SECRET_ACCESS_KEY
  region     = var.AWS_INFRA_REGION
}

module "infra_network" {
  source          = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/infra_network?ref=v7.1.1"
  domain          = "prod.infra.webex.com"
  vpc-name        = "wbx3-logs-int-useast-1d"
  aws-az          = "us-east-1d"
  cidr-nodes      = "*********/20"
  vpc-public-cidr = "**********/20"
  vpc-cidr-block  = "*********/16"
  create-vpc      = true
  # if eip-on-external-media-dx is true, the subnet' names will be "public-dx-private-{az}" and "cluster-dx-private-{az}"







}
