# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.6
# template-version: EXPERIMENTAL
# module-version: v8.1.8_with_aws_efs
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_a-uswest-2c-mw-prod.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/network?ref=96d2955454ea33b8b2d77eddeb33888835d28466"
  aws_infra_az = "us-west-2c"
  aws_infra_region = "us-west-2"
  cidr_nodes = "**********/16"
  create_eip = true
  create_provider_subnet = true
  create_vpc = false
  eip_pool_id = "ipv4pool-ec2-002c70c81368c701c"
  ext_cidr_nodes = "************/18"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  name = "a-uswest-2c-mw-prod"
  nameservers = [
    "************",
    "************"
  ]
  nat_gateway = true
  provider_cidr_nodes = "*************/26"
  vpc_name = "aore-mw-prod"
  
}

output "name" {
  value = module.infra.name
}
