# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.16.3
# template-version: EXPERIMENTAL
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_anrtwxt-prod-az2.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/network?ref=v7.23.4"
  aws_infra_az = "ap-northeast-1d"
  aws_infra_region = "ap-northeast-1"
  cidr_nodes = "***********/18"
  control_cidr_block = "************/22"
  create_control_subnet = true
  create_eip = true
  create_vpc = false
  eip_pool_id = "ipv4pool-ec2-09e959e045c1b1dd4"
  ext_cidr_nodes = "************/22"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  name = "anrtwxt-prod-az2"
  nameservers = [
    "************",
    "************"
  ]
  nat_gateway = true
  public_control = true
  vpc_name = "anrtwxt-prod"
  
}

output "name" {
  value = module.infra.name
}
