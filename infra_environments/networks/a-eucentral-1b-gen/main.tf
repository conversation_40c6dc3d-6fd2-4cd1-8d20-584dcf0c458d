# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  /
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/
#
#
# codegen-version: v7.10.8
# template-version: 0.4.42-EXPERIMENTAL-81500db
# -----------------------------------------------

data "vault_generic_secret" "aws_infra_credential" {
  path=replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}


terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_a-eucentral-1b-gen.tfstate"
    region = "us-east-2"
  }
}


variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "eu-central-1"
}

# Infra AWS Provider
provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = var.AWS_INFRA_REGION
  max_retries = 3
}

module "infra_network" {
  source          = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/infra_network?ref=v7.10.15"
  domain          = "prod.infra.webex.com"
  vpc-name        = "afram-vpc-gen"
  igw-name        = "afram-vpc-gen-igw"
  aws-az          = "eu-central-1b"
  name            = "a-eucentral-1b-gen"
  cidr-nodes      = "**********/16"
  vpc-public-cidr = "***********/18"
  vpc-cidr-block  = "<nil>"
  create-vpc      = false
  create-eip = true
  nat-gateway  = true
  # if eip-on-external-media-dx is true, the subnet' names will be "public-dx-private-{az}" and "cluster-dx-private-{az}"







}

output "name" {
  value = module.infra_network.name
}
