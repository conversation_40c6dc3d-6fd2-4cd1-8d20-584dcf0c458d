# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.12
# template-version: 7.18.12
# module-version: v10.2.17
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------

data "vault_generic_secret" "infra_credential" {
  path = replace("secret/data/mccprod/infra/wmel01-ocp4-prod/openstack", "/data/", "/")
}

provider "openstack" {
  # v3
  application_credential_id     = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_ID"], null)
  application_credential_secret = try(data.vault_generic_secret.infra_credential.data["OS_APPLICATION_CREDENTIAL_SECRET"], null)
  # v2
  tenant_name = try(data.vault_generic_secret.infra_credential.data["OS_TENANT_NAME"], null)
  user_name   = try(data.vault_generic_secret.infra_credential.data["OS_USERNAME"], null)
  password    = try(data.vault_generic_secret.infra_credential.data["OS_PASSWORD"], null)

  # common
  auth_url = data.vault_generic_secret.infra_credential.data["OS_AUTH_URL"]
  region   = data.vault_generic_secret.infra_credential.data["OS_REGION_NAME"]
}

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_wmel01-ocp4-media.tfstate"
    region = "us-east-2"
  }
  required_providers {
    openstack = {
      source = "terraform-provider-openstack/openstack"
    }
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/network_wmel01-ocp4-media.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/network?ref=v10.2.17"
  providers = {
    openstack = openstack
  }
  cidr_nodes = "************/21"
  external_network = "public-417"
  gateway_name = "wmel01-ocp4-media"
  internal_network = "wmel01-ocp4-media"
  nameservers = [
    "************",
    "************"
  ]
  use_floating_ips = false
  
}
output "external_network" {
  value = module.infra.external_network
}
output "internal_network" {
  value = module.infra.internal_network
}
output "internal_network_subnet" {
  value = module.infra.internal_network_subnet
}


