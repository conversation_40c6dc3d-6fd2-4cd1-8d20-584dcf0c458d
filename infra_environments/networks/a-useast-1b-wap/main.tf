# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.8
# template-version: EXPERIMENTAL
# module-version: v8.2.0
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_a-useast-1b-wap.tfstate"
    region = "us-east-2"
  }
}


module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/network?ref=v8.2.0"
  aws_infra_az = "us-east-1b"
  aws_infra_region = "us-east-1"
  cidr_nodes = "**********/16"
  create_eip = true
  create_provider_subnet = true
  create_vpc = false
  ext_cidr_nodes = "***********/18"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  name = "a-useast-1b-wap"
  nameservers = [
    "************",
    "************"
  ]
  nat_gateway = true
  provider_cidr_nodes = "*************/26"
  vpc_name = "a-useast-1-wap-vpc-prod"
  eip_pool_id = "ipv4pool-ec2-0277a64bec3ddeb69"
}

output "name" {
  value = module.infra.name
}
