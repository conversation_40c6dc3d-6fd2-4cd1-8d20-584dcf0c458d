# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  / 
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /  
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/   
#
# codegen-version: v5.0.1
# template-version: 0.4.42
# -----------------------------------------------

terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_wsjc2-prod-v3231.tfstate"
    region = "us-east-2"
  }
}


module "infra_network" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/openstack/network?ref=v5.0.3"
  domain                    = "prod.infra.webex.com"
  force_delete_enabled      = "true"
  project-name              = "wsjc2-prod-v3231"
  external-network-name     = "Public-CCP-3094"
  internal-network-name     = "wsjc2-prod-v3231"
  cidr-nodes                = "************/22"
  use-floating-ip             = "false"
  nameservers               =  [
    "************",
    "************",

  ]
}
