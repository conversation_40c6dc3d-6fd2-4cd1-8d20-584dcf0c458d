# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
#    ___ _       ________       __     __    ______     __         __             __
#   /   | |     / / ___/ |     / /__  / /_  / ____/  __/ /____  __/ /_  ___  ____/ /
#  / /| | | /| / /\__ \| | /| / / _ \/ __ \/ __/ | |/_/ //_/ / / / __ \/ _ \/ __  /
# / ___ | |/ |/ /___/ /| |/ |/ /  __/ /_/ / /____>  </ ,< / /_/ / /_/ /  __/ /_/ /
#/_/  |_|__/|__//____/ |__/|__/\___/_.___/_____/_/|_/_/|_|\__,_/_.___/\___/\__,_/
#
#
# codegen-version: v7.17.0
# template-version: EXPERIMENTAL
# -----------------------------------------------


terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_apdxwxt-alpha-v2-az2.tfstate"
    region = "us-east-2"
  }
}


variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "us-west-2"
}

module "network" {
  source           = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/network?ref=v7.23.4.1"
  vpc_name         = "apdxwxt-alpha-v2"
  igw_name         = "apdxwxt-alpha-v2-igw"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"

  aws_infra_region = var.AWS_INFRA_REGION
  aws_infra_az     = "us-west-2b"
  name             = "apdxwxt-alpha-v2-az2"
  cidr_nodes       = "***********/18"
  eip_pool_id      = "ipv4pool-ec2-002c70c81368c701c"
  ext_cidr_nodes   = "************/22"
  vpc_cidr_block   = "<nil>"
  control_cidr_block    = "************/22"
  create_vpc      = false
  create_control_subnet = true
  public_control = true
  create_eip = true
  nat_gateway  = true
  # if eip_on_external_media_dx is true, the subnet' names will be "public-dx-private-{az}" and "cluster-dx-private-{az}"







}

output "name" {
  value = module.network.name
}
