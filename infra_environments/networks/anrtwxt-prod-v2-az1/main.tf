# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.17.0
# template-version: v7.18.0-EXPERIMENTAL-fc69d52
# module-version: v7.23.4-ignore-routes
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_anrtwxt-prod-v2-az1.tfstate"
    region = "us-east-2"
  }
}


module "network" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/network?ref=b3127b6c3688bddf7e6d57b13c37baecf0526adc"
  aws_infra_az = "ap-northeast-1a"
  aws_infra_region = "ap-northeast-1"
  cidr_nodes = "**********/18"
  control_cidr_block = "************/22"
  create_control_subnet = true
  create_eip = true
  create_provider_subnet = true
  create_vpc = true
  eip_pool_id = "ipv4pool-ec2-09e959e045c1b1dd4"
  ext_cidr_nodes = "************/22"
  infra_credentials_path = "secret/data/mccprod/infra/mpe-aws-prod/aws"
  name = "anrtwxt-prod-v2-az1"
  nat_gateway = true
  provider_cidr_nodes = "**********/26"
  public_control = true
  vpc_additional_cidrs = [
    "**********/16",
    "**********/24"
  ]
  vpc_cidr_block = "*************/28"
  vpc_name = "anrtwxt-prod-v2"

}
output "name" {
  value = module.network.name
}

