data "vault_generic_secret" "infra_credentials" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}

data "vault_generic_secret" "peering_credentials" {
  path = replace("secret/data/mccprod/infra/************/archipelago_service_account", "/data/", "/")
}

data "vault_generic_secret" "dns_credentials" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

provider "vault" {
  skip_child_token = true
}

provider "aws" {
  access_key  = data.vault_generic_secret.infra_credentials.data["AWS_ACCESS_KEY_ID"]
  secret_key  = data.vault_generic_secret.infra_credentials.data["AWS_SECRET_ACCESS_KEY"]
  region      = "af-south-1"
  max_retries = 100
  default_tags {
    tags = {
      webex_schema_version = "v3"
      webex_environment    = "p1"
    }
  }
}

provider "aws" {
  alias = "peering"
  access_key  = data.vault_generic_secret.peering_credentials.data["AWS_ACCESS_KEY_ID"]
  secret_key  = data.vault_generic_secret.peering_credentials.data["AWS_SECRET_ACCESS_KEY"]
  region      = "af-south-1"
  max_retries = 100
  default_tags {
    tags = {
      webex_schema_version = "v3"
      webex_environment    = "p1"
    }
  }
}

provider "aws" {
  alias = "dns"
  access_key  = data.vault_generic_secret.dns_credentials.data["AWS_ACCESS_KEY_ID"]
  secret_key  = data.vault_generic_secret.dns_credentials.data["AWS_SECRET_ACCESS_KEY"]
  region      = "af-south-1"
  max_retries = 100
  default_tags {
    tags = {
      webex_schema_version = "v3"
      webex_environment    = "p1"
    }
  }
}