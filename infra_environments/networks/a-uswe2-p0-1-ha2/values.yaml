# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v10.13.0
# wbx3-infra-version: <nil>
# resource: a-uswe2-p0-1-ha2
# resource-type: network
# -----------------------------------------------

#~~ Resource Values
absorb_public_root_domain: true
availability_zones:
    az1:
        availability_zone: us-west-2a
    az2:
        availability_zone: us-west-2b
    az3:
        availability_zone: us-west-2c
aws_infra_az: us-west-2
core_tgw_amazon_asn: 64512
dx_amazon_owner_account_id: ************
egress_vpc:
    vpc_index: 2
    webx_dc_nat_range: disabled
enable_dx_association: true
enable_peering_for_calling_meetings: false
env_name: a-uswe2-p0-1
environment: b51
infractl_version: v7.18.36
ingress_vpc:
    cidr_private_services: 10.249.166.128/25
    cidr_public_services: disabled
    primary_cld_cidr: 10.10.0.0/16
    secondary_isolated_cidr: 100.80.0.0/16
    vpc_index: 2
inspection_vpc:
    aws_gateway_asn: **********
    cidr_prv_direct_management: disabled
    enabled: false
    vpc_index: 2
media_vpcs:
    calling:
        aws_gateway_asn: **********
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled
        enabled: false
        vpc_index: 2
    teams:
        aws_gateway_asn: **********
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled
        enabled: true
        ipv6_allocations:
            - cidr_block: 2607:fcf0:20a4:700::/56
              pool_id: ipv6pool-ec2-09a2244d0e119683c
        role: app
        subnets:
            cloud:
                ipv4_cidr: *********/16
            isolated:
                ipv4_cidr: **********/16
                routes:
                    0.0.0.0/0:
                        nat_gateway_subnet: public-eip
                        nat_gateway_type: public
                    10.0.0.0/8:
                        nat_gateway_subnet: private
                        nat_gateway_type: private
            private:
                ipv4_cidr: **************/25
                nat_private: true
            public-eip:
                ipv6_cidr: 2607:fcf0:20a4:700::/62
                nat_public: true
        vpc_index: 2
module_path: modules/aws/network_archipelago
module_version: v10.13.0
name: a-uswe2-p0-1-ha2
primary_cidr_prefix_length: 16
private_root_domain: disabled
public_ipv4_pool: ipv4pool-ec2-021a4b5fd403fe977
public_root_domain: prod.infra.webex.com
region: us-west-2
root_domain: webex.com
tgw_dx_gateway_id: 074188f7-b6bf-4774-820c-fea74a56035b
vgw_dx_gateway_id: 64e00fc2-168d-4980-8ad7-afe454fb8156
webex_any_cast_ip:
    - ************

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: b50.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.5.3
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra_environments/networks/a-uswe2-p0-1-ha2
    name: a-uswe2-p0-1-ha2-network
    workflow: standard
dir: infra_environments/networks
external: false
include_defaults: true
include_environment: true
manifest_path: manifests/a-uswe2-p0-1/a-uswe2-p0-1-ha2/network/manifest.yaml
s3_bucket_path: terraform-state/network_a-uswe2-p0-1-ha2.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.18.36
    templateversion: 7.18.36
workflow: standard