# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.18.36
# template-version: 7.18.36
# module-version: v10.13.0
# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
# -----------------------------------------------



terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/network_a-uswe2-p0-1-ha2.tfstate"
    region = "us-east-2"
  }
}

provider "aws" {
  alias = "tfstate-lock"
  # Credentials provided by environment variables
  region = "us-east-2"
}

resource "aws_s3_object" "terraform_lock" {
  provider = aws.tfstate-lock
  bucket   = "tfstate-mccprod"
  key      = "terraform-state/network_a-uswe2-p0-1-ha2.tfstate.terraform.lock.hcl"
  source   = ".terraform.lock.hcl"
}

module "infra" {
  source = "git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git//modules/aws/network_archipelago?ref=v10.13.0"
  providers = {
    aws = aws
    aws.dns = aws.dns
    aws.peering = aws.peering
  }
  absorb_public_root_domain = true
  availability_zones = {
    az1 = {
      availability_zone = "us-west-2a"
    }
    az2 = {
      availability_zone = "us-west-2b"
    }
    az3 = {
      availability_zone = "us-west-2c"
    }
  }
  core_tgw_amazon_asn = 64512
  dx_amazon_owner_account_id = ************
  egress_vpc = {
    vpc_index = 2
    webx_dc_nat_range = "disabled"
  }
  enable_dx_association = true
  enable_peering_for_calling_meetings = false
  environment = "b51"
  ingress_vpc = {
    cidr_private_services = "**************/25"
    cidr_public_services = "disabled"
    primary_cld_cidr = "*********/16"
    secondary_isolated_cidr = "**********/16"
    vpc_index = 2
  }
  inspection_vpc = {
    aws_gateway_asn = **********
    cidr_prv_direct_management = "disabled"
    enabled = false
    vpc_index = 2
  }
  media_vpcs = {
    calling = {
      aws_gateway_asn = **********
      cidr_persistence = "disabled"
      cidr_private_media = "disabled"
      cidr_public_media = "disabled"
      enabled = false
      vpc_index = 2
    }
    teams = {
      aws_gateway_asn = **********
      cidr_persistence = "disabled"
      cidr_private_media = "disabled"
      cidr_public_media = "disabled"
      enabled = true
      ipv6_allocations = [
        {
          cidr_block = "2607:fcf0:20a4:700::/56"
          pool_id = "ipv6pool-ec2-09a2244d0e119683c"
        }
      ]
      role = "app"
      subnets = {
        cloud = {
          ipv4_cidr = "*********/16"
        }
        isolated = {
          ipv4_cidr = "**********/16"
          routes = {
            "0.0.0.0/0" = {
              nat_gateway_subnet = "public-eip"
              nat_gateway_type = "public"
            }
            "10.0.0.0/8" = {
              nat_gateway_subnet = "private"
              nat_gateway_type = "private"
            }
          }
        }
        private = {
          ipv4_cidr = "**************/25"
          nat_private = true
        }
        public-eip = {
          ipv6_cidr = "2607:fcf0:20a4:700::/62"
          nat_public = true
        }
      }
      vpc_index = 2
    }
  }
  primary_cidr_prefix_length = 16
  private_root_domain = "disabled"
  public_ipv4_pool = "ipv4pool-ec2-021a4b5fd403fe977"
  public_root_domain = "prod.infra.webex.com"
  region = "us-west-2"
  root_domain = "webex.com"
  tgw_dx_gateway_id = "074188f7-b6bf-4774-820c-fea74a56035b"
  vgw_dx_gateway_id = "64e00fc2-168d-4980-8ad7-afe454fb8156"
  
}
output "application_vpcs" {
  value = module.infra.application_vpcs
}
output "backbone_transit_route_table" {
  value = module.infra.backbone_transit_route_table
}
output "core_transit_gateway_id" {
  value = module.infra.core_transit_gateway_id
}
output "egress_transit_route_table" {
  value = module.infra.egress_transit_route_table
}
output "egress_vpc" {
  value = module.infra.egress_vpc
}
output "egress_vpc_gwlbe_isolated_subnets" {
  value = module.infra.egress_vpc_gwlbe_isolated_subnets
}
output "egress_vpc_nat_dc_provider_subnets" {
  value = module.infra.egress_vpc_nat_dc_provider_subnets
}
output "egress_vpc_nat_internet_isolated_subnets" {
  value = module.infra.egress_vpc_nat_internet_isolated_subnets
}
output "egress_vpc_tgw_isolated_subnets" {
  value = module.infra.egress_vpc_tgw_isolated_subnets
}
output "inbound_resolver_ips" {
  value = module.infra.inbound_resolver_ips
}
output "ingress_transit_route_table" {
  value = module.infra.ingress_transit_route_table
}
output "ingress_vpc" {
  value = module.infra.ingress_vpc
}
output "ingress_vpc_iso_gwlbe_firepower_ingress_subnets" {
  value = module.infra.ingress_vpc_iso_gwlbe_firepower_ingress_subnets
}
output "ingress_vpc_service_cloud_subnets" {
  value = module.infra.ingress_vpc_service_cloud_subnets
}
output "ingress_vpc_service_igw_cloud_subnets" {
  value = module.infra.ingress_vpc_service_igw_cloud_subnets
}
output "ingress_vpc_services_provider_expansion_subnets" {
  value = module.infra.ingress_vpc_services_provider_expansion_subnets
}
output "ingress_vpc_services_provider_subnets" {
  value = module.infra.ingress_vpc_services_provider_subnets
}
output "ingress_vpc_services_public_expansion_subnets" {
  value = module.infra.ingress_vpc_services_public_expansion_subnets
}
output "ingress_vpc_services_public_subnets" {
  value = module.infra.ingress_vpc_services_public_subnets
}
output "ingress_vpc_tgw_isolated_subnets" {
  value = module.infra.ingress_vpc_tgw_isolated_subnets
}
output "inspection_transit_route_table" {
  value = module.infra.inspection_transit_route_table
}
output "inspection_vpc" {
  value = module.infra.inspection_vpc
}
output "inspection_vpc_firepower_cluster_egress_isolated_subnets" {
  value = module.infra.inspection_vpc_firepower_cluster_egress_isolated_subnets
}
output "inspection_vpc_firepower_cluster_ingress_isolated_subnets" {
  value = module.infra.inspection_vpc_firepower_cluster_ingress_isolated_subnets
}
output "inspection_vpc_firepower_egress_isolated_subnets" {
  value = module.infra.inspection_vpc_firepower_egress_isolated_subnets
}
output "inspection_vpc_firepower_ingress_isolated_subnets" {
  value = module.infra.inspection_vpc_firepower_ingress_isolated_subnets
}
output "inspection_vpc_fw_mgmt_provider_subnets" {
  value = module.infra.inspection_vpc_fw_mgmt_provider_subnets
}
output "inspection_vpc_gwlbe_isolated_subnets" {
  value = module.infra.inspection_vpc_gwlbe_isolated_subnets
}
output "inspection_vpc_nat_isolated_subnets" {
  value = module.infra.inspection_vpc_nat_isolated_subnets
}
output "inspection_vpc_tgw_isolated_subnets" {
  value = module.infra.inspection_vpc_tgw_isolated_subnets
}
output "media_vpcs" {
  value = module.infra.media_vpcs
}
output "platform_vpcs" {
  value = module.infra.platform_vpcs
}
output "private_root_domain" {
  value = module.infra.private_root_domain
}
output "public_root_domain" {
  value = module.infra.public_root_domain
}
output "region_internal_zone" {
  value = module.infra.region_internal_zone
}
output "region_private_zone" {
  value = module.infra.region_private_zone
}
output "region_public_zone" {
  value = module.infra.region_public_zone
}
output "regional_public_name_servers" {
  value = module.infra.regional_public_name_servers
}
output "root_domain" {
  value = module.infra.root_domain
}

