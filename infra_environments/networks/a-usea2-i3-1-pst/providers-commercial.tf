data "vault_generic_secret" "infra_credentials" {
  path = replace("secret/data/mccprod/infra/************/atlantis-c2p-pre-prod", "/data/", "/")
}

data "vault_generic_secret" "peering_credentials" {
  path = replace("secret/data/mccprod/infra/************/archipelago_service_account", "/data/", "/")
}

data "vault_generic_secret" "dns_credentials" {
  path = replace("secret/data/mccprod/infra/route53/credentials", "/data/", "/")
}

provider "vault" {
  skip_child_token = true
}

provider "aws" {
  access_key  = data.vault_generic_secret.infra_credentials.data["AWS_ACCESS_KEY_ID"]
  secret_key  = data.vault_generic_secret.infra_credentials.data["AWS_SECRET_ACCESS_KEY"]
  region      = "us-east-2"
  max_retries = 100
  default_tags {
    tags = {
      webex_schema_version = "v3"
      webex_environment    = "c1p"
      CcpOrganizationId    = "9edd5e35-1f5a-4fbc-978d-8f250f3501b0"
      CcpAccountId         = "32bc6b5d-84a0-4df6-9838-fb9a16d4cffb"
    }
  }
}

provider "aws" {
  alias = "peering"
  access_key  = data.vault_generic_secret.peering_credentials.data["AWS_ACCESS_KEY_ID"]
  secret_key  = data.vault_generic_secret.peering_credentials.data["AWS_SECRET_ACCESS_KEY"]
  region      = "us-east-2"
  max_retries = 100
  default_tags {
    tags = {
      webex_schema_version = "v3"
      webex_environment    = "c1p"
    }
  }
}

provider "aws" {
  alias = "dns"
  access_key  = data.vault_generic_secret.dns_credentials.data["AWS_ACCESS_KEY_ID"]
  secret_key  = data.vault_generic_secret.dns_credentials.data["AWS_SECRET_ACCESS_KEY"]
  region      = "us-east-2"
  max_retries = 100
  default_tags {
    tags = {
      webex_schema_version = "v3"
      webex_environment    = "c1p"
    }
  }
}