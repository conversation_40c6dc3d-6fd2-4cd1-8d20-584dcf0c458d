# -----------------------------------------------
# Code generated by infractl. DO NOT EDIT.
# _       __     __    ______     __ __      __             __
#| |     / /__  / /_  / ____/  __/ //_/_  __/ /_  ___  ____/ /
#| | /| / / _ \/ __ \/ __/ | |/_/ ,< / / / / __ \/ _ \/ __  /
#| |/ |/ /  __/ /_/ / /____>  </ /| / /_/ / /_/ /  __/ /_/ /
#|__/|__/\___/_.___/_____/_/|_/_/ |_\__,_/_.___/\___/\__,_/
#
# codegen-version: v7.19.0
# template-version: 7.19.1
# module-version: v10.16.4
# wbx3-infra-version: <nil>
# resource: a-uswe2-i3-2-ha1
# resource-type: network
# -----------------------------------------------

#~~ Resource Values
absorb_public_root_domain: true
availability_zones:
    az1:
        availability_zone: us-west-2a
    az2:
        availability_zone: us-west-2b
    az3:
        availability_zone: us-west-2c
aws_infra_az: us-west-2
core_tgw_amazon_asn: 64512
dx_amazon_owner_account_id: ************
egress_vpc:
    vpc_index: 1
    webx_dc_nat_range: disabled
enable_dx_association: false
enable_peering_for_calling_meetings: false
env_name: a-uswe2-i3-2
environment: c40
infractl_version: v7.19.0
ingress_vpc:
    cidr_private_services: disabled
    cidr_public_services: disabled
    primary_cld_cidr: 10.9.0.0/16
    secondary_isolated_cidr: 100.79.0.0/16
    vpc_index: 1
inspection_vpc:
    aws_gateway_asn: **********
    cidr_prv_direct_management: disabled
    enabled: false
    vpc_index: 1
media_vpcs:
    calling:
        aws_gateway_asn: **********
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled
        enabled: false
        vpc_index: 1
    teams:
        aws_gateway_asn: **********
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled
        enabled: true
        ipv6_allocations:
            - cidr_block: 2607:fcf0:4001:100::/56
              pool_id: ipv6pool-ec2-046d34b748308976a
        role: app
        subnets:
            cloud:
                ipv4_cidr: ********/16
            isolated:
                ipv4_cidr: **********/16
            public-eip:
                ipv6_cidr: 2607:fcf0:4001:100::/62
                nat_public: true
        vpc_index: 1
module_path: modules/aws/network_archipelago
module_version: v10.16.4
name: a-uswe2-i3-2-ha1
primary_cidr_prefix_length: 16
private_root_domain: disabled
public_ipv4_pool: ipv4pool-ec2-08b46eca4b134628f
public_root_domain: prod.infra.webex.com
region: us-west-2
root_domain: webex.com
tgw_dx_gateway_id: 05111561-bb31-41d7-a109-08ebaac9df0e
vgw_dx_gateway_id: 503b6907-eadc-4903-a792-3af072a8ba95
webex_any_cast_ip:
    - ************

#~~ No Blueprint Values


#~~ Environment Values
backend: s3
blueprint_repo: git::https://sqbu-github.cisco.com/WebexPlatform/wbx3-infra.git
cloud_service_provider: aws
command_and_control: mccprod
dns_credentials_path: secret/data/mccprod/infra/route53/credentials
domain: c30.prod.infra.webex.com
embed_provider_template: true
enable_credential_lookup: true
infra_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
infra_repo: git::https://sqbu-github.cisco.com/WebexPlatform/federated-infra.git
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
s3_bucket_region: us-east-2
s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform_version: v1.8.5
use_provider_template: true

#~~ No Default Values


#~~ Internally Generated Values
atlantis_options:
    dir: infra_environments/networks/a-uswe2-i3-2-ha1
    name: a-uswe2-i3-2-ha1-network
    workflow: standard
dir: infra_environments/networks
external: false
include_defaults: true
include_environment: true
manifest_path: manifests/a-uswe2-i3-2/a-uswe2-i3-2-ha1/network/manifest.yaml
s3_bucket_path: terraform-state/network_a-uswe2-i3-2-ha1.tfstate
terraform_templates:
    - path: providers-commercial.tf.tpl
versioningData:
    infractlversion: 7.19.1
    templateversion: 7.19.1
workflow: standard