# Needed by Atlantis:
# s3-credentials-path: secret/data/mccprod/infra/mpe-aws-prod/aws
terraform {
  backend "s3" {
    bucket = "tfstate-mccprod"
    key = "terraform-state/s3-buckets-us-east-1.tfstate"
    region = "us-east-2"
  }
}

data "vault_generic_secret" "aws_infra_credential" {
  path = replace("secret/data/mccprod/infra/mpe-aws-prod/aws", "/data/", "/")
}


variable "AWS_INFRA_REGION" {
  description = "AWS Region"
  default = "us-east-1"
}

provider "aws" {
  access_key = data.vault_generic_secret.aws_infra_credential.data["AWS_ACCESS_KEY_ID"]
  secret_key = data.vault_generic_secret.aws_infra_credential.data["AWS_SECRET_ACCESS_KEY"]
  region     = var.AWS_INFRA_REGION
}

data "aws_canonical_user_id" "current_user" {}

resource "aws_s3_bucket" "mccint_argo_workflows" {
  bucket = "mccint-argo-workflows"

  versioning {
    enabled = true
    mfa_delete = false
  }

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = ""
        sse_algorithm = "AES256"
      }
    }
  }

  lifecycle_rule {
    enabled = "true"
    expiration {
      days = 180
    }
    noncurrent_version_expiration {
      days = 30
    }
  }

  tags = {
    argoproj = "mccprod"
    supportgroup = "wbx3_platform"
    DataClassification = "Cisco Highly Confidential"
    Environment = "Prod"
    ApplicationName = "wbx3_platform"
    ResourceOwner = "BU_Collaboration"
    CiscoMailAlias = "<EMAIL>"
    DataTaxonomy = "Administrative Data+Customer Data+Entrusted Data+Telemetry Data"
  }
}

resource "aws_s3_bucket_policy" "mccint_argo_workflows" {
  bucket = aws_s3_bucket.mccint_argo_workflows.id
  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "DenyUnSecureCommunications",
      "Effect": "Deny",
      "Principal": "*",
      "Action": "s3:*",
      "Resource": "arn:aws:s3:::mccint-argo-workflows",
      "Condition": {
        "Bool": {
          "aws:SecureTransport": "false"
        }
      }
    }
  ]
}
POLICY
}

resource "aws_s3_bucket_public_access_block" "mccint_argo_workflows" {
  bucket = aws_s3_bucket.mccint_argo_workflows.id
  block_public_acls = true
  block_public_policy = true
  ignore_public_acls = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "s3accesslogs_527856644868_us_east_1" {
  bucket = "s3accesslogs-527856644868-us-east-1"

  versioning {
    enabled = true
    mfa_delete = false
  }

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = ""
        sse_algorithm = "AES256"
      }
    }
  }

  lifecycle_rule {
    abort_incomplete_multipart_upload_days = "0"
    enabled = true

    expiration {
      days = "30"
      expired_object_delete_marker = false
    }
    id = "ExpireLogs"
    prefix = ""
  }

  grant {
    id = ""
    permissions = ["READ_ACP", "WRITE"]
    type = "Group"
    uri = "http://acs.amazonaws.com/groups/s3/LogDelivery"
  }

  grant {
    id = data.aws_canonical_user_id.current_user.id
    permissions = ["FULL_CONTROL"]
    type = "CanonicalUser"
    uri = ""
  }

  tags = {
    supportgroup = "wbx3_platform"
    ApplicationName = "Logging Bucket"
    CiscoMailAlias = "<EMAIL>"
    DataClassification = "Cisco Highly Confidential"
    Environment = "Prod"
    ResourceOwner = "mpe-aws-prod"
    DataTaxonomy = "Administrative Data+Customer Data+Entrusted Data+Telemetry Data"
  }
}

resource "aws_s3_bucket_policy" "s3accesslogs_527856644868_us_east_1" {
  bucket = aws_s3_bucket.s3accesslogs_527856644868_us_east_1.id
  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "DenyUnSecureCommunications",
      "Effect": "Deny",
      "Principal": "*",
      "Action": "s3:*",
      "Resource": "arn:aws:s3:::s3accesslogs-527856644868-us-east-1",
      "Condition": {
        "Bool": {
          "aws:SecureTransport": "false"
        }
      }
    }
  ]
}
POLICY
}

resource "aws_s3_bucket" "wbx3_static" {
  bucket = "wbx3-static"

  versioning {
    enabled = true
    mfa_delete = false
  }

  logging {
    target_bucket = "s3accesslogs-527856644868-us-east-1"
    target_prefix = ""
  }

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = ""
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    supportgroup = "wbx3_platform"
    DataClassification = "Cisco Highly Confidential"
    Environment = "Prod"
    ApplicationName = "wbx3_platform"
    ResourceOwner = "BU_Collaboration"
    CiscoMailAlias = "<EMAIL>"
    DataTaxonomy = "Administrative Data+Customer Data+Entrusted Data+Telemetry Data"
  }
}

resource "aws_s3_bucket_policy" "wbx3_static" {
  bucket = aws_s3_bucket.wbx3_static.id
  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "DenyUnSecureCommunications",
      "Effect": "Deny",
      "Principal": "*",
      "Action": "s3:*",
      "Resource": "arn:aws:s3:::wbx3-static",
      "Condition": {
        "Bool": {
          "aws:SecureTransport": "false"
        }
      }
    }
  ]
}
POLICY
}
