version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # DualStack NLB for wxt-calling mesh
  - name: wxtc-prod-adxb-calling1
    env_name: me-01a-ha1
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: me-central-1
      vpc_name: p6_mtg1
      ip_address_type: dualstack
      deployment_group: wxtc-prod-adxb-calling1
      mesh_name: wxt-calling
      ingressgateway_name: wxt-calling-ingressgateway
      subnet_type: public-eip
      domain: p6.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-8443:
          protocol: TCP
          port: 8443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-ci mesh
  - name: wxtci-prod-adxb-ci1
    env_name: me-01a-ha1
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: me-central-1
      vpc_name: p6_mtg1
      ip_address_type: dualstack
      deployment_group: wxtci-prod-adxb-ci1
      mesh_name: wxt-ci
      ingressgateway_name: wxt-ci-ingressgateway
      subnet_type: public-eip
      domain: p6.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-general mesh
  - name: wxtgen-prod-adxb-general1
    env_name: me-01a-ha1
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: me-central-1
      vpc_name: p6_mtg1
      ip_address_type: dualstack
      deployment_group: wxtgen-prod-adxb-general1
      mesh_name: wxt-general
      ingressgateway_name: wxt-general-ingressgateway
      subnet_type: public-eip
      domain: p6.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-meet mesh
  - name: wxtm-prod-adxb-meet1
    env_name: me-01a-ha1
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: me-central-1
      vpc_name: p6_mtg1
      ip_address_type: dualstack
      deployment_group: wxtm-prod-adxb-meet1
      mesh_name: wxt-meet
      ingressgateway_name: wxt-meet-ingressgateway
      subnet_type: public-eip
      domain: p6.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-mercury mesh
  - name: wxtmer-prod-adxb-mercury1
    env_name: me-01a-ha1
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: me-central-1
      vpc_name: p6_mtg1
      ip_address_type: dualstack
      deployment_group: wxtmer-prod-adxb-mercury1
      mesh_name: wxt-mercury
      ingressgateway_name: wxt-mercury-ingressgateway
      subnet_type: public-eip
      domain: p6.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-message mesh
  - name: wxtmsg-prod-adxb-message1
    env_name: me-01a-ha1
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: me-central-1
      vpc_name: p6_mtg1
      ip_address_type: dualstack
      deployment_group: wxtmsg-prod-adxb-message1
      mesh_name: wxt-message
      ingressgateway_name: wxt-message-ingressgateway
      subnet_type: public-eip
      domain: p6.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-registration mesh
  - name: wxtreg-prod-adxb-regnlb1
    env_name: me-01a-ha1
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: me-central-1
      vpc_name: p6_mtg1
      ip_address_type: dualstack
      deployment_group: wxtreg-prod-adxb-registration1
      mesh_name: wxt-registration
      ingressgateway_name: wxt-registration-ingressgateway
      subnet_type: public-eip
      domain: p6.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-wxid mesh
  - name: wxtci-prod-adxb-idaas1
    env_name: me-01a-ha1
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: me-central-1
      vpc_name: p6_mtg1
      ip_address_type: dualstack
      deployment_group: wxtci-prod-adxb-idaas1
      mesh_name: wxt-wxid
      ingressgateway_name: wxt-wxid-ingressgateway
      subnet_type: public-eip
      domain: p6.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ccccf50993a6e066
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
