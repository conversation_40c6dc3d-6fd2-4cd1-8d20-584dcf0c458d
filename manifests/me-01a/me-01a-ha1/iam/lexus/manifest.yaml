---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infra:
  - name: s3bucketLexusADXB
    module_path: modules/aws/aws_iam_policy
    module_version: v10.15.0
    env_name: me-01a-ha1
    terraform_version: v1.5.7
    args:
      policy_description: >-
        Allow Lexus service owners to access the S3 bucket s3bucket-lexus-adxb for ADXB
      policy_json: |
        {
          "Version": "2012-10-17",
          "Statement": [
            {
              "Action": "s3:ListBucket",
              "Resource": "arn:aws:s3:::s3bucket-lexus-adxb",
              "Effect": "Allow"
            },
            {
              "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:GetObjectTagging",
                "s3:PutObjectTagging",
                "s3:AbortMultipartUpload",
                "s3:DeleteObject",
                "s3:PutObjectAcl"
              ],
              "Resource": "arn:aws:s3:::s3bucket-lexus-adxb/*",
              "Effect": "Allow"
            }
          ]
        }
  - name: s3bucketLexusADXBGroup
    env_name: me-01a-ha1
    module_path: modules/aws/aws_iam_group
    module_version: v10.15.1
    terraform_version: v1.5.7
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    args:
      group_name: s3bucketLexusADXB
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      policy_arns:
        - arn:aws:iam::527856644868:policy/s3bucketLexusADXB
  - name: s3bucketLexusADXBUser
    env_name: me-01a-ha1
    module_path: modules/aws/aws_iam_user
    module_version: v10.15.1
    terraform_version: v1.5.7
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    args:
      user_name: s3BucketLexusADXBUser
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      groups:
        - s3bucketLexusADXB
      create_access_key: true
      iam_credentials_path: secret/mccprod/infra/iam-users
defaults:
  infractl_version: v7.18.19
  backend: s3
  terraform_version: v1.5.7
  use_provider_template: true
  embed_provider_template: true

  terraform_templates:
    - path: providers-commercial.tf.tpl

  include_defaults: true
