---
version: 1
command_and_control: mccprod
clusters:
- name: wsydmw-az-1
  status: online
  env_name: wsyd01-ocp4-prod
  worker_count: 0
  ingress_count: 6
  ingress_int_count: 2
  bastion_count: 1
  optimized_storage_count: 0
  worker_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  master_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  base_image: wbx3-focal-1.21.5-fy23q1
  base_k8s_image: wbx3-focal-1.21.5-fy23q1
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: kube_network_node_prefix=26 kubelet_max_pods=56
  cidr_node_prefix: 26
  cidr_pods_prefix: 18
  cidr_svcs_prefix: 22
  health_checks: true
  terraform_version: v1.0.5
  module_version: v7.14.5
  infractl_version: v7.14.5
  provisioning_module_version: v5.4.18-blockstorage
  metadata:
    cluster_type: webapps-prod
    platform_release_channel: stable-3
    annotations:
      helm3Only: true
    deployment_groups:
    - mw-prod-az
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/base_apps_old.yaml
  - platform/external-dns.yaml
  - platform/falco.yaml
  - webapps/init.yaml
  server_group_policies: [soft-anti-affinity]
  internal_network: wsyd01-ocp4-mw
  external_network: wbx3-meetings
  management_network: provider-3258
  internal_provider_network: provider-3258
  internal_provider_subnets: [***********/23]
  worker_eth1_network: provider-3258
  volume_storage: true
  gateway_name: wsyd01-ocp4-mw
  node_pools:
  - name: mw-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 81
    max_size: 81
    metadata:
      node_labels: pool-name=mw-p0,type=worker
  - name: mw-p1-
    type: provider-worker
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    min_size: 6
    max_size: 6
    metadata:
      node_labels: pool-name=mw-p1,type=worker
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.64mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
defaults:
  import_defaults: ["../../../manifest.yaml"]
