# TODO: Change IATA code to dxb from dbx in env name and vpc name later
---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
- name: &wxc-dbx-env adbxwxc-prod
  terraform_version: v1.2.4
  module_version: v7.23.4
  infractl_version: v7.17.2
  provisioning_module_version: v5.6.0
  base_image: wbx3-focal-1.23.5-containerd-455_8d0cd6e
  base_k8s_image: wbx3-focal-1.23.5-containerd-455_8d0cd6e
  cloud_service_provider: aws
  domain: prod.infra.webex.com
  external_network: adbxwxc-prod
  internal_network: multi-az
  infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  aws_infra_az: me-central-1a
  aws_infra_region: me-central-1
  backend: s3
  master_flavor: m5ad.4xlarge
  bastion_flavor: m5a.large
  image_flavor: m5a.2xlarge
  ingress_flavor: c5n.2xlarge
  ingress_int_flavor: c5n.2xlarge
  create_vpc: true
  create_eip: true
  nat_gateway: true
  health_checks: false
  thousand_eyes_node_count: 0
  location: DXB
infra:
- name: a-dbxwxc-resolvers
  module_path: modules/aws/route53_endpoint
  module_version: v7.23.4/resolver-provider-upgrade
  module_commit: 31117e97ca4f4a450f2396442249e1d79daf40b7
  env_name: *wxc-dbx-env
  args:
    infra_credentials_path: secret/mccprod/infra/mpe-aws-prod/aws
    aws_infra_region: me-central-1
    direction: OUTBOUND
    vpc_name: adbxwxc-prod
    subnet_tier: provider
    resolver_rules:
    - domain_name: broadcloud.org
      name: broadcloud-resolver
      rule_type: FORWARD
      targets:
      - target_ip: *************
        target_port: 53
      - target_ip: *************
        target_port: 53
      - target_ip: ************
        target_port: 53
      - target_ip: ************
        target_port: 53
- name: wxc-dxb-vgw
  module_path: modules/aws/dx_vgw
  module_version: v9.4.13
  terraform_version: v1.5.0
  infract_version: v7.18.2
  env_name: *wxc-dbx-env
  aws_infra_region: me-central-1
  args:
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    vpc_name: adbxwxc-prod
    dx_gateway_id: 07712cad-8877-4570-9f79-1d2f835d6749   # FRA01-DGW01
    dx_gateway_account: ************
    routes:
    - subnet_tier: provider
      subnet: 10.0.0.0/8
- name: wxc-dxb-tgw
  terraform_version: v1.5.0
  infractl_version: v7.17.10
  module_version: v10.2.18
  module_path: modules/aws/transit_gateway
  env_name: *wxc-dbx-env
  args:
    aws_infra_region: me-central-1
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    vpc_name: adbxwxc-prod
    subnet_tier: provider-peering
    tgw_owner_id: '************'
    extra_tgw_filters:
    - name: transit-gateway-id
      values:
      - tgw-003cce995246d0b45
    routes:
    - subnet_tier: provider-peering
      subnet: **************/25
    - subnet_tier: provider-peering
      subnet: ************/25
    - subnet_tier: provider-peering
      subnet: **************/25
networks:
- name: a-mecentral-1a-wxc
  eip_pool_id: ipv4pool-ec2-0ccccf50993a6e066
  env_name: *wxc-dbx-env
  aws_infra_region: me-central-1
  aws_infra_az: me-central-1a
  vpc_cidr_block: ************/24
  cidr_nodes: **********/18
  ext_cidr_nodes: ************/20
  provider_cidr_nodes: ************/27
  vpc_additional_cidrs:
  - **********/16
  nameservers:
  - AmazonProvidedDNS
  create_vpc: true
  create_provider_subnet: true
- name: a-mecentral-1b-wxc
  eip_pool_id: ipv4pool-ec2-0ccccf50993a6e066
  env_name: *wxc-dbx-env
  aws_infra_region: me-central-1
  aws_infra_az: me-central-1b
  cidr_nodes: ***********/18
  ext_cidr_nodes: ************/20
  provider_cidr_nodes: *************/27
  create_vpc: false
  create_provider_subnet: true
- name: a-mecentral-1c-wxc
  eip_pool_id: ipv4pool-ec2-0ccccf50993a6e066
  env_name: *wxc-dbx-env
  aws_infra_region: me-central-1
  aws_infra_az: me-central-1c
  cidr_nodes: ************/18
  ext_cidr_nodes: ************/20
  provider_cidr_nodes: *************/27
  create_vpc: false
  create_provider_subnet: true
defaults:
  import_defaults: ["../../manifest.yaml"]
