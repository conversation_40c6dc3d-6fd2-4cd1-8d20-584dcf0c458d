version: 1
command_and_control: mccprod

infra:
- name: ap-northeast-1-resolvers
  module_path: modules/aws/route53_endpoint
  module_version: v10.2.15
  terraform_version: v1.3.0
  env_name: anrtwxt-prod
  args:
    infra_credentials_path: secret/mccprod/infra/mpe-aws-prod/aws
    aws_infra_region: ap-northeast-1
    direction: OUTBOUND
    vpc_name: anrtwxt-prod
    # subnet_tier must match a Tier tag in AWS
    subnet_tier: private
    resolver_rules:
      - domain_name: webex.com
        name: webex-com-resolver
        rule_type: FORWARD
        vpc_ids: ["vpc-0b4f98626c107f52c", "vpc-09298b06b2e36213d"]
        targets:
          - target_ip: *******
            target_port: 53
          - target_ip: **************
            target_port: 53
      - domain_name: wbx2.com
        name: wbx2-com-resolver
        rule_type: FORWARD
        vpc_ids: ["vpc-0b4f98626c107f52c", "vpc-09298b06b2e36213d"]
        targets:
          - target_ip: *******
            target_port: 53
          - target_ip: **************
            target_port: 53
      - domain_name: ciscospark.com
        name: ciscospark-com-resolver
        rule_type: FORWARD
        vpc_ids: ["vpc-0b4f98626c107f52c", "vpc-09298b06b2e36213d"]
        targets:
          - target_ip: *******
            target_port: 53
          - target_ip: **************
            target_port: 53
defaults:
  import_defaults: ["../../manifest.yaml"]
