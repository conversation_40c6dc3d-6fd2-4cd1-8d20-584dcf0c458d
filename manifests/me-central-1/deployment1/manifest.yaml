---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

environments:
- name: mec1-ha1
  domain: prod.infra.webex.com
  #mpe-aws-int (************)
  infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  #wbx3-aws-tgw (************)
  peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
  s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
  cloud_service_provider: aws

infra:
  # VPC peering add on module
  - name: mec1-pcx-cal1-to-cal2
    env_name: mec1-ha1
    module_path: modules/aws/wxt/peering_connection
    module_version: v10.5.0
    infractl_version: v7.18.17
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_aws_infra_region: me-central-1
      source_vpc_name: p1_cal1
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_aws_infra_region: me-central-1
      destination_vpc_name: p2_cal2
      infra_service_domain: "https://infra.int.mccprod.prod.infra.webex.com"
      name: pcx-p1-cal1-to-p2-cal2
      destination_subnet_filter:
        - "*prv*"
      destination_route_table_filter:
        - "*prv*"
      source_subnet_filter:
        - "*prv*"
      source_route_table_filter:
        - "*prv*"

networks:
  - name: mec1-production-ha1
    aws_infra_az: <replace-me> #for older infractl versions
    env_name: mec1-ha1
    include_environment: true
    module_version: v10.11.13
    module_path: "modules/aws/network_archipelago"
    infractl_version: v7.18.36

    region: me-central-1
    environment: 's1'
    primary_cidr_prefix_length: 17
    ha_deployment_side: first
    availability_zones:
      az1:
        availability_zone: me-central-1a
      az2:
        availability_zone: me-central-1b
      az3:
        availability_zone: me-central-1c

    egress_vpc:
      vpc_index: 1
      webx_dc_nat_range: *************/26 # CIDR for nat_to_dc_subnets

    ingress_vpc:
      vpc_index: 1
      cidr_public_services: disabled # CIDR used in front_end_services_public
      cidr_private_services: ***********/25 # CIDR used in front_end_services_private

    inspection_vpc:
      vpc_index: 1
      enabled: false
      cidr_prv_direct_management: disabled
      aws_gateway_asn: **********

    ##################################
    ### Application VPC Configurations
    ##################################

    media_vpcs:
      calling:
        enabled: true
        vpc_index: 1
        aws_gateway_asn: **********
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled
        cidr_access_private: 10.249.72.0/24
        cidr_trunk_private: 10.249.73.0/24
        cidr_access_public: 144.196.118.0/25
        cidr_trunk_public: 144.196.119.0/25
        expansion_subnets:
          expansion-1:
            cidr_persistence: disabled
            cidr_private_media: disabled
            cidr_public_media: disabled
          expansion-2:
            cidr_persistence: disabled
            cidr_private_media: disabled
            cidr_public_media: disabled
        subnets:
          isolated:
            routes:
              10.0.0.0/8:
                nat_gateway_subnet: access-private
                nat_gateway_type: private
          access-private:
            nat_private: true
          access-public:
            nat_private: true

    outbound_resolver_rules:
    - domain_name: broadcloud.org
      resolver_rule_name: broadcloud-resolver
      targets:
      # DNS IPs for FR2
      - target_ip: *************
      - target_ip: *************
      # DNS IPs for AMS
      - target_ip: ************
      - target_ip: ************
    - domain_name: cisco.com
      resolver_rule_name: cisco-com_outbound
      targets:
      - target_ip: ************
      - target_ip: ************
    - domain_name: webex.com
      resolver_rule_name: webex-com_outbound
      targets:
      - target_ip: ************
      - target_ip: ************

    enable_dx_association: true
    core_tgw_amazon_asn: **********

    ### me-central-1
    vgw_dx_gateway_id: ad0f5b24-d89e-4443-b2a3-4227d9369cbe
    tgw_dx_gateway_id: a1f90bad-7f18-44a8-8e1c-f94705f9ac9a
    dx_amazon_owner_account_id: ************
    public_ipv4_pool: ipv4pool-ec2-0ccccf50993a6e066

    # If enabled, this will create a peering connection between the Meetings and Calling VPCs
    enable_peering_for_calling_meetings: false

    # This should always be webex.com in commercial
    root_domain: webex.com
    # This enables the use of prv.webex.com in this network environment, requires a 10.x peering
    # Also requires SNOW tickets to be created so infoblox can be updated
    private_root_domain: disabled
    # This is the root domain that will be used for this network
    # Use int.infra.webex.com for integration environments
    public_root_domain: prod.infra.webex.com
    # This should be set to true if the public_root_domain hosted zone already exists in Route53
    absorb_public_root_domain: true
    webex_any_cast_ip: ["************"]


defaults:
    module_version: v10.5.0
    infractl_version: v7.18.17
    backend: s3
    s3_bucket_region: us-east-2
     # Prod is us-east-2, integration is us-east-1
    terraform_version: v1.5.0
    use_provider_template: true
    embed_provider_template: true

    terraform_templates:
      - path: providers-commercial.tf.tpl

    include_defaults: true
    import_defaults: ["../../../manifest.yaml"]
