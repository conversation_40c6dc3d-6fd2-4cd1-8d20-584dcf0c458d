version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

networks:
  - name: c3s_s31
    vpc_name: c3s_s31
    env_name: a-uswe2-i3-1
    aws_infra_region: us-east-2
    aws_infra_az: us-east-2a
    vpc_cidr_block: 192.168.0.0/24
    cidr_nodes: 192.168.0.0/27
    ext_cidr_nodes: 192.168.0.32/27
    create_vpc: true
    create_vpce: true
    nameservers:
      - AmazonProvidedDNS
  - name: c3s_s31_b
    vpc_name: c3s_s31
    env_name: a-uswe2-i3-1
    aws_infra_region: us-east-2
    aws_infra_az: us-east-2b
    cidr_nodes: 192.168.0.64/27
    ext_cidr_nodes: 192.168.0.96/27
    create_vpc: false
  - name: c3s_s31_c
    vpc_name: c3s_s31
    env_name: a-uswe2-i3-1
    aws_infra_region: us-east-2
    aws_infra_az: us-east-2c
    cidr_nodes: 192.168.0.128/27
    ext_cidr_nodes: 192.168.0.160/27
    create_vpc: false
