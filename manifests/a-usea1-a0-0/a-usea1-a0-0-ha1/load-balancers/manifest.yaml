version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
# DualStack NLB for webapps mesh
  - name: nlb-mw-ats-pub
    env_name: a-usea1-a0-0
    module_path: modules/aws/nlb
    module_version: v10.8.10
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a20_app1
      ip_address_type: dualstack
      deployment_group: ats1
      mesh_name: webapps
      ingressgateway_name: mw-pub-ingressgateway
      subnet_type: public-eip
      domain: a20.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-80:
          protocol: TCP
          port: 80
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  - name: nlb-mw-ats-pub-mls
    env_name: a-usea1-a0-0
    module_path: modules/aws/nlb
    module_version: v10.8.10
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a20_app1
      ip_address_type: dualstack
      deployment_group: ats1
      mesh_name: webapps
      ingressgateway_name: mw-pub-ingressgateway-mls
      subnet_type: public-eip
      domain: a20.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-80:
          protocol: TCP
          port: 80
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
