version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
    - name: us-vaamtga0
      env_name: a-usea1-a0-0
      domain: a20.prod.infra.webex.com
      local_dns_enabled: true
      infractl_version: v7.18.36
      cluster_api: true
      zone_type: public
      apiserver_record_public: true
      module_version: v10.10.1
      s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      s3_bucket_region: us-east-2
      use_provider_template: true
      terraform_version: v1.5.7
      terraform_templates:
        - path: providers.tf.tpl
      admin_port: 6443
      aws_infra_azs:
        - us-east-1a
        - us-east-1b
        - us-east-1c
      aws_infra_region: us-east-1
      base_os: jammy-fips
      bastion_count: 1
      bastion_flavor: t3a.small
      bastion_image_name: WBX-Ubuntu-20-x86_64-harden-nonfips-v1.2-202211
      cloud_service_provider: aws
      datacenter: us-east-1
      dns_credentials_path: secret/data/mccprod/infra/route53/credentials
      gateway_name: not-used
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      kubernetes_version: v1.30.1
      location: IAD
      managed_by: us-txccnc2
      master_count: 3
      master_flavor: c6a.2xlarge
      mesh:
        enabled: true
      metadata:
        annotations:
            helm3Only: true
        cluster_type: webapps-ats
        deployment_groups:
            - webapps-ats-aws
            - ats1
            - ats1-dc
        platform_release_channel: alpha-1
      network_name: a-usea1-a0-0-ha1
      security_groups:
        istio_public:
          rules:
            - name: https
              protocol: tcp
              from: 8443
              to: 8443
              cidrs:
              - 0.0.0.0/0
              type: ingress
            - name: healthcheck
              protocol: tcp
              from: 15021
              to: 15021
              cidrs:
              - vpc
              type: ingress
            - name: http
              protocol: tcp
              from: 8081
              to: 8081
              cidrs:
              - 0.0.0.0/0
              type: ingress
          tags:
          - key: kubernetes.io/cluster/us-vaamtga0
            value: owned
        istio_private:
          rules:
          - name: https
            protocol: tcp
            from: 8443
            to: 8443
            cidrs:
            - 10.0.0.0/8
            type: ingress
          - name: healthcheck
            protocol: tcp
            from: 15021
            to: 15021
            cidrs:
            - vpc
            type: ingress
          tags:
          - key: kubernetes.io/cluster/us-vaamtga0
            value: owned
      node_pools:
        - flavor: m5a.2xlarge
          max_size: 10
          metadata:
            node_labels: type=worker
          min_size: 3
          name: platform-worker
          type: worker
        - availability_zones:
            - us-east-1a
          flavor: r5a.2xlarge
          max_size: 1
          metadata:
            node_labels: type=storage-node
            node_taints: type=storage-node:NoSchedule
          min_size: 1
          name: optimized-storage-1a
          type: worker
        - availability_zones:
            - us-east-1b
          flavor: r5a.2xlarge
          max_size: 1
          metadata:
            node_labels: type=storage-node
            node_taints: type=storage-node:NoSchedule
          min_size: 1
          name: optimized-storage-1b
          type: worker
        - availability_zones:
            - us-east-1c
          flavor: r5a.2xlarge
          max_size: 1
          metadata:
            node_labels: type=storage-node
            node_taints: type=storage-node:NoSchedule
          min_size: 1
          name: optimized-storage-1c
          type: worker
        - availability_zones:
            - us-east-1c
          flavor: m5a.4xlarge
          max_size: 36
          metadata:
            node_labels: type=mw-worker
            node_taints: dedicated=webapps:NoSchedule
          min_size: 1
          name: mw-p0
          type: worker
          volume_size: 80
        - availability_zones:
            - us-east-1c
          flavor: m5a.4xlarge
          max_size: 5
          metadata:
            node_labels: dedicated=nbr,type=provider-worker
            node_taints: dedicated=nbr:NoSchedule
          min_size: 1
          name: mw-p1
          type: worker
          volume_size: 80
        - availability_zones:
            - us-east-1a
          flavor: m5a.2xlarge
          max_size: 1
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ip-1a
          type: worker
          cluster_security_groups:
            - name: istio_public
        - availability_zones:
            - us-east-1b
          flavor: m5a.2xlarge
          max_size: 1
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ip-1b
          type: worker
          cluster_security_groups:
            - name: istio_public
        - availability_zones:
            - us-east-1c
          flavor: m5a.2xlarge
          max_size: 1
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ip-1c
          type: worker
          cluster_security_groups:
            - name: istio_public
        - availability_zones:
            - us-east-1a
          flavor: m5a.2xlarge
          max_size: 1
          metadata:
            node_labels: dedicated=istio-ingress-ciscoint
            node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
          min_size: 1
          name: istio-ip-cisco-1a
          type: worker
          cluster_security_groups:
            - name: istio_private
        - availability_zones:
            - us-east-1b
          flavor: m5a.2xlarge
          max_size: 1
          metadata:
            node_labels: dedicated=istio-ingress-ciscoint
            node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
          min_size: 1
          name: istio-ip-cisco-1b
          type: worker
          cluster_security_groups:
            - name: istio_private
        - availability_zones:
            - us-east-1c
          flavor: m5a.2xlarge
          max_size: 1
          metadata:
            node_labels: dedicated=istio-ingress-ciscoint
            node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
          min_size: 1
          name: istio-ip-cisco-1c
          type: worker
          cluster_security_groups:
            - name: istio_private
      pipeline_bundles:
        - platform/post-provision.yaml
        - platform/aws-cloud-controller.yaml
        - platform/base-apps.yaml
      release: v1.7.2
      status: online
      vpc_mission_tag_app: teams
      vpc_routing: true
      worker_flavor: m5a.2xlarge
defaults:
    import_defaults:
        - ../../../../manifest.yaml
