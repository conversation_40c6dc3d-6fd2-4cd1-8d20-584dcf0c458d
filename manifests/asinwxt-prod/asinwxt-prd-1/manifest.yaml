version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
- name: asinwxt-prd-1
  status: offline
  metadata:
    cluster_type: wxt
    platform_release_channel: stable-3
    annotations:
      helm3Only: true
    deployment_groups:
    - kubed-prod-wxt
    - anycast-prod
    - webex-persistence-tee4x-prod
    - apsvcs-wxt-asin-prd-1
  base_image: wbx3-focal-1.23.5-containerd-450_aa19634
  image_name: wbx3-focal-1.23.5-containerd-450_aa19634
  scale_image_name: wbx3-focal-1.23.5-containerd-450_aa19634
  gateway_name: asinwxt-prod
  env_name: asinwxt-prod
  provisioning_module_version: v5.7.0
  module_version: v7.23.4-patch6
  infractl_version: v7.17.0
  terraform_version: v1.2.4
  gluster_count: 0
  worker_count: 2
  ingress_count: 1
  ingress_int_count: 1
  cidr_pods: auto
  cidr_svcs: auto
  cidr_svcs_prefix: 16
  optimized_storage_flavor: r5.4xlarge
  worker_flavor: m5a.2xlarge
  create_eip: true
  vpc_routing: true
  optimized_storage_count: 0
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/aws-cloud-controller.yaml
  - platform/base-apps.yaml
  - platform/cluster-autoscaler.yaml
  - platform/falco.yaml
  - platform/kafka-base-apps-v2.yaml
  provisioning_extra_args: container_manager='containerd' containerd_instance_storage='true'
    enable_coredns_resolv_file_forwarding='true'
  mesh:
    enabled: true
  node_pools:
  - name: optimized-storage-pool
    type: worker
    min_size: 0
    max_size: 5
    flavor: r6i.2xlarge
    fallback_flavors:
    - r6i.2xlarge
    - r6a.2xlarge
    - r5a.2xlarge
    metadata:
      node_labels: type=optimized-storage-node
      node_taints: dedicated=prometheus:NoSchedule
  - type: worker
    name: istio-ingress-pool
    min_size: 3
    max_size: 9
    flavor: c6a.2xlarge
    fallback_flavors:
    - c6a.2xlarge
    - c5a.2xlarge
    - c5.2xlarge
    metadata:
      node_labels: dedicated=istio-ingress
      node_taints: dedicated=istio-ingress:NoSchedule
  - name: istio-ingress-cisco
    type: worker
    min_size: 3
    max_size: 6
    flavor: c6a.large
    fallback_flavors:
    - c6a.large
    - c5.2xlarge
    metadata:
      node_labels: dedicated=istio-ingress-ciscoint
      node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
  - name: worker
    type: worker
    flavor: m5a.2xlarge
    fallback_flavors:
    - m5a.2xlarge
    - m5.2xlarge
    min_size: 0
    max_size: 10
  - name: worker-1a
    availability_zones:
    - ap-southeast-1a
    type: worker
    flavor: m5a.2xlarge
    fallback_flavors:
    - m5a.2xlarge
    - m5.2xlarge
    min_size: 0
    max_size: 5
  - name: worker-1b
    availability_zones:
    - ap-southeast-1b
    type: worker
    flavor: m5a.2xlarge
    fallback_flavors:
    - m5a.2xlarge
    - m5.2xlarge
    min_size: 0
    max_size: 5
  - name: worker-1c
    availability_zones:
    - ap-southeast-1c
    type: worker
    flavor: m5a.2xlarge
    fallback_flavors:
    - m5a.2xlarge
    - m5.2xlarge
    min_size: 0
    max_size: 5
  - name: wxt-4xlarge
    type: worker
    flavor: c6a.4xlarge
    fallback_flavors:
    - c6a.4xlarge
    - c5a.4xlarge
    - c5.4xlarge
    min_size: 0
    max_size: 5
    metadata:
      node_taints: dedicated=prod-asin:NoSchedule
      node_labels: dedicated=prod-asin
  - name: wxt-4xlarge-1a
    availability_zones:
    - ap-southeast-1a
    type: worker
    flavor: c6a.4xlarge
    fallback_flavors:
    - c6a.4xlarge
    - c5a.4xlarge
    - c5.4xlarge
    min_size: 0
    max_size: 10
    metadata:
      node_taints: dedicated=prod-asin:NoSchedule
      node_labels: dedicated=prod-asin
  - name: wxt-4xlarge-1b
    availability_zones:
    - ap-southeast-1b
    type: worker
    flavor: c6a.4xlarge
    fallback_flavors:
    - c6a.4xlarge
    - c5a.4xlarge
    - c5.4xlarge
    min_size: 0
    max_size: 10
    metadata:
      node_taints: dedicated=prod-asin:NoSchedule
      node_labels: dedicated=prod-asin
  - name: wxt-4xlarge-1c
    availability_zones:
    - ap-southeast-1c
    type: worker
    flavor: c6a.4xlarge
    fallback_flavors:
    - c6a.4xlarge
    - c5a.4xlarge
    - c5.4xlarge
    min_size: 0
    max_size: 10
    metadata:
      node_taints: dedicated=prod-asin:NoSchedule
      node_labels: dedicated=prod-asin
  - name: optimized-storage-2a
    availability_zones: [ap-southeast-1a]
    type: worker
    flavor: r6a.4xlarge
    fallback_flavors:
    - r5a.4xlarge
    - r6i.4xlarge
    min_size: 0
    max_size: 5
    metadata:
      node_labels: type=optimized-storage-node
      node_taints: type=optimized-storage-node:NoSchedule
  - name: optimized-storage-2b
    availability_zones: [ap-southeast-1b]
    type: worker
    flavor: r6a.4xlarge
    fallback_flavors:
    - r5a.4xlarge
    - r6i.4xlarge
    min_size: 0
    max_size: 5
    metadata:
      node_labels: type=optimized-storage-node
      node_taints: type=optimized-storage-node:NoSchedule
  - name: optimized-storage-2c
    availability_zones: [ap-southeast-1c]
    type: worker
    flavor: r6a.4xlarge
    fallback_flavors:
    - r5a.4xlarge
    - r6i.4xlarge
    min_size: 0
    max_size: 5
    metadata:
      node_labels: type=optimized-storage-node
      node_taints: type=optimized-storage-node:NoSchedule
  - name: anycast-proxy-pool
    type: worker
    min_size: 2
    max_size: 10
    flavor: c6a.large
    security_groups:
      int:
      - name: anycast-proxy-tls
        type: ingress
        protocol: tcp
        from: 10511
        to: 10511
        cidr: 0.0.0.0/0
      - name: anycast-proxy-healthcheck
        type: ingress
        protocol: tcp
        from: 15021
        to: 15021
        cidr: 0.0.0.0/0
    metadata:
      node_labels: type=anycast-proxy-node
      node_taints: dedicated=anycast-proxy-node:NoSchedule
defaults:
  import_defaults: [../../../manifest.yaml]
