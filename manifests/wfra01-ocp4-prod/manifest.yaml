---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
- name: wfra01-ocp4-prod
  gateway_name: wfra01-ocp4-prod
  domain: prod.infra.webex.com
  internal_network: wfra01-ocp4-prod
  external_network: provider-427
  management_network: WebexAudio-beech-private-426
  worker_eth1_network: WebexAudio-beech-private-426
  internal_provider_network: WebexAudio-beech-private-426
  internal_provider_subnets: [10.247.212.0/24]
  base_image: wbx3-focal-1.23.5-containerd-v1-23-4
  base_k8s_image: wbx3-focal-1.23.5-containerd-v1-23-4
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  image_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  optimized_storage_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  external_media_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  thousand_eyes_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  infra_credentials_path: secret/data/mccprod/infra/wfra-meet-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  terraform_version: v1.2.4
  module_version: v7.23.4-custom-routing
  module_commit: a879a5252caf36664627b0a193388367aa94681b
  infractl_version: v7.17.2
  provisioning_module_version: v5.6.0
  backend: s3
  location: FRA
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  master_count: 3
  worker_count: 1
  ingress_count: 2
  ingress_int_count: 2
  thousand_eyes_node_count: 0
  optimized_storage_count: 0
  bastion_count: 1
  internal_media_node_count: 0
  external_media_node_count: 0
  internal_mini_media_node_count: 0
  gluster_count: 0
- name: wfra01-ocp4-prod-media
  gateway_name: wfra01-ocp4-prod-media
  network_name: wfra01-ocp4-prod-media
  domain: prod.infra.webex.com
  internal_network: wfra01-ocp4-prod-media
  external_network: Pub-Calliope-Telephony-cmr4-423
  management_network: Prv-Calliope-Telephony-cmr4-422
  worker_eth1_network: Prv-Calliope-Telephony-cmr4-422
  internal_provider_network: Prv-Calliope-Telephony-cmr4-422
  internal_provider_subnets: [10.0.0.0/8]
  base_image: wbx3-focal-1.23.5-containerd-8800b2a
  base_k8s_image: wbx3-focal-1.23.5-containerd-8800b2a
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  image_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  optimized_storage_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  external_media_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  thousand_eyes_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  infra_credentials_path: secret/data/mccprod/infra/wfra-meet-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  terraform_version: v1.2.4
  module_version: v7.23.4
  infractl_version: v7.17.2
  provisioning_module_version: v5.6.0
  backend: s3
  location: FRA
  ocp_ownership_business_service: "WBX3 Platform - Meetings"
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  master_count: 3
  worker_count: 1
  ingress_count: 3
  ingress_int_count: 1
  thousand_eyes_node_count: 0
  optimized_storage_count: 0
  bastion_count: 1
  internal_media_node_count: 0
  external_media_node_count: 0
  internal_mini_media_node_count: 0
  gluster_count: 0
networks:
- name: wfra01-ocp4-prod
  env_name: wfra01-ocp4-prod
- name: wfra01-ocp4-prod-media
  env_name: wfra01-ocp4-prod-media
gateways:
- name: wfra01-ocp4-prod
  env_name: wfra01-ocp4-prod
  gateway_count: 2
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  external_network: Pub-Calliope-Telephony-cmr4-414
  management_network: Prv-Calliope-Telephony-cmr4-415
  worker_eth1_network: Prv-Calliope-Telephony-cmr4-415
  internal_provider_network: Prv-Calliope-Telephony-cmr4-415
  gateway_block_storage: true
- name: wfra01-ocp4-prod-media
  env_name: wfra01-ocp4-prod-media
  gateway_count: 2
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  gateway_block_storage: true
defaults:
  import_defaults: ["../../manifest.yaml"]
