version: 1
command_and_control: mccprod
clusters:
- name: wfram-a-4
  status: online
  service_block: &wfram-a-4_sb europe-b
  env_name: wfra01-ocp4-prod-media
  cloud_service_provider: openstack
  module_version: v7.23.4-patch-customised-storage-size
  module_commit: f9fefc95dc512fe11ec718e30dee6d902a29c71f
  ingress_count: 3
  ingress_int_count: 1
  bastion_count: 1
  master_count: 3
  worker_count: 1
  optimized_storage_count: 0
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  bastion_flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  health_checks: true
  cidr_pods: auto
  cidr_svcs: auto
  provisioning_extra_args: container_manager='containerd'
  mesh:
    enabled: true
  metadata:
    cluster_type: media
    platform_release_channel: stable-2
    annotations:
      calliope_org: wfram
      calliope_group: wfram
      service_block: *wfram-a-4_sb
      helm3Only: true
    deployment_groups:
    - crc-prod-europe-b-fra
    - apsvcs-media-wfram-a-4
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/external-dns.yaml
  - platform/base-apps.yaml
  - media/init.yaml
  - media/lma-mirrors.yaml
  - media/calliope-media.yaml
  - platform/falco.yaml
  node_pools:
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.16vcpu.128mem.80ssd.0eph
    root_block_storage: false
    metadata:
      node_labels: type=optimized-storage-node
  - name: storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.16vcpu.128mem.0ssd.0eph
    volume_size: 512
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - name: worker
    type: worker
    min_size: 6
    max_size: 6
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: pool-name=worker,type=worker
  - name: internal-media-pool
    type: internal-media
    root_block_storage: true
    flavor: PROD-Spark-Media.47vcpu.64mem.0ssd.0eph.numa
    min_size: 50
    max_size: 50
  - name: external-media-pool
    type: external-media
    root_block_storage: true
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    min_size: 156
    max_size: 156
defaults:
  import_defaults: [../../../../manifest.yaml]
