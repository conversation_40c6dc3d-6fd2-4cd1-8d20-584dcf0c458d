version: 1
command_and_control: mccprod
infra_service_domain: null
clusters:
    - name: wdfwlma-p-1
      env_name: w-dfwha-p0-0
      release: v1.8.1
      infractl_version: v7.18.44
      cluster_api: true
      cluster_chart_version: 0.8.10
      module_version: v10.14.3
      use_provider_template: true
      embed_provider_template: false
      module_path: modules/openstack/capi-infra
      terraform_version: v1.10.4
      admin_port: 6443
      availability_zones:
        - compute: az1
          volume: az1
        - compute: az2
          volume: az2
        - compute: az3
          volume: az3
      aws_infra_region: us-east-1
      bastion_count: 0
      bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
      cidr_node_prefix: 26
      cidr_pods: auto
      cidr_svcs: auto
      cloud_service_provider: openstack
      control_plane_network_type: prv_kubed_network
      enable_lb_port_security: false
      external_network: public-network
      gateway_name: not-used
      image: wbx3-capi-jammy-1.31.5-c8d-amd64-v1.29.6
      image_name: wbx3-capi-jammy-1.31.5-c8d-amd64-v1.29.6
      internal_network_type: iso_network
      internal_provider_network: provider-network
      kubernetes_version: v1.31.5
      managed_by: us-txccnc2
      master_count: 3
      master_flavor: gp1.3xlarge
      mesh:
        enabled: true
      metadata:
        annotations:
            helm3Only: true
        cloud_type: agglog
        cluster_type: agglog
        deployment_groups:
            - kubed-prod-gen
            - agglog-3az-prod
            - aggmet-3az-prod
            - agglog-strimzi-ocp-aggregate
      nameservers:
        - 10.240.240.1
      network_mission_tag: lma
      network_name: w-dfwha-p0-0-a1
      node_pools:
        - flavor: gp1.3xlarge
          max_size: 5
          metadata:
            node_labels: type=worker
          min_size: 5
          name: worker
          type: worker
        - cluster_security_groups:
            - name: istio_public
          flavor: gp1.large
          max_size: 3
          metadata:
            node_labels: type=worker,dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 3
          name: istio-ingress
          type: worker
        - flavor: gp1.3xlarge
          max_size: 11
          metadata:
            node_labels: pool-name=agglog-loki
          min_size: 11
          name: agglog-loki
          type: worker
        - flavor: so2.medium
          max_size: 14
          metadata:
            node_labels: pool-name=agglog-strimzi
          min_size: 14
          name: agglog-strimzi
          type: worker
        - flavor: gp1.2xlarge
          max_size: 5
          metadata:
            node_labels: pool-name=agglog-general
          min_size: 5
          name: agglog-general
          type: worker
        - flavor: gp1.5xlarge
          max_size: 1
          metadata:
            node_labels: pool-name=agglog-prometheus
          min_size: 1
          name: agglog-prometheus
          type: worker
        - flavor: gp1.5xlarge
          max_size: 53
          metadata:
            node_labels: pool-name=agglog-logstash
          min_size: 53
          name: agglog-logstash
          type: worker
      pipeline_bundles:
        - platform/post-provision.yaml
        - platform/openstack-cloud-controller.yaml
        - platform/base-apps.yaml
      pod_subnet: **********/16
      pod_subnet_size: 16
      route_reflector_name: dfwha-p0-rr
      security_groups:
        istio_public:
            rules:
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 443
                  name: lb-tcp-443
                  protocol: tcp
                  to: 443
                  type: ingress
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 15020
                  name: lb-tcp-15020
                  protocol: tcp
                  to: 15020
                  type: ingress
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 15021
                  name: lb-tcp-15021
                  protocol: tcp
                  to: 15021
                  type: ingress
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 4000
                  name: lb-tcp-4000
                  protocol: tcp
                  to: 4000
                  type: ingress
      status: online
      use_oidc: false
      volume_storage: true
    - name: wdfwmet-p-1
      env_name: w-dfwha-p0-0
      release: v1.8.1
      infractl_version: v7.18.44
      cluster_api: true
      cluster_chart_version: 0.8.10
      module_version: v10.14.3
      use_provider_template: true
      embed_provider_template: false
      module_path: modules/openstack/capi-infra
      terraform_version: v1.10.4
      admin_port: 6443
      availability_zones:
        - compute: az1
          volume: az1
        - compute: az2
          volume: az2
        - compute: az3
          volume: az3
      aws_infra_region: us-east-1
      bastion_count: 0
      bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
      cidr_node_prefix: 26
      cidr_pods: auto
      cidr_svcs: auto
      cloud_service_provider: openstack
      control_plane_network_type: prv_kubed_network
      enable_lb_port_security: false
      external_network: public-network
      gateway_name: not-used
      image: wbx3-capi-jammy-1.31.5-c8d-amd64-v2.25.4.0
      image_name: wbx3-capi-jammy-1.31.5-c8d-amd64-v2.25.4.0
      internal_network_type: iso_network
      internal_provider_network: provider-network
      kubernetes_version: v1.31.5
      managed_by: us-txccnc2
      master_count: 3
      master_flavor: gp1.3xlarge
      mesh:
        enabled: true
      metadata:
        annotations:
            helm3Only: true
        cloud_type: aggmet
        cluster_type: aggmet
        deployment_groups:
            - kubed-prod-gen
            - aggmet-3az-prod
            - aggmet-mimir
      nameservers:
        - 10.240.240.1
      network_mission_tag: lma
      network_name: w-dfwha-p0-0-a1
      node_pools:
        - flavor: gp1.3xlarge
          max_size: 2
          metadata:
            node_labels: type=worker
          min_size: 2
          name: worker
          type: worker
        - cluster_security_groups:
            - name: istio_public
          flavor: gp1.large
          max_size: 9
          metadata:
            node_labels: type=worker,dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 9
          name: istio-ingress
          type: worker
        - flavor: gpa1.3xlarge
          max_size: 63
          metadata:
            node_labels: pool-name=aggmet-general
          min_size: 63
          name: aggmet-general
          type: worker
        - flavor: gpa1.4xlarge
          max_size: 39
          metadata:
            node_labels: pool-name=aggmet-mimir
          min_size: 39
          name: aggmet-mimir
          type: worker
      pipeline_bundles:
        - platform/post-provision.yaml
        - platform/openstack-cloud-controller.yaml
        - platform/base-apps.yaml
      pod_subnet: **********/17
      pod_subnet_size: 17
      route_reflector_name: dfwha-p0-rr
      security_groups:
        istio_public:
            rules:
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 443
                  name: lb-tcp-443
                  protocol: tcp
                  to: 443
                  type: ingress
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 15020
                  name: lb-tcp-15020
                  protocol: tcp
                  to: 15020
                  type: ingress
                - cidrs:
                    - ***********/23
                    - ************/23
                    - *************/23
                  from: 15021
                  name: lb-tcp-15021
                  protocol: tcp
                  to: 15021
                  type: ingress
      status: online
      use_oidc: false
      volume_storage: true
defaults:
    import_defaults:
        - ../../../../manifest.yaml
