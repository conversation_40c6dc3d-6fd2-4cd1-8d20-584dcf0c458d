version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

environments:
- name: w-dfwha-p0-0
  blueprint_repo: null
  gateway_name: not-used
  backend: s3
  enable_credential_lookup: true
  infra_credentials_path: secret/data/mccprod/infra/dfwha-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  domain: prod.infra.webex.com
  bastion_flavor: go1.small
  ingress_flavor: gp1.medium
  ingress_int_flavor: gp1.medium
  master_flavor: gp1.large
  worker_flavor: gp1.large
  terraform_version: v1.10.4
  cloud_provider: openstack
  ocp_ownership_server_type: wxkbsvr
  ocp_ownership_support_group: wbx3-prod
  ocp_ownership_component: wxkb
  ocp_ownership_business_service: WBX3 Platform - Meetings
  location: DFW

defaults:
    module_version: v10.13.4
    infractl_version: v7.18.44
    use_provider_template: true
    embed_provider_template: true
    terraform_templates:
      - path: providers-commercial.tf.tpl
    include_defaults: true
    import_defaults: ["../../../manifest.yaml"]
