version: 1
command_and_control: mccprod
clusters:
- name: sa-02wrtm2
  status: online
  service_block: &sa-02wrtm2_sb middle-east
  env_name: wjed01-ocp4-prod
  external_network: provider-432
  release: v1.7.1
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/openstack-cloud-controller.yaml
  - platform/base-apps.yaml
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  datacenter: jed
  health_checks: true
  cidr_node_prefix: 26
  provisioning_extra_args: kubelet_max_pods=56 container_manager='containerd'
  module_version: v10.7.17-fix-sg-attachment
  module_commit: e3830b10c2ccbd1aa787df315948895af853e5f1
  server_group_policies: [soft-anti-affinity]
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_count: 3
  node_pools:
  - name: crc-pool
    type: worker
    min_size: 10
    max_size: 10
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: dedicated=crc,pool-name=crc-pool
      node_taints: dedicated=crc:NoSchedule
  - name: worker-pool
    type: worker
    min_size: 9
    max_size: 9
    root_block_storage: true
  - name: prom-pool
    type: worker
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    metadata:
      node_labels: type=optimized-storage-node
  - name: hesiod-pool
    type: internal-mini-media
    public_network_name: provider-432
    media_network_name: provider-412
    flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
    min_size: 20
    max_size: 20
    root_block_storage: true
    metadata:
      node_labels: type=hesiod
      dns_prefix: internal-mini-media
  - name: homer-pool
    type: external-large-media
    public_network_name: provider-432
    media_network_name: provider-412
    flavor: kubed-homer.nv.12vcpu.24mem.0ssd.0eph
    root_block_storage: true
    min_size: 45
    max_size: 45
    metadata:
      node_labels: type=external-large-media
      dns_prefix: xlm
  - name: mygdonus-pool
    type: internal-media
    media_network_name: provider-412
    flavor: kubed-media.nv.42vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 15
    max_size: 15
  - name: injector-pool
    type: internal-mini-media
    media_network_name: provider-412
    flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    min_size: 3
    max_size: 3
  - name: linus-pool
    type: external-media
    public_network_name: provider-432
    media_network_name: provider-412
    flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=linus
      dns_prefix: external-media
    min_size: 70
    max_size: 70
  - name: sip-pool-a
    type: external-media
    min_size: 16
    max_size: 16
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-417
    public_network_name: provider-433
    metadata:
      dns_prefix: sse-a
      node_labels: pool-name=sip-pool-a,type=external-media
    provider_security_group_names:
    - sse-media
    public_security_group_names:
    - sse-ext
  - name: media-pool-a
    type: external-media
    min_size: 22
    max_size: 22
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-417
    public_network_name: provider-433
    metadata:
      dns_prefix: mse-a
      node_labels: pool-name=media-pool-a,type=external-media
    provider_security_group_names:
    - mse-media
    public_security_group_names:
    - mse-ext
  metadata:
    cluster_type: wxcedge-prod
    platform_release_channel: stable-1
    annotations:
      calliope_org: wjedm
      calliope_group: wjedm
      service_block: *sa-02wrtm2_sb
      helm3Only: true
    deployment_groups:
      - crc-prod-middle-east-jed
      - wxc-edge-sse-sa-02wrtm2-prod
      - wxc-edge-mse-sa-02wrtm2-prod
      - wxc-edge-sbc-operator-sa-02wrtm2-prod
  security_groups:
    sse-media:
    - name: sse-sip-internal-udp-1
      type: ingress
      protocol: udp
      from: 5060
      to: 5060
      cidr: 10.0.0.0/8
    - name: sse-sip-internal-udp-2
      type: ingress
      protocol: udp
      from: 5060
      to: 5060
      cidr: **********/12
    - name: sse-sip-internal-tcp-1
      type: ingress
      protocol: tcp
      from: 5060
      to: 5060
      cidr: 10.0.0.0/8
    - name: sse-sip-internal-tcp-2
      type: ingress
      protocol: tcp
      from: 5060
      to: 5060
      cidr: **********/12
    sse-ext:
    - name: sse-sip-internal-tcp-3
      type: ingress
      protocol: tcp
      from: 5061
      to: 5061
      cidr: 0.0.0.0/0
    - name: sse-sip-internal-tcp-4
      type: ingress
      protocol: tcp
      from: 5062
      to: 5062
      cidr: 0.0.0.0/0
    - name: sse-sip-internal-tcp-5
      type: ingress
      protocol: tcp
      from: 8934
      to: 8934
      cidr: 0.0.0.0/0
    mse-media:
    - name: mse-grpc-tcp-1
      type: ingress
      protocol: tcp
      from: 9443
      to: 9443
      cidr: 10.0.0.0/8
    - name: mse-grpc-tcp-2
      type: ingress
      protocol: tcp
      from: 9443
      to: 9443
      cidr: **********/12
    - name: mse-media-rtp-udp-media-1
      type: ingress
      protocol: udp
      from: 19560
      to: 65535
      cidr: 10.0.0.0/8
    - name: mse-media-rtp-udp-media-2
      type: ingress
      protocol: udp
      from: 19560
      to: 65535
      cidr: **********/12
    mse-ext:
    - name: mse-media-rtp-udp
      type: ingress
      protocol: udp
      from: 19560
      to: 65535
      cidr: 0.0.0.0/0
    - name: mse-media-multiplexed
      type: ingress
      protocol: udp
      from: 5004
      to: 5004
      cidr: 0.0.0.0/0
  domain: prod.infra.webex.com
defaults:
  import_defaults: [../manifest.yaml]
