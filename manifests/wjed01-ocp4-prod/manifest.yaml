---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
- name: wjed01-ocp4-prod
  gateway_name: wjed01-ocp4-prod
  domain: prod.infra.webex.com
  cloud_service_provider: openstack
  internal_network: wjed01-ocp4-prod
  # despite the name - provider-415 is actually a public network (163.129.51.0/24)
  external_network: provider-415
  internal_provider_network: provider-414
  internal_provider_subnets: [10.0.0.0/8]
  management_network: provider-414
  worker_eth1_network: provider-414
  base_image: wbx3-focal-1.25.6-containerd-v1.27.0
  base_k8s_image: wbx3-focal-1.25.6-containerd-v1.27.0
  infra_credentials_path: secret/data/mccprod/infra/wjed01-ocp4-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  image_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  optimized_storage_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  external_media_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  thousand_eyes_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  server_group_policies: [anti-affinity]
  terraform_version: v1.5.4
  module_version: v10.2.17
  infractl_version: v7.18.12
  provisioning_module_version: v5.11.10-validation-fix
  backend: s3
  location: JED
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  master_count: 3
  worker_count: 1
  ingress_count: 3
  ingress_int_count: 2
  thousand_eyes_node_count: 0
  optimized_storage_count: 0
  bastion_count: 1
  internal_media_node_count: 0
  external_media_node_count: 0
  internal_mini_media_node_count: 0
  gluster_count: 0
  ocp_ownership_business_service: "WBX3 Platform - Meetings"
networks:
- name: wjed01-ocp4-prod
  env_name: wjed01-ocp4-prod
gateways:
- name: wjed01-ocp4-prod
  env_name: wjed01-ocp4-prod
  internal_network: wjed01-ocp4-prod
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  volume_storage: true
  gateway_block_storage: true
  gateway_count: 2
defaults:
  import_defaults: ["../../manifest.yaml"]
