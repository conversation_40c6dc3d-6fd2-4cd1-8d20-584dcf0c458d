---
version: 1
command_and_control: mccprod
clusters:
- name: wbomm-a-3
  status: online
  service_block: &wbomm-a-3_sb asia-b
  env_name: wbom01-ocp4-media
  cloud_service_provider: openstack
  ingress_count: 3
  ingress_int_count: 1
  bastion_count: 1
  master_count: 3
  worker_count: 0
  optimized_storage_count: 0
  cidr_pods_prefix: 19
  cidr_node_prefix: 27
  worker_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  bastion_flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  health_checks: true
  cidr_pods: auto
  cidr_svcs: auto
  provisioning_extra_args: container_manager='containerd'
  mesh:
    enabled: true
  metadata:
    cluster_type: media
    platform_release_channel: stable-2
    deployment_groups:
    - crc-prod-asia-b-bom
    - apsvcs-media-wbomm-a-3
    annotations:
      calliope_org: wbomm
      calliope_group: wbomm
      service_block: *wbomm-a-3_sb
      helm3Only: true
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/external-dns.yaml
  - platform/base-apps.yaml
  - platform/kafka-base-apps.yaml
  - media/init.yaml
  - media/lma-mirrors.yaml
  - media/calliope-media.yaml
  - platform/falco.yaml
  node_pools:
  - name: crc-pool
    type: worker
    min_size: 10
    max_size: 10
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: dedicated=crc,pool-name=crc-pool
      node_taints: dedicated=crc:NoSchedule
  - name: optimized-storage-local
    type: worker
    min_size: 3
    max_size: 3
    flavor: 8vcpu.16mem.512ssd.0eph
    root_block_storage: false
    metadata:
      node_labels: type=optimized-storage-node
  - name: worker
    type: worker
    min_size: 9
    max_size: 9
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: pool-name=worker,type=worker
  - name: homer-pool
    type: external-large-media
    flavor: homer.12vcpu.24mem.0ssd.0eph
    root_block_storage: true
    min_size: 88
    max_size: 88
    metadata:
      node_labels: type=external-large-media
      dns_prefix: xlm
  - name: linus-pool
    type: external-media
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    public_network_name: Pub-Calliope-Telephony-cmr4-191
    media_network_name: Prv-Calliope-Telephony-cmr4-190
    root_block_storage: true
    min_size: 50
    max_size: 50
  - name: hesiod-pool
    type: internal-mini-media
    min_size: 4
    max_size: 4
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    root_block_storage: true
    metadata:
      node_labels: type=hesiod
      dns_prefix: internal-mini-media
  - name: injector-poold
    type: internal-mini-media
    min_size: 4
    max_size: 4
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    root_block_storage: true
  - name: mygdonus-pool
    type: internal-media
    min_size: 30
    max_size: 30
    flavor: PROD-Spark-Media.47vcpu.64mem.0ssd.0eph.numa
    root_block_storage: true
defaults:
  import_defaults: ["../../../../manifest.yaml"]
