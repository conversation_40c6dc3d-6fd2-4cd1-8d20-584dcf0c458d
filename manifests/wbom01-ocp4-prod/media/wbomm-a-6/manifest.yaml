version: 1
command_and_control: mccprod
clusters:
- name: wbomm-a-6
  status: online
  service_block: &wbomm-a-6_sb asia-bts
  env_name: wbom01-ocp4-media
  cloud_service_provider: openstack
  base_image: wbx3-focal-1.25.6-containerd-v1.27.0
  base_k8s_image: wbx3-focal-1.25.6-containerd-v1.27.0
  module_version: v10.2.17-v6
  module_commit: 13bb3d34b572947f37f790aa95dd9fdb3c96d2e8
  infractl_version: v7.18.12
  provisioning_module_version: v5.11.10-validation-fix
  terraform_version: v1.5.4
  external_network: Pub-Calliope-Telephony-cmr4-191
  internal_provider_network: Prv-Calliope-Telephony-cmr4-190
  management_network: Prv-Calliope-Telephony-cmr4-190
  master_count: 3
  bastion_count: 1
  worker_count: 0
  ingress_count: 2
  ingress_int_count: 1
  thousand_eyes_node_count: 0
  optimized_storage_count: 0
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  bastion_flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
  image_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  cidr_pods: auto
  cidr_svcs: auto
  provisioning_extra_args: container_manager='containerd'
  health_checks: true
  cidr_node_prefix: 26
  server_group_policies: [soft-anti-affinity]
  ocp_ownership_business_service: WBX3 Platform - Meetings
  metadata:
    cluster_type: media
    platform_release_channel: beta-2
    annotations:
      helm3Only: true
      calliope_org: bts
      calliope_group: wbomm
      service_block: *wbomm-a-6_sb
    deployment_groups:
    - crc-prod-asia-bts-bom
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/openstack-cloud-controller.yaml
  - platform/base-apps.yaml
  - media/init.yaml
  - media/lma-mirrors.yaml
  - media/calliope-media.yaml
  - platform/falco.yaml
  node_pools:
  - name: crc-pool
    type: worker
    min_size: 10
    max_size: 10
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: dedicated=crc,pool-name=crc-pool
      node_taints: dedicated=crc:NoSchedule
  - name: worker
    type: worker
    min_size: 7
    max_size: 7
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: pool-name=worker,type=worker
  - name: homer-pool
    type: external-large-media
    min_size: 6
    max_size: 6
    public_network_name: Pub-Calliope-Telephony-cmr4-191
    media_network_name: Prv-Calliope-Telephony-cmr4-190
    flavor: homer.12vcpu.24mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=external-large-media,infra.webex.com/remove-ds-a-record=true,infra.webex.com/dual-stack-identifier=ds
      dns_prefix: xlm
  - name: hesiod-pool
    type: internal-mini-media
    min_size: 2
    max_size: 2
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    root_block_storage: true
    metadata:
      node_labels: type=hesiod
      dns_prefix: internal-mini-media
  - name: mygdonus-pool
    type: internal-media
    min_size: 2
    max_size: 2
    flavor: PROD-Spark-Media.47vcpu.64mem.0ssd.0eph.numa
    root_block_storage: true
  - name: linus-pool
    type: external-media
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    root_block_storage: true
    min_size: 4
    max_size: 4
  - name: injector-pool
    type: internal-mini-media
    min_size: 2
    max_size: 2
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    root_block_storage: true
  external_media_sg_rules:
  - name: external-media-https
    type: ingress
    protocol: tcp
    from: 443
    to: 444
    cidr: 0.0.0.0/0
  - name: external-media-cascade-ports-tcp
    type: ingress
    protocol: tcp
    from: 11000
    to: 33000
    cidr: 0.0.0.0/0
  - name: external-media-cascade-ports-udp
    type: ingress
    protocol: udp
    from: 11000
    to: 33000
    cidr: 0.0.0.0/0
  - name: external-media-rtp-ports-tcp
    type: ingress
    protocol: tcp
    from: 49152
    to: 65535
    cidr: 0.0.0.0/0
  - name: external-media-rtp-ports-udp
    type: ingress
    protocol: udp
    from: 49152
    to: 65535
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-1-tcp
    type: ingress
    protocol: tcp
    from: 5004
    to: 5004
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-1-udp
    type: ingress
    protocol: udp
    from: 5004
    to: 5004
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-3-tcp
    type: ingress
    protocol: tcp
    from: 9000
    to: 9000
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-3-udp
    type: ingress
    protocol: udp
    from: 9000
    to: 9000
    cidr: 0.0.0.0/0
  - name: external-media-https-v6
    type: ingress
    protocol: tcp
    from: 443
    to: 444
    cidr: "::/0"
  - name: external-media-cascade-ports-tcp-v6
    type: ingress
    protocol: tcp
    from: 11000
    to: 33000
    cidr: "::/0"
  - name: external-media-cascade-ports-udp-v6
    type: ingress
    protocol: udp
    from: 11000
    to: 33000
    cidr: "::/0"
  - name: external-media-rtp-ports-tcp-v6
    type: ingress
    protocol: tcp
    from: 49152
    to: 65535
    cidr: "::/0"
  - name: external-media-rtp-ports-udp-v6
    type: ingress
    protocol: udp
    from: 49152
    to: 65535
    cidr: "::/0"
  - name: external-media-shared-ports-1-tcp-v6
    type: ingress
    protocol: tcp
    from: 5004
    to: 5004
    cidr: "::/0"
  - name: external-media-shared-ports-1-udp-v6
    type: ingress
    protocol: udp
    from: 5004
    to: 5004
    cidr: "::/0"
  - name: external-media-shared-ports-3-tcp-v6
    type: ingress
    protocol: tcp
    from: 9000
    to: 9000
    cidr: "::/0"
  - name: external-media-shared-ports-3-udp-v6
    type: ingress
    protocol: udp
    from: 9000
    to: 9000
    cidr: "::/0"
defaults:
  import_defaults: [../../../../manifest.yaml]
