version: 1
command_and_control: mccprod
clusters:
- name: wmelm-b-1
  status: online
  service_block: &wmelm-b-1_sb asia-b
  env_name: wmel01-ocp4-prod-media
  base_image: wbx3-jammy-1.27.12-containerd-v2-25-2-0
  base_k8s_image: wbx3-jammy-1.27.12-containerd-v2-25-2-0
  module_version: v10.13.9
  infractl_version: v7.18.12
  provisioning_module_version: v5.12.5
  terraform_version: v1.5.4
  external_network: public-417
  management_network: wbx3-provider-416
  internal_provider_network: wbx3-provider-416
  cloud_service_provider: openstack
  ingress_count: 3
  ingress_int_count: 2
  bastion_count: 1
  master_count: 3
  worker_count: 0
  optimized_storage_count: 0
  cidr_node_prefix: 26
  worker_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  bastion_flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  health_checks: true
  cidr_pods: auto
  cidr_svcs: auto
  provisioning_extra_args: container_manager='containerd'
  metadata:
    cluster_type: media
    platform_release_channel: stable-1
    deployment_groups:
    - crc-prod-asia-b-mel
    annotations:
      calliope_org: wmelm
      calliope_group: wmelm
      service_block: *wmelm-b-1_sb
      helm3Only: true
  pipeline_bundles:
  - platform/openstack-cloud-controller.yaml
  - platform/post-provision.yaml
  - platform/base-apps.yaml
  - media/init.yaml
  - media/lma-mirrors.yaml
  - media/calliope-media.yaml
  node_pools:
  - name: storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    volume_size: 256
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - name: worker
    type: worker
    min_size: 5
    max_size: 5
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: pool-name=worker,type=worker
  - name: crc-pool
    type: worker
    min_size: 10
    max_size: 10
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: dedicated=crc,pool-name=crc-pool
      node_taints: dedicated=crc:NoSchedule
  - name: homer-pool
    type: external-large-media
    public_network_name: public-417
    media_network_name: wbx3-provider-416
    flavor: kubed-homer.nv.12vcpu.24mem.0ssd.0eph
    root_block_storage: true
    min_size: 108
    max_size: 108
    metadata:
      node_labels: type=external-large-media,infra.webex.com/remove-ds-a-record=true,infra.webex.com/dual-stack-identifier=ds
      dns_prefix: xlm
  - name: linus-pool
    type: external-media
    public_network_name: public-417
    media_network_name: wbx3-provider-416
    flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    min_size: 70
    max_size: 70
  - name: hesiod-pool
    type: internal-mini-media
    media_network_name: wbx3-provider-416
    flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
    min_size: 20
    max_size: 20
    root_block_storage: true
    metadata:
      node_labels: type=hesiod
      dns_prefix: internal-mini-media
    image_name: wbx3-jammy-1.27.12-containerd-v2-25-3-2
  - name: injector-pool
    type: internal-mini-media
    media_network_name: wbx3-provider-416
    flavor: kubed-media.nv.8vcpu.16mem.0ssd.0eph
    root_block_storage: true
    min_size: 4
    max_size: 4
    image_name: wbx3-jammy-1.27.12-containerd-v2-25-3-2
  - name: mygdonus-pool
    type: internal-media
    media_network_name: wbx3-provider-416
    flavor: kubed-media.nv.47vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 0
    max_size: 0
    image_name: wbx3-jammy-1.27.12-containerd-v2-25-3-2
  external_media_sg_rules:
  - name: external-media-https
    type: ingress
    protocol: tcp
    from: 443
    to: 444
    cidr: 0.0.0.0/0
  - name: external-media-cascade-ports-tcp
    type: ingress
    protocol: tcp
    from: 11000
    to: 33000
    cidr: 0.0.0.0/0
  - name: external-media-cascade-ports-udp
    type: ingress
    protocol: udp
    from: 11000
    to: 33000
    cidr: 0.0.0.0/0
  - name: external-media-rtp-ports-tcp
    type: ingress
    protocol: tcp
    from: 49152
    to: 65535
    cidr: 0.0.0.0/0
  - name: external-media-rtp-ports-udp
    type: ingress
    protocol: udp
    from: 49152
    to: 65535
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-1-tcp
    type: ingress
    protocol: tcp
    from: 5004
    to: 5004
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-1-udp
    type: ingress
    protocol: udp
    from: 5004
    to: 5004
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-3-tcp
    type: ingress
    protocol: tcp
    from: 9000
    to: 9000
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-3-udp
    type: ingress
    protocol: udp
    from: 9000
    to: 9000
    cidr: 0.0.0.0/0
  - name: external-media-https-v6
    type: ingress
    protocol: tcp
    from: 443
    to: 444
    cidr: ::/0
  - name: external-media-cascade-ports-tcp-v6
    type: ingress
    protocol: tcp
    from: 11000
    to: 33000
    cidr: ::/0
  - name: external-media-cascade-ports-udp-v6
    type: ingress
    protocol: udp
    from: 11000
    to: 33000
    cidr: ::/0
  - name: external-media-rtp-ports-tcp-v6
    type: ingress
    protocol: tcp
    from: 49152
    to: 65535
    cidr: ::/0
  - name: external-media-rtp-ports-udp-v6
    type: ingress
    protocol: udp
    from: 49152
    to: 65535
    cidr: ::/0
  - name: external-media-shared-ports-1-tcp-v6
    type: ingress
    protocol: tcp
    from: 5004
    to: 5004
    cidr: ::/0
  - name: external-media-shared-ports-1-udp-v6
    type: ingress
    protocol: udp
    from: 5004
    to: 5004
    cidr: ::/0
  - name: external-media-shared-ports-3-tcp-v6
    type: ingress
    protocol: tcp
    from: 9000
    to: 9000
    cidr: ::/0
  - name: external-media-shared-ports-3-udp-v6
    type: ingress
    protocol: udp
    from: 9000
    to: 9000
    cidr: ::/0
  domain: prod.infra.webex.com
defaults:
  import_defaults: [../../../../manifest.yaml]
