version: 1
command_and_control: mccprod
clusters:
- name: wmelgen-p-1
  env_name: wmel01-ocp4-prod
  status: online
  worker_count: 0
  ingress_count: 3
  ingress_int_count: 2
  optimized_storage_count: 0
  bastion_count: 1
  master_count: 3
  terraform_version: v1.5.4
  provisioning_module_version: v5.12.5
  module_version: v10.13.9
  infractl_version: v7.18.12
  bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  base_image: wbx3-jammy-1.27.12-containerd-v2-25-3-2
  base_k8s_image: wbx3-jammy-1.27.12-containerd-v2-25-3-2
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: container_manager='containerd'
  cidr_node_prefix: 26
  cidr_pods_prefix: 18
  cidr_svcs_prefix: 22
  health_checks: true
  server_group_policies: [soft-anti-affinity]
  metadata:
    cluster_type: generic
    platform_release_channel: stable-1
    annotations:
      helm3Only: true
    deployment_groups:
      - agglog-infra-wmel
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/base-apps.yaml
  - platform/external-dns.yaml
  - platform/falco.yaml
  internal_network: wmel01-ocp4-mw
  external_network: public-419
  management_network: wbx3-provider-418
  internal_provider_network: wbx3-provider-418
  internal_provider_subnets: [************/23]
  worker_eth1_network: wbx3-provider-418
  volume_storage: true
  gateway_name: wmel01-ocp4-mw
  node_pools:
  - name: worker
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 12
    max_size: 12
    metadata:
      node_labels: pool-name=worker,type=worker
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  domain: prod.infra.webex.com
defaults:
  import_defaults: [../../../manifest.yaml]
