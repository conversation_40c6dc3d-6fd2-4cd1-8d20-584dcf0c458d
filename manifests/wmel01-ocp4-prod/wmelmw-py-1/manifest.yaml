version: 1
command_and_control: mccprod
clusters:
- name: wmelmw-py-1
  status: online
  env_name: wmel01-ocp4-prod
  worker_count: 0
  ingress_count: 3
  ingress_int_count: 2
  bastion_count: 1
  optimized_storage_count: 0
  worker_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  master_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  base_image: wbx3-jammy-1.27.12-containerd-v2-25-2-0
  base_k8s_image: wbx3-jammy-1.27.12-containerd-v2-25-2-0
  cloud_service_provider: openstack
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: kube_network_node_prefix=26 kubelet_max_pods=56 container_manager='containerd'
  cidr_node_prefix: 26
  cidr_pods_prefix: 18
  cidr_svcs_prefix: 22
  health_checks: true
  terraform_version: v1.5.4
  module_version: v10.13.9
  infractl_version: v7.18.12
  provisioning_module_version: v5.12.5
  metadata:
    cluster_type: webapps-prod
    platform_release_channel: stable-1
    annotations:
      helm3Only: true
    deployment_groups:
        - mw-prod-wmel01-dc
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/base-apps.yaml
  - platform/external-dns.yaml
  - platform/falco.yaml
  - webapps/init.yaml
  server_group_policies: [soft-anti-affinity]
  internal_network: wmel01-ocp4-mw
  external_network: public-419
  management_network: wbx3-provider-418
  internal_provider_network: wbx3-provider-418
  internal_provider_subnets: [************/23]
  worker_eth1_network: wbx3-provider-418
  volume_storage: true
  gateway_name: wmel01-ocp4-mw
  node_pools:
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.64mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - name: mw-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 72
    max_size: 72
    metadata:
      node_labels: pool-name=mw-p0,type=worker
    image_name: wbx3-jammy-1.27.12-containerd-v2-25-3-2
  - name: wss-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 4
    max_size: 4
    metadata:
      node_labels: pool-name=wss-p0,type=worker
      node_taints: dedicated=nbrwss:NoSchedule
    image_name: wbx3-jammy-1.27.12-containerd-v2-25-3-2
  domain: prod.infra.webex.com
defaults:
  import_defaults: [../../../manifest.yaml]
