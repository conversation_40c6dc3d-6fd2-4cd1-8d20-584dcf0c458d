version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  - name: sg-01a-ha1-pst-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: sg-01a-ha1
    module_version: v10.7.10
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: p3_mtg1
      destination_vpc_name: p5_mtg1
      source_aws_infra_region: ap-southeast-1
      destination_aws_infra_region: ap-southeast-1
      name: sg-01a-ha1-pst
      destination_route_table_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*workload_iso*'
      source_subnet_filter:
      - '*workload_iso*'
  - name: sg-01a-ha1-ha2-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: sg-01a-ha1
    module_version: v10.7.10
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: p3_mtg1
      destination_vpc_name: p4_mtg1
      source_aws_infra_region: ap-southeast-1
      destination_aws_infra_region: ap-southeast-1
      name: sg-01a-ha1-ha2
      destination_route_table_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*workload_iso*'
      source_subnet_filter:
      - '*workload_iso*'
  - name: wxt-sg-01a-ha1-sg
    module_path: modules/aws/wxt/security_groups
    env_name: sg-01a-ha1
    module_version: v10.7.10
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/wxt/sg
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: p3_mtg1
      source_aws_infra_region: ap-southeast-1
      default_tags: {}
      security_groups:
        wxt-sg-01a-ha1-pst:
          create_new: true
          existing_sg_id: ""
          description: "SG attached on WxT workers to access persistence in sg-01a-pst"
          tags:
            name: "wxt-sg-01a-ha1-pst"
            security_group_type: "kubed"
          ingress_rules:
          - from_port: 0
            to_port: 65535
            protocol: TCP
            cidr_blocks: ["**********/16", "**********/16", "**********/16"]
            security_groups: []
            ipv6_cidr_blocks: []
            prefix_list_ids: []
            self: false
          egress_rules:
          - from_port: 0
            to_port: 65535
            protocol: TCP
            cidr_blocks: ["0.0.0.0/0"]
            security_groups: []
            ipv6_cidr_blocks: []
            prefix_list_ids: []
            self: false
defaults:
  infractl_version: v7.18.19
  backend: s3
  terraform_version: v1.5.7
  use_provider_template: true
  embed_provider_template: true

  terraform_templates:
  - path: providers-commercial.tf.tpl

  include_defaults: true
