version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # DualStack NLB for wxt-general mesh is just used by asin1
  # DualStack NLB for wxt-meet mesh
  - name: wxtm-prod-asin-meet2
    env_name: sg-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-1
      vpc_name: p4_mtg1
      ip_address_type: dualstack
      deployment_group: wxtm-prod-asin-meet2
      mesh_name: wxt-meet
      ingressgateway_name: wxt-meet-ingressgateway
      subnet_type: public-eip
      domain: p4.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0a5855fed7e32d4af
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 8443
              to: 8443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxt-wxid mesh
  - name: wxtci-prod-asin-idaas2
    env_name: sg-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-1
      vpc_name: p4_mtg1
      ip_address_type: dualstack
      deployment_group: wxtci-prod-asin-idaas2
      mesh_name: wxt-wxid
      ingressgateway_name: wxt-wxid-ingressgateway
      subnet_type: public-eip
      domain: p4.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0a5855fed7e32d4af
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 8443
              to: 8443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxc-general mesh
  - name: wxc-gen-pub-nlb2
    env_name: sg-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-1
      vpc_name: p4_mtg1
      ip_address_type: dualstack
      deployment_group: mobius-prod-wxt-sgp
      allowed_clusters:
        - asinwxt-prd-3
      mesh_name: wxc-general
      ingressgateway_name: wxc-general-pub-ingressgateway
      custom_nlb_name: wxc-gen-pub-nlb2
      subnet_type: public-eip
      domain: p4.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0a5855fed7e32d4af
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 8443
              to: 8443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  - name: wxcctl-pub-nlb2
    env_name: sg-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-1
      vpc_name: p4_mtg1
      ip_address_type: dualstack
      deployment_group: mobius-prod-wxt-sgp
      allowed_clusters:
        - asinwxt-prd-3
      mesh_name: wxc-general
      ingressgateway_name: wxcctl-pub-ingressgateway
      subnet_type: public-eip
      domain: p4.prod.infra.webex.com
      custom_nlb_name: wxcctl-prod-asin-pub-nlb2
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0a5855fed7e32d4af
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 8443
              to: 8443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxt-ci mesh
  - name: wxtci-prod-asin-ci2
    env_name: sg-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-1
      vpc_name: p4_mtg1
      ip_address_type: dualstack
      deployment_group: wxtci-prod-asin-ci2
      mesh_name: wxt-ci
      ingressgateway_name: wxt-ci-ingressgateway
      subnet_type: public-eip
      domain: p4.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0a5855fed7e32d4af
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 8443
              to: 8443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
