version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
    - name: asinwxt-prd-3
      env_name: sg-01a-ha2
      infractl_version: v7.18.22
      cluster_api: true
      module_version: v10.7.10
      s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      terraform_version: v1.5.7
      admin_port: 6443
      apiserver_nlb_subnet_type: public
      apiserver_record_public: true
      assign_eip: true
      aws_infra_az: ap-southeast-1a
      aws_infra_region: ap-southeast-1
      base_image: wbx3-capi-focal-1.25.6-containerd-v1.26.6
      bastion_flavor: t3a.small
      bastion_image_name: wbx3-capi-focal-1.25.6-containerd-v1.26.6
      cidr_pods_prefix: 18
      cidr_svcs_prefix: 22
      cloud_service_provider: aws
      dns_credentials_path: secret/data/mccprod/infra/route53/credentials
      domain: p4.prod.infra.webex.com
      eip_pool: ipv4pool-ec2-0a5855fed7e32d4af
      gateway_name: not-used
      gluster_count: 0
      image_name: wbx3-capi-focal-1.25.6-containerd-v1.26.6
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      ingress_count: 1
      ingress_flavor: c5a.2xlarge
      ingress_int_count: 1
      ingress_int_flavor: c5a.2xlarge
      kubernetes_version: v1.27.12
      lb_security_group_nat_gateway_vpc: application
      local_dns_enabled: true
      location: SIN
      managed_by: mccprod06
      master_count: 3
      master_flavor: c6a.2xlarge
      mesh:
        enabled: true
      metadata:
        annotations:
            helm3Only: true
        cluster_type: wxt
        deployment_groups:
            - kubed-prod-wxt
            - anycast-prod
            - wxtci-prod-asin-ci2
            - wxtci-prod-asin-idaas2
            - wxtm-prod-asin-meet2
            - mobius-prod-wxt-sgp
        platform_release_channel: stable-1
      node_pools:
        - name: anycast-proxy-pool
          type: worker
          min_size: 3
          max_size: 10
          flavor: c6a.2xlarge
          cluster_security_groups:
          - name: anycast
          metadata:
            node_labels: type=anycast-proxy-node
            node_taints: dedicated=anycast-proxy-node:NoSchedule
        - availability_zones:
            - ap-southeast-1a
          cluster_security_groups:
            - name: istio_public
          flavor: c6a.4xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ip-1a
          type: worker
        - availability_zones:
            - ap-southeast-1b
          cluster_security_groups:
            - name: istio_public
          flavor: c6a.4xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ip-1b
          type: worker
        - availability_zones:
            - ap-southeast-1c
          cluster_security_groups:
            - name: istio_public
          flavor: c6a.4xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ip-1c
          type: worker
        - cluster_security_groups:
            - name: istio_private
          flavor: c6a.4xlarge
          max_size: 6
          metadata:
            node_labels: dedicated=istio-ingress-ciscoint
            node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
          min_size: 0
          name: istio-ip-cisco
          type: worker
        - availability_zones:
            - ap-southeast-1a
          external_security_groups:
            - name: wxt-sg-01a-ha2-pst
          fallback_flavors:
            - c6a.4xlarge
            - c5a.4xlarge
            - c5.4xlarge
          flavor: c6a.4xlarge
          max_size: 100
          metadata:
            node_labels: type=worker,zone1=ap-southeast-1a,dedicated=prod-asin
            node_taints: dedicated=prod-asin:NoSchedule
          min_size: 4
          name: wxt-pool-1a
          type: worker
        - availability_zones:
            - ap-southeast-1b
          external_security_groups:
            - name: wxt-sg-01a-ha2-pst
          fallback_flavors:
            - c6a.4xlarge
            - c5a.4xlarge
            - c5.4xlarge
          flavor: c6a.4xlarge
          max_size: 100
          metadata:
            node_labels: type=worker,zone2=ap-southeast-1b,dedicated=prod-asin
            node_taints: dedicated=prod-asin:NoSchedule
          min_size: 4
          name: wxt-pool-1b
          type: worker
        - availability_zones:
            - ap-southeast-1c
          external_security_groups:
            - name: wxt-sg-01a-ha2-pst
          fallback_flavors:
            - c6a.4xlarge
            - c5a.4xlarge
            - c5.4xlarge
          flavor: c6a.4xlarge
          max_size: 100
          metadata:
            node_labels: zone3=ap-southeast-1c,dedicated=prod-asin
            node_taints: dedicated=prod-asin:NoSchedule
          min_size: 4
          name: wxt-pool-1c
          type: worker
        - max_size: 100
          metadata:
            node_labels: type=worker
          min_size: 1
          flavor: m5a.2xlarge
          name: worker
          type: worker
        - availability_zones:
            - ap-southeast-1a
          flavor: r5a.2xlarge
          max_size: 10
          metadata:
            node_labels: type=optimized-storage-node
            node_taints: type=optimized-storage-node:NoSchedule
          min_size: 1
          name: opt-storage-1a
          type: worker
        - availability_zones:
            - ap-southeast-1b
          flavor: r5a.2xlarge
          max_size: 10
          metadata:
            node_labels: type=optimized-storage-node
            node_taints: type=optimized-storage-node:NoSchedule
          min_size: 1
          name: opt-storage-1b
          type: worker
        - availability_zones:
            - ap-southeast-1c
          flavor: r5a.2xlarge
          max_size: 10
          metadata:
            node_labels: type=optimized-storage-node
            node_taints: type=optimized-storage-node:NoSchedule
          min_size: 1
          name: opt-storage-1c
          type: worker
      optimized_storage_count: 0
      optimized_storage_flavor: r5a.2xlarge
      pipeline_bundles:
        - platform/cluster-autoscaler.yaml
        - platform/post-provision.yaml
        - platform/aws-cloud-controller.yaml
        - platform/base-apps.yaml
        - platform/kubed-mesh.yaml
        - platform/keda.yaml
        - platform/falco.yaml
        - wxt/wxt-dynamic-config.yaml
        - platform/flagger.yaml
        - platform/synthetic-heart.yaml
        - platform/ctf.yaml
        - platform/flagger-loadtester.yaml
        - platform/split-prometheus.yaml
        - wxt/wxt-init.yaml
      provisioning_extra_args: container_manager='containerd' containerd_instance_storage='true' enable_coredns_resolv_file_forwarding='true'
      release: v1.6.2
      scale_image_name: wbx3-capi-focal-1.25.6-containerd-v1.26.6
      security_groups:
        istio_private:
            rules:
                - cidrs:
                    - 10.0.0.0/8
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - vpc
                  from: 15021
                  name: healthcheck
                  protocol: tcp
                  to: 15021
                  type: ingress
            tags:
                - key: kubernetes.io/cluster/asinwxt-prd-3
                  value: owned
        istio_public:
            rules:
                - cidrs:
                    - 0.0.0.0/0
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - vpc
                  from: 15021
                  name: healthcheck
                  protocol: tcp
                  to: 15021
                  type: ingress
            tags:
                - key: kubernetes.io/cluster/asinwxt-prd-3
                  value: owned
        anycast:
            rules:
                - name: anycast-proxy-tls
                  type: ingress
                  protocol: tcp
                  from: 10511
                  to: 10511
                  cidrs:
                  - 0.0.0.0/0
                - name: anycast-proxy-healthcheck
                  type: ingress
                  protocol: tcp
                  from: 15021
                  to: 15021
                  cidrs:
                  - vpc
            tags:
                - key: kubernetes.io/cluster/asinwxt-prd-3
                  value: owned
      status: online
      vpc_mission_tag_app: meetings
      vpc_routing: true
      worker_count: 2
      worker_flavor: m5a.2xlarge
      zone_type: public
defaults:
    module_version: v10.7.4
    s3_bucket_region: us-east-2
    use_provider_template: true
    embed_provider_template: true
    terraform_version: v1.5.0
    terraform_templates:
        - path: providers-commercial.tf.tpl
    include_defaults: true
    infractl_version: v7.18.19
    https_internal_ips:
        - 10.0.0.0/8
        - ***********/16
        - **********/16
        - **********/12
        - **********/14
        - *************/23
        - ************/24
        - ***********/24
        - ***********/21
        - *************/19
        - *************/21
        - *************/24
        - **********/14
        - **********/16
        - **********/19
        - ************/20
        - ***********/20
        - **********/16
        - ***********/20
        - ***********/20
    mgmt_remote_ips:
        - 10.0.0.0/8
        - ***********/16
        - **********/16
        - **********/12
        - **********/14
        - *************/23
        - ************/24
        - ***********/24
        - ***********/21
        - *************/19
        - *************/21
        - *************/24
        - **********/14
        - **********/16
        - **********/19
        - ************/20
        - ***********/20
        - **********/16
        - ***********/20
        - ***********/20
