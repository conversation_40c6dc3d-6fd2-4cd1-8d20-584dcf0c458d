version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infra:
  - name: apse4-ha2-thousand-eyes
    env_name: apse4-ha2
    infractl_version: v7.18.22
    module_version: v10.13.4
    module_path: modules/aws/eni
    terraform_version: v1.5.7
    args:
      aws_infra_region: ap-southeast-4
      vpc_name: a5_cal2
      eni_pools:
        thousand-eyes-media-prv:
          subnet_tags:
            webex_purpose: trunk-media
            webex_reachability: private-provider
          availability_zones:
          - ap-southeast-4a
          - ap-southeast-4b
          - ap-southeast-4c
          instances_per_az: 1
          is_public: false
          eip:
            enabled: false
          dns_zone: a5.prod.infra.webex.com
          dns_prefix: te-media-prv
          security_group_names:
          - thousand-eyes
        thousand-eyes-access-media-prv:
          subnet_tags:
            webex_purpose: access-trusted-media
            webex_reachability: private-provider
          availability_zones:
          - ap-southeast-4a
          - ap-southeast-4b
          - ap-southeast-4c
          instances_per_az: 1
          is_public: false
          eip:
            enabled: false
          dns_zone: a5.prod.infra.webex.com
          dns_prefix: te-access-prv
          security_group_names:
          - thousand-eyes
          - sse_media
          - mse_media
      security_groups:
        # https://docs.thousandeyes.com/product-documentation/global-vantage-points/enterprise-agents/configuring/firewall-configuration-for-enterprise-agents
        thousand-eyes:
          - name: thousand-eyes-icmp-echo-reply
            type: ingress
            protocol: ICMP
            from: 0
            to: 0
            cidr: 0.0.0.0/0
          - name: thousand-eyes-icmp-echo
            type: ingress
            protocol: ICMP
            from: 8
            to: 8
            cidr: 0.0.0.0/0
          - name: thousand-eyes-icmp-unreachable
            type: ingress
            protocol: ICMP
            from: 3
            to: 3
            cidr: 0.0.0.0/0
          - name: thousand-eyes-icmp-time-exceeded
            type: ingress
            protocol: ICMP
            from: 11
            to: 11
            cidr: 0.0.0.0/0
          - name: thousand-eyes-a2a-tcp
            type: ingress
            protocol: TCP
            from: 49153
            to: 49153
            cidr: 0.0.0.0/0
          - name: thousand-eyes-a2a-udp
            type: ingress
            protocol: UDP
            from: 49153
            to: 49153
            cidr: 0.0.0.0/0
          - name: thousand-eyes-https
            type: ingress
            protocol: TCP
            from: 443
            to: 443
            cidr: 0.0.0.0/0
          - name: allow-all-egress
            type: egress
            protocol: all
            from: 0
            to: 0
            cidr: 0.0.0.0/0
        sse_media: # todo: bring the ENI defs into the same manifest and use yaml anchors to ensure these are up to date
          - cidr: 0.0.0.0/0
            from: 0
            name: all-egress
            protocol: all
            to: 0
            type: egress
          - cidr: 10.0.0.0/8
            from: 5060
            name: sse-sip-internal-udp-1
            protocol: udp
            to: 5060
            type: ingress
          - cidr: **********/12
            from: 5060
            name: sse-sip-internal-udp-2
            protocol: udp
            to: 5060
            type: ingress
          - cidr: 10.0.0.0/8
            from: 5060
            name: sse-sip-internal-tcp-1
            protocol: tcp
            to: 5060
            type: ingress
          - cidr: **********/12
            from: 5060
            name: sse-sip-internal-tcp-2
            protocol: tcp
            to: 5060
            type: ingress
          - cidr: 10.0.0.0/8
            from: 3000
            name: sse-lb-udp
            protocol: udp
            to: 5000
            type: ingress
          - cidr: 10.0.0.0/8
            from: 3000
            name: sse-lb-tcp
            protocol: tcp
            to: 5000
            type: ingress
        mse_media:
          - cidr: 0.0.0.0/0
            from: 0
            name: all-egress
            protocol: all
            to: 0
            type: egress
          - cidr: 10.0.0.0/8
            from: 9443
            name: mse-grpc-tcp-1
            protocol: tcp
            to: 9443
            type: ingress
          - cidr: **********/12
            from: 9443
            name: mse-grpc-tcp-2
            protocol: tcp
            to: 9443
            type: ingress
          - cidr: 10.0.0.0/8
            from: 19560
            name: mse-media-rtp-udp-media-1
            protocol: udp
            to: 65535
            type: ingress
          - cidr: **********/12
            from: 19560
            name: mse-media-rtp-udp-media-2
            protocol: udp
            to: 65535
            type: ingress
defaults:
    enable_credential_lookup: true
    import_defaults:
        - ../../../manifest.yaml
    include_defaults: true
