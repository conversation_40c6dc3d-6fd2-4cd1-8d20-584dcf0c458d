version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
    - name: au-vicrtm1
      env_name: apse4-ha2
      release: v1.5.1
      infractl_version: v7.18.22
      cluster_api: true
      module_version: v10.7.7
      s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      use_provider_template: true
      terraform_version: v1.5.3
      terraform_templates:
        - path: providers.tf.tpl
      admin_port: 6443
      apiserver_record_public: true
      aws_infra_region: ap-southeast-4
      bastion_count: 0
      bastion_flavor: c5.large
      bastion_image_name: wbx3-capi-focal-1.27.12-containerd-4
      cloud_service_provider: aws
      datacenter: ap-southeast-4
      dns_credentials_path: secret/data/mccprod/infra/route53/credentials
      domain: a5.prod.infra.webex.com
      gateway_name: not-used
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      kubernetes_version: v1.27.12
      local_dns_enabled: true
      location: MEL
      managed_by: mccprod06
      master_count: 3
      master_flavor: c6in.2xlarge
      mesh:
        enabled: true
      metadata:
        annotations:
            helm3Only: true
        cluster_type: wxcedge-prod
        deployment_groups:
            - kubed-prod-calling
            - wxc-dhruva-mno-aws-prod-apsoutheast4
            - dhruva-operator-aws-prod-apsoutheast4
            - wxc-edge-mse-au-vicrtm1-prod
            - wxc-edge-sbc-operator-au-vicrtm1-prod
            - wxc-edge-sse-au-vicrtm1-prod
            - wxc-dhruva-aws-prod-apsoutheast4
            - mobius-prod-mel1
        platform_release_channel: stable-2
      node_pools:
        - availability_zones:
            - ap-southeast-4a
          cluster_security_groups:
            - name: istio_public
          flavor: c5a.2xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ingress-pool-4a
          type: worker
        - availability_zones:
            - ap-southeast-4b
          cluster_security_groups:
            - name: istio_public
          flavor: c5.2xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ingress-pool-4b
          type: worker
        - availability_zones:
            - ap-southeast-4c
          cluster_security_groups:
            - name: istio_public
          flavor: c5.2xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 0
          name: istio-ingress-pool-4c
          type: worker
        - cluster_security_groups:
            - name: istio_private
          flavor: c5.xlarge
          max_size: 10
          metadata:
            node_labels: dedicated=istio-ingress-ciscoint
            node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
          min_size: 0
          name: istio-ingress-cisco
          type: worker
        - name: thousand-eyes-a
          min_size: 1
          max_size: 1
          flavor: c5.4xlarge
          availability_zones:
          - ap-southeast-4a
          metadata:
            node_labels: dedicated=thousand-eyes
            node_taints: dedicated=thousand-eyes:NoSchedule
        - name: thousand-eyes-b
          min_size: 1
          max_size: 1
          flavor: c5.4xlarge
          availability_zones:
          - ap-southeast-4b
          metadata:
            node_labels: dedicated=thousand-eyes
            node_taints: dedicated=thousand-eyes:NoSchedule
        - name: thousand-eyes-c
          min_size: 1
          max_size: 1
          flavor: c5.4xlarge
          availability_zones:
          - ap-southeast-4c
          metadata:
            node_labels: dedicated=thousand-eyes
            node_taints: dedicated=thousand-eyes:NoSchedule
        - max_size: 101
          metadata:
            node_labels: type=worker
          min_size: 3
          name: worker
          type: worker
        - flavor: c6in.2xlarge
          max_size: 8
          metadata:
            node_labels: type=optimized-storage-node
            node_taints: type=optimized-storage-node:NoSchedule
          min_size: 0
          name: optimized-storage
          type: worker
        - availability_zones: null
          flavor: c5.2xlarge
          max_size: 16
          metadata:
            dns_prefix: sse-a
            node_labels: pool-name=sip-pool-a,infra.webex.com/dns-prefix=sse-a,type=external-media,infra.webex.com/public-ipv6-addr=2a05-4200-4--5bc-8
            node_taints: type=media-node:NoSchedule
          min_size: 16
          name: sip-pool-a
          type: worker
        - availability_zones:
            - ap-southeast-4a
            - ap-southeast-4b
          flavor: c5.2xlarge
          max_size: 2
          metadata:
            dns_prefix: sse-p
            node_labels: pool-name=sip-peering-pool,infra.webex.com/dns-prefix=sse-p,type=external-media
            node_taints: type=media-node:NoSchedule
          min_size: 2
          name: sip-peering-pool
          type: worker
        - flavor: c5.2xlarge
          max_size: 22
          metadata:
            dns_prefix: mse-a
            node_labels: pool-name=media-pool-a,infra.webex.com/dns-prefix=mse-a,type=external-media
            node_taints: type=media-node:NoSchedule
          min_size: 22
          name: media-pool-a
          type: worker
        - availability_zones:
            - ap-southeast-4a
            - ap-southeast-4b
          flavor: c5.2xlarge
          max_size: 6
          metadata:
            dns_prefix: mse-p
            node_labels: pool-name=media-peering-pool,infra.webex.com/dns-prefix=mse-p,type=external-media
            node_taints: type=media-node:NoSchedule
          min_size: 6
          name: media-peering-pool
          type: worker
        - availability_zones:
            - ap-southeast-4a
          flavor: c5.2xlarge
          max_size: 1
          metadata:
            dns_prefix: wxc-dhruva-proxy
            node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod
            node_taints: type=media-node:NoSchedule
          min_size: 1
          name: wxc-dhruva-proxy-a
          type: worker
        - availability_zones:
            - ap-southeast-4b
          flavor: c5.2xlarge
          max_size: 1
          metadata:
            dns_prefix: wxc-dhruva-proxy
            node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod
            node_taints: type=media-node:NoSchedule
          min_size: 1
          name: wxc-dhruva-proxy-b
          type: worker
        - availability_zones:
            - ap-southeast-4a
            - ap-southeast-4b
            - ap-southeast-4c
          flavor: c5.4xlarge
          max_size: 5
          metadata:
            dns_prefix: wxc-dhruva-antares
            node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=prod
            node_taints: type=media-node:NoSchedule
          min_size: 5
          name: wxc-dhruva-antares
          type: worker
        - availability_zones:
            - ap-southeast-4a
          flavor: c5.2xlarge
          max_size: 1
          metadata:
            dns_prefix: wxc-dhruva-proxy
            node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod-mno
            node_taints: type=media-node:NoSchedule
          min_size: 1
          name: wxc-dhruva-proxy-mno-a
          type: worker
        - availability_zones:
            - ap-southeast-4b
          flavor: c5.2xlarge
          max_size: 1
          metadata:
            dns_prefix: wxc-dhruva-proxy
            node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod-mno
            node_taints: type=media-node:NoSchedule
          min_size: 1
          name: wxc-dhruva-proxy-mno-b
          type: worker
        - availability_zones:
            - ap-southeast-4a
            - ap-southeast-4b
            - ap-southeast-4c
          flavor: c5.4xlarge
          max_size: 5
          metadata:
            dns_prefix: wxc-dhruva-antares
            node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=prod-mno
            node_taints: type=media-node:NoSchedule
          min_size: 5
          name: wxc-dhruva-antares-mno
          type: worker
      pipeline_bundles:
        - platform/post-provision.yaml
        - platform/aws-cloud-controller.yaml
        - platform/base-apps.yaml
      region: ap-southeast-4
      security_groups:
        istio_private:
            rules:
                - cidrs:
                    - 10.0.0.0/8
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - vpc
                  from: 15021
                  name: healthcheck
                  protocol: tcp
                  to: 15021
                  type: ingress
            tags:
                - key: kubernetes.io/cluster/au-vicrtm1
                  value: owned
        istio_public:
            rules:
                - cidrs:
                    - 0.0.0.0/0
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - vpc
                  from: 15021
                  name: healthcheck
                  protocol: tcp
                  to: 15021
                  type: ingress
            tags:
                - key: kubernetes.io/cluster/au-vicrtm1
                  value: owned
      status: online
      vpc_mission_tag_app: calling
      vpc_routing: true
      worker_flavor: c5.2xlarge
      zone_type: public
infra:
    - name: apse4-ha2-pub-trk-a-rsv
      env_name: apse4-ha2
      infractl_version: v7.18.19
      module_version: v10.7.18
      module_path: modules/aws/cidr-reservation
      terraform_version: v1.5.2
      args:
        aws_infra_region: ap-southeast-4
        from_ip: **************
        reservation_type: explicit
        subnet_id: subnet-08b5f7bb15e53e4ad
        to_ip: **************
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    - name: apse4-ha2-pub-trk-b-rsv
      env_name: apse4-ha2
      infractl_version: v7.18.19
      module_version: v10.7.18
      module_path: modules/aws/cidr-reservation
      terraform_version: v1.5.2
      args:
        aws_infra_region: ap-southeast-4
        from_ip: **************
        reservation_type: explicit
        subnet_id: subnet-0453ea12d1b83a3d9
        to_ip: **************
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    - name: apse4-ha2-pub-trk-c-rsv
      env_name: apse4-ha2
      infractl_version: v7.18.19
      module_version: v10.7.18
      module_path: modules/aws/cidr-reservation
      terraform_version: v1.5.2
      args:
        aws_infra_region: ap-southeast-4
        from_ip: **************
        reservation_type: explicit
        subnet_id: subnet-02a8ce69b6e6e5741
        to_ip: **************
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    - name: apse4-ha2-prv-trk-a-rsv
      env_name: apse4-ha2
      infractl_version: v7.18.19
      module_version: v10.7.18
      module_path: modules/aws/cidr-reservation
      terraform_version: v1.5.2
      args:
        aws_infra_region: ap-southeast-4
        from_ip: *************
        reservation_type: explicit
        subnet_id: subnet-0dda7ead3133bf7d5
        to_ip: *************
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    - name: apse4-ha2-prv-trk-b-rsv
      env_name: apse4-ha2
      infractl_version: v7.18.19
      module_version: v10.7.18
      module_path: modules/aws/cidr-reservation
      terraform_version: v1.5.2
      args:
        aws_infra_region: ap-southeast-4
        from_ip: *************
        reservation_type: explicit
        subnet_id: subnet-0e9e61aab07ca699c
        to_ip: *************
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    - name: apse4-ha2-prv-trk-c-rsv
      env_name: apse4-ha2
      infractl_version: v7.18.19
      module_version: v10.7.18
      module_path: modules/aws/cidr-reservation
      terraform_version: v1.5.2
      args:
        aws_infra_region: ap-southeast-4
        from_ip: *************
        reservation_type: explicit
        subnet_id: subnet-0844a62be4873b8f4
        to_ip: *************
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    - name: apse4-wxcedge-ha2
      env_name: apse4-ha2
      infractl_version: v7.18.19
      module_version: v10.10.5
      module_path: modules/aws/eni
      terraform_version: v1.5.2
      args:
        aws_infra_region: ap-southeast-4
        eni_pools:
            wxc-edge-mse-provider-a:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: mse-a$i.media
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 8
                security_group_names:
                    - mse_media
                subnet_tags:
                    webex_purpose: access-trusted-media
                    webex_reachability: private-provider
            wxc-edge-mse-provider-peering:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                dns_prefix: mse-p$i.media
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 3
                security_group_names:
                    - mse_media
                subnet_tags:
                    webex_purpose: access-trusted-media
                    webex_reachability: private-provider
            wxc-edge-mse-public-a:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: mse-a$i.public
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 8
                is_public: true
                security_group_names:
                    - mse_ext
                subnet_tags:
                    webex_purpose: access-media
                    webex_reachability: public-provider
            wxc-edge-mse-public-peering:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                dns_prefix: mse-p$i.public
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 3
                is_public: true
                security_group_names:
                    - mse_ext
                subnet_tags:
                    webex_purpose: access-untrusted-media
                    webex_reachability: private-provider
            wxc-edge-sse-access-provider-a:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: sse-a$i.media
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 6
                security_group_names:
                    - sse_media
                subnet_tags:
                    webex_purpose: access-trusted-media
                    webex_reachability: private-provider
            wxc-edge-sse-access-public-a:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: sse-a$i.public
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 6
                ipv6_nat_record: 2a05:4200:4::5bc:8
                is_public: true
                security_group_names:
                    - sse_ext
                subnet_tags:
                    webex_purpose: access-media
                    webex_reachability: public-provider
            wxc-edge-sse-provider-peering:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                dns_prefix: sse-p$i.media
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 1
                security_group_names:
                    - sse_media
                subnet_tags:
                    webex_purpose: access-trusted-media
                    webex_reachability: private-provider
            wxc-edge-sse-public-peering:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                dns_prefix: sse-p$i.public
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 1
                is_public: true
                security_group_names:
                    - sse_ext
                subnet_tags:
                    webex_purpose: access-untrusted-media
                    webex_reachability: private-provider
        security_groups:
            mse_ext:
                - cidr: 0.0.0.0/0
                  from: 0
                  name: all-egress
                  protocol: all
                  to: 0
                  type: egress
                - cidr: 0.0.0.0/0
                  from: 19560
                  name: mse-media-rtp-udp
                  protocol: udp
                  to: 65535
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 5004
                  name: mse-media-multiplexed
                  protocol: udp
                  to: 5004
                  type: ingress
            mse_media:
                - cidr: 0.0.0.0/0
                  from: 0
                  name: all-egress
                  protocol: all
                  to: 0
                  type: egress
                - cidr: 10.0.0.0/8
                  from: 9443
                  name: mse-grpc-tcp-1
                  protocol: tcp
                  to: 9443
                  type: ingress
                - cidr: **********/12
                  from: 9443
                  name: mse-grpc-tcp-2
                  protocol: tcp
                  to: 9443
                  type: ingress
                - cidr: 10.0.0.0/8
                  from: 19560
                  name: mse-media-rtp-udp-media-1
                  protocol: udp
                  to: 65535
                  type: ingress
                - cidr: **********/12
                  from: 19560
                  name: mse-media-rtp-udp-media-2
                  protocol: udp
                  to: 65535
                  type: ingress
            sse_ext:
                - cidr: 0.0.0.0/0
                  from: 0
                  name: all-egress
                  protocol: all
                  to: 0
                  type: egress
                - cidr: 0.0.0.0/0
                  from: 5061
                  name: sse-sip-internal-tcp-3
                  protocol: tcp
                  to: 5061
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 5062
                  name: sse-sip-internal-tcp-4
                  protocol: tcp
                  to: 5062
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 8934
                  name: sse-sip-internal-tcp-5
                  protocol: tcp
                  to: 8934
                  type: ingress
            sse_media:
                - cidr: 0.0.0.0/0
                  from: 0
                  name: all-egress
                  protocol: all
                  to: 0
                  type: egress
                - cidr: 10.0.0.0/8
                  from: 5060
                  name: sse-sip-internal-udp-1
                  protocol: udp
                  to: 5060
                  type: ingress
                - cidr: **********/12
                  from: 5060
                  name: sse-sip-internal-udp-2
                  protocol: udp
                  to: 5060
                  type: ingress
                - cidr: 10.0.0.0/8
                  from: 5060
                  name: sse-sip-internal-tcp-1
                  protocol: tcp
                  to: 5060
                  type: ingress
                - cidr: **********/12
                  from: 5060
                  name: sse-sip-internal-tcp-2
                  protocol: tcp
                  to: 5060
                  type: ingress
                - cidr: 10.0.0.0/8
                  from: 3000
                  name: sse-lb-udp
                  protocol: udp
                  to: 5000
                  type: ingress
                - cidr: 10.0.0.0/8
                  from: 3000
                  name: sse-lb-tcp
                  protocol: tcp
                  to: 5000
                  type: ingress
        vpc_name: a5_cal2
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    - name: apse4-dhruva-ha2
      env_name: apse4-ha2
      infractl_version: v7.18.22
      module_version: master
      module_commit: 9942427a0a0c4549fc0d03ccc66b10ba6f1ef966
      module_path: modules/aws/eni
      terraform_version: v1.5.3
      args:
        aws_infra_region: ap-southeast-4
        eni_pools:
            wxc-dhruva-antares-mno-provider:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: wxc-dhruva-antares-mno$i.provider
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 2
                security_group_names:
                    - open_egress
                    - antares_media
                subnet_tags:
                    webex_purpose: trunk-media
                    webex_reachability: private-provider
            wxc-dhruva-antares-mno-public:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: wxc-dhruva-antares-mno$i.public
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 2
                is_public: true
                security_group_names:
                    - open_egress
                    - antares_ext
                subnet_tags:
                    webex_purpose: trunk-media
                    webex_reachability: public-provider
            wxc-dhruva-antares-pstn-provider:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: wxc-dhruva-antares-pstn$i.provider
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 2
                security_group_names:
                    - open_egress
                    - antares_media
                subnet_tags:
                    webex_purpose: trunk-media
                    webex_reachability: private-provider
            wxc-dhruva-antares-pstn-public:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: wxc-dhruva-antares-pstn$i.public
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                instances_per_az: 2
                is_public: true
                security_group_names:
                    - open_egress
                    - antares_ext
                subnet_tags:
                    webex_purpose: trunk-media
                    webex_reachability: public-provider
            wxc-dhruva-proxy-mno-0-provider:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: wxc-dhruva-proxy-mno.provider
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                fixed_ips:
                    - ip: *************
                    - ip: *************
                    - ip: *************
                instances_per_az: 1
                security_group_names:
                    - open_egress
                    - dhruva_media
                subnet_tags:
                    webex_purpose: trunk-media
                    webex_reachability: private-provider
            wxc-dhruva-proxy-mno-0-public:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: wxc-dhruva-proxy-mno.public
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                fixed_ips:
                    - ip: **************
                    - ip: **************
                    - ip: **************
                instances_per_az: 1
                is_public: true
                security_group_names:
                    - open_egress
                    - dhruva_ext
                subnet_tags:
                    webex_purpose: trunk-media
                    webex_reachability: public-provider
            wxc-dhruva-proxy-pstn-0-provider:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: wxc-dhruva-proxy-pstn.provider
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                fixed_ips:
                    - ip: *************
                    - ip: *************
                    - ip: *************
                instances_per_az: 1
                security_group_names:
                    - open_egress
                    - dhruva_media
                subnet_tags:
                    webex_purpose: trunk-media
                    webex_reachability: private-provider
            wxc-dhruva-proxy-pstn-0-public:
                availability_zones:
                    - ap-southeast-4a
                    - ap-southeast-4b
                    - ap-southeast-4c
                dns_prefix: wxc-dhruva-proxy-pstn.public
                dns_zone: a5.prod.infra.webex.com
                eip:
                    create: false
                    enabled: false
                fixed_ips:
                    - ip: **************
                    - ip: **************
                    - ip: **************
                instances_per_az: 1
                is_public: true
                security_group_names:
                    - open_egress
                    - dhruva_ext
                subnet_tags:
                    webex_purpose: trunk-media
                    webex_reachability: public-provider
        security_groups:
            antares_ext:
                - cidr: 0.0.0.0/0
                  from: 5060
                  name: wxc-dhruva-antares-udp-1
                  protocol: udp
                  to: 5061
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 19560
                  name: wxc-dhruva-antares-udp-2
                  protocol: udp
                  to: 65535
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 5060
                  name: wxc-dhruva-antares-tcp-2
                  protocol: tcp
                  to: 5061
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 19560
                  name: wxc-dhruva-antares-tcp-2
                  protocol: tcp
                  to: 65535
                  type: ingress
            antares_media:
                - cidr: 0.0.0.0/0
                  from: 5060
                  name: wxc-dhruva-antares-udp-1
                  protocol: udp
                  to: 5061
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 19560
                  name: wxc-dhruva-antares-udp-2
                  protocol: udp
                  to: 65535
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 5060
                  name: wxc-dhruva-antares-tcp-1
                  protocol: tcp
                  to: 5061
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 19560
                  name: wxc-dhruva-antares-tcp-2
                  protocol: tcp
                  to: 65535
                  type: ingress
            dhruva_ext:
                - cidr: 0.0.0.0/0
                  from: 5060
                  name: wxc-dhruva-proxy-udp
                  protocol: udp
                  to: 5060
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 5060
                  name: wxc-dhruva-proxy-tcp
                  protocol: tcp
                  to: 5061
                  type: ingress
            dhruva_media:
                - cidr: 0.0.0.0/0
                  from: 5060
                  name: wxc-dhruva-proxy-udp
                  protocol: udp
                  to: 5060
                  type: ingress
                - cidr: 0.0.0.0/0
                  from: 5060
                  name: wxc-dhruva-proxy-tcp
                  protocol: tcp
                  to: 5061
                  type: ingress
            open_egress:
                - cidr: 0.0.0.0/0
                  from: 0
                  name: all-egress
                  protocol: all
                  to: 0
                  type: egress
        vpc_name: a5_cal2
      dns_credentials_path: secret/data/mccprod/infra/route53/credentials
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
defaults:
    enable_credential_lookup: true
    import_defaults:
        - ../../../manifest.yaml
    include_defaults: true
