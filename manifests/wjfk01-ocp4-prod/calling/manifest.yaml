---
version: 1
command_and_control: mccprod
clusters:
- name: wjfkwxc-p-2
  status: online
  env_name: wjfk01-ocp4-prod
  datacenter: jfk
  service_block: &wjfkwxc-p-2_sb america-b
  region: us
  pipeline_bundles:
    - platform/post-provision-ecr.yaml
    - platform/base-apps.yaml
    - wxcedge/wxcedge-init.yaml
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  health_checks: true
  cidr_node_prefix: 26
  provisioning_extra_args: kubelet_max_pods=56 container_manager='containerd'
  module_version: v7.23.4-custom-routing
  module_commit: a879a5252caf36664627b0a193388367aa94681b
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  node_pools:
  - name: worker-pool
    type: worker
    min_size: 7
    max_size: 7
    root_block_storage: true
  - name: prom-pool
    type: worker
    min_size: 3
    max_size: 3
    root_block_storage: true
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    metadata:
      node_labels: type=optimized-storage-node
  - name: thousand-eyes
    # TE nodes use a subset of external-media security groups
    type: external-media
    flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
    public_network_name: public-3252
    min_size: 1
    max_size: 1
    root_block_storage: true
    metadata:
      node_labels: type=thousand-eyes
      dns_prefix: thousand-eyes
  - type: external-media
    name: sip-pool-a
    min_size: 51
    max_size: 51
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3251
    public_network_name: public-3252
    metadata:
      dns_prefix: sse-a
      node_labels: pool-name=sip-pool-a,type=external-media,infra.webex.com/public-ipv6-addr=2607-fcf0-9000-1--6
    security_groups:
      media: &sip-pool-media-sgs
      - name: sse-sip-internal-udp-1
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: 10.0.0.0/8
      - name: sse-sip-internal-udp-2
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: **********/12
      - name: sse-sip-internal-tcp-1
        type: ingress
        protocol: tcp
        from: 5060
        to: 5060
        cidr: 10.0.0.0/8
      - name: sse-sip-internal-tcp-2
        type: ingress
        protocol: tcp
        from: 5060
        to: 5060
        cidr: **********/12
      ext: &sip-pool-ext-sgs
      - name: sse-sip-internal-tcp-3
        type: ingress
        protocol: tcp
        from: 5061
        to: 5061
        cidr: 0.0.0.0/0
      - name: sse-sip-internal-tcp-4
        type: ingress
        protocol: tcp
        from: 5062
        to: 5062
        cidr: 0.0.0.0/0
      - name: sse-sip-internal-tcp-5
        type: ingress
        protocol: tcp
        from: 8934
        to: 8934
        cidr: 0.0.0.0/0
  - type: external-media
    name: sip-pool-b
    min_size: 34
    max_size: 34
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3251
    public_network_name: CallingInfra
    metadata:
      dns_prefix: sse-b
      node_labels: pool-name=sip-pool-b,type=external-media,infra.webex.com/public-ipv6-addr=2607-fcf0-9000-1--6
    security_groups:
      media: *sip-pool-media-sgs
      ext: *sip-pool-ext-sgs
  - type: external-media
    name: sip-pool-d
    min_size: 34
    max_size: 34
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3212
    public_network_name: provider-3031
    metadata:
      dns_prefix: sse-d
      node_labels: pool-name=sip-pool-d,type=external-media,infra.webex.com/public-ipv6-addr=2607-fcf0-9000-1--6
    security_groups:
      media: *sip-pool-media-sgs
      ext: *sip-pool-ext-sgs
  - type: external-media
    name: sip-pool-e
    min_size: 41
    max_size: 41
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3264
    public_network_name: public-3263
    metadata:
      dns_prefix: sse-e
      node_labels: pool-name=sip-pool-e,type=external-media,infra.webex.com/public-ipv6-addr=2607-fcf0-9000-1--6
    security_groups:
      media: *sip-pool-media-sgs
      ext: *sip-pool-ext-sgs
  - type: external-media
    name: sip-peering-pool
    root_block_storage: true
    media_network_name: provider-3251
    public_network_name: provider-3260
    min_size: 6
    max_size: 6
    custom_routing: true
    routes:
      eth1:
        - "10.0.0.0/8"
      eth2:
        - "***********/20"
        - "************/20"
        - "************/20"
    metadata:
      dns_prefix: sse-p
      node_labels: pool-name=sip-peering-pool,infra.webex.com/dns-prefix=sse-p,type=external-media
    security_groups:
      media: *sip-pool-media-sgs
      ext: *sip-pool-ext-sgs
  - type: external-media
    name: media-pool-a
    min_size: 54
    max_size: 54
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3251
    public_network_name: public-3252
    metadata:
      dns_prefix: mse-a
      node_labels: pool-name=media-pool-a,type=external-media
    security_groups:
      media: &media-pool-media-sgs
      - name: mse-grpc-tcp-1
        type: ingress
        protocol: tcp
        from: 9443
        to: 9443
        cidr: 10.0.0.0/8
      - name: mse-grpc-tcp-2
        type: ingress
        protocol: tcp
        from: 9443
        to: 9443
        cidr: **********/12
      - name: mse-media-rtp-udp-media-1
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 10.0.0.0/8
      - name: mse-media-rtp-udp-media-2
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: **********/12
      ext: &media-pool-ext-sgs
      - name: mse-media-rtp-udp
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      - name: mse-media-multiplexed
        type: ingress
        protocol: udp
        from: 5004
        to: 5004
        cidr: 0.0.0.0/0
  - type: external-media
    name: media-pool-b
    min_size: 45
    max_size: 45
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3251
    public_network_name: CallingInfra
    metadata:
      dns_prefix: mse-b
      node_labels: pool-name=media-pool-b,type=external-media
    security_groups:
      media: *media-pool-media-sgs
      ext: *media-pool-ext-sgs
  - type: external-media
    name: media-pool-d
    min_size: 86
    max_size: 86
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3212
    public_network_name: provider-3031
    metadata:
      dns_prefix: mse-d
      node_labels: pool-name=media-pool-d,type=external-media
    security_groups:
      media: *media-pool-media-sgs
      ext: *media-pool-ext-sgs
  - type: external-media
    name: media-pool-e
    min_size: 55
    max_size: 55
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3264
    public_network_name: public-3263
    metadata:
      dns_prefix: mse-e
      node_labels: pool-name=media-pool-e,type=external-media
    security_groups:
      media: *media-pool-media-sgs
      ext: *media-pool-ext-sgs
  - type: external-media
    name: media-peering-pool
    root_block_storage: true
    media_network_name: provider-3251
    public_network_name: provider-3260
    min_size: 43
    max_size: 43
    custom_routing: true
    routes:
      eth1:
        - "10.0.0.0/8"
      eth2:
        - "***********/20"
        - "************/20"
        - "************/20"
    metadata:
      dns_prefix: mse-p
      node_labels: pool-name=media-peering-pool,infra.webex.com/dns-prefix=mse-p,type=external-media
    security_groups:
      media: *media-pool-media-sgs
      ext: *media-pool-ext-sgs
  - type: external-media
    name: wxc-dhruva-proxy
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed-media.nv.32vcpu.64mem.0ssd.0eph
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod
    security_groups:
      media: &dhruva-proxy-media-sgs
      - name: wxc-dhruva-proxy-udp
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: 0.0.0.0/0
      - name: wxc-dhruva-proxy-tcp
        type: ingress
        protocol: tcp
        from: 5060
        to: 5061
        cidr: 0.0.0.0/0
      ext: &dhruva-proxy-ext-sgs
      - name: wxc-dhruva-proxy-udp
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: 0.0.0.0/0
      - name: wxc-dhruva-proxy-tcp
        type: ingress
        protocol: tcp
        from: 5060
        to: 5061
        cidr: 0.0.0.0/0
  - type: external-media
    name: wxc-dhruva-antares
    min_size: 23
    max_size: 23
    flavor: kubed-antares.nv.16vcpu.16mem.0ssd.0eph
    root_block_storage: true
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    metadata:
      dns_prefix: wxc-dhruva-antares
      node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=prod
    security_groups:
      media: &antares-media-sgs
      - name: wxc-dhruva-antares-udp
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      - name: wxc-dhruva-antares-tcp
        type: ingress
        protocol: tcp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      ext: &antares-ext-sgs
      - name: wxc-dhruva-antares-udp
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      - name: wxc-dhruva-antares-tcp
        type: ingress
        protocol: tcp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
  - type: external-media
    name: wxc-dhruvaproxy-mno
    min_size: 2
    max_size: 2
    flavor: kubed-media.nv.32vcpu.64mem.0ssd.0eph
    root_block_storage: true
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    metadata:
      dns_prefix: wxc-dhruvaproxy-mno
      node_labels: pool-name=wxc-dhruvaproxy-mno,infra.webex.com/dns-prefix=wxc-dhruvaproxy-mno,deployment=prod
    security_groups:
      media: *dhruva-proxy-media-sgs
      ext: *dhruva-proxy-ext-sgs
  - type: external-media
    name: wxc-dhruvantares-mno
    min_size: 3
    max_size: 3
    flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
    root_block_storage: true
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    metadata:
      dns_prefix: wxc-dhruvantares-mno
      node_labels: pool-name=wxc-dhruvantares-mno,infra.webex.com/dns-prefix=wxc-dhruvantares-mno,deployment=prod
    security_groups:
      media: *antares-media-sgs
      ext: *antares-ext-sgs
  - type: external-media
    name: sip-pool-g
    min_size: 1
    max_size: 1
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-3261
    custom_routing: true
    routes:
      eth1:
        - "10.0.0.0/8"
      eth2:
        - "***********/22"
    metadata:
      dns_prefix: sse-g
      node_labels: pool-name=sip-pool-g,type=external-media,infra.webex.com/dns-prefix=sse-g
    security_groups:
      media: *sip-pool-media-sgs
      ext:
      - name: sse-sip-tango-tcp
        type: ingress
        protocol: tcp
        from: 1024
        to: 65535
        cidr: *************/29
      - name: sse-sip-tango-udp
        type: ingress
        protocol: udp
        from: 1024
        to: 65535
        cidr: *************/29
  - type: external-media
    name: media-pool-g
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-3261
    custom_routing: true
    routes:
      eth1:
        - "10.0.0.0/8"
      eth2:
        - "***********/22"
    metadata:
      dns_prefix: mse-g
      node_labels: pool-name=media-pool-g,type=external-media,infra.webex.com/dns-prefix=mse-g
    security_groups:
      media: *media-pool-media-sgs
      ext: *media-pool-ext-sgs
  - type: external-media
    name: wxc-dhr-proxy-1
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed-media.nv.32vcpu.64mem.0ssd.0eph
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    metadata:
      dns_prefix: wxc-dhr-proxy-1
      node_labels: pool-name=wxc-dhr-proxy-1,infra.webex.com/dns-prefix=wxc-dhr-proxy-1,deployment=prod
    security_groups:
      media: *dhruva-proxy-media-sgs
      ext: *dhruva-proxy-ext-sgs
  - name: homer-pool
    type: external-large-media
    media_network_name: provider-3264
    public_network_name: public-3263
    flavor: homer.12vcpu.24mem.0ssd.0eph
    root_block_storage: true
    min_size: 2
    max_size: 2
    metadata:
      node_labels: type=external-large-media
      node_taints: type=media-node:NoSchedule
      dns_prefix: xlm
  - type: external-media
    name: wxc-dhr-proxy-2
    min_size: 2
    max_size: 2
    root_block_storage: true
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    flavor: kubed-media.nv.32vcpu.64mem.0ssd.0eph
    metadata:
      dns_prefix: wxc-dhr-proxy-2
      node_labels: pool-name=wxc-dhr-proxy-2,infra.webex.com/dns-prefix=wxc-dhr-proxy-2,deployment=prod
    security_groups:
      media: *dhruva-proxy-media-sgs
      ext: *dhruva-proxy-ext-sgs
  - type: external-media
    name: wxc-dhr-proxy-3
    min_size: 2
    max_size: 2
    root_block_storage: true
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    flavor: kubed-media.nv.32vcpu.64mem.0ssd.0eph
    metadata:
      dns_prefix: wxc-dhr-proxy-3
      node_labels: pool-name=wxc-dhr-proxy-3,infra.webex.com/dns-prefix=wxc-dhr-proxy-3,deployment=prod
    security_groups:
      media: *dhruva-proxy-media-sgs
      ext: *dhruva-proxy-ext-sgs
  - type: external-media
    name: wxc-dhr-proxy-4
    min_size: 2
    max_size: 2
    root_block_storage: true
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    flavor: kubed-media.nv.32vcpu.64mem.0ssd.0eph
    metadata:
      dns_prefix: wxc-dhr-proxy-4
      node_labels: pool-name=wxc-dhr-proxy-4,infra.webex.com/dns-prefix=wxc-dhr-proxy-4,deployment=prod
    security_groups:
      media: *dhruva-proxy-media-sgs
      ext: *dhruva-proxy-ext-sgs
  - type: external-media
    name: wxc-dhr-proxy-5
    min_size: 2
    max_size: 2
    root_block_storage: true
    media_network_name: provider-3256
    public_network_name: public-floating-3257
    flavor: kubed-media.nv.32vcpu.64mem.0ssd.0eph
    metadata:
      dns_prefix: wxc-dhr-proxy-5
      node_labels: pool-name=wxc-dhr-proxy-5,infra.webex.com/dns-prefix=wxc-dhr-proxy-5,deployment=prod
    security_groups:
      media: *dhruva-proxy-media-sgs
      ext: *dhruva-proxy-ext-sgs
  metadata:
    cluster_type: wxcedge-prod
    platform_release_channel: stable-2
    annotations:
      helm3Only: true
    deployment_groups:
    - kubed-calling-prod
    - wxc-edge-mse-jfk-prod
    - wxc-edge-sse-jfk-prod
    - wxc-edge-sbc-operator-jfk-prod
    - wxc-dhruva-ocp-prod-jfk01
    - wxc-dhruva-mno-ocp-prod-jfk01
    - dhruva-operator-ocp-prod-jfk01
    - wxc-dhruva-1-ocp-prod-jfk01
    - homer-prod-jfk-rtm
    - wxc-dhruva-2-ocp-prod-jfk01
    - wxc-dhruva-3-ocp-prod-jfk01
    - wxc-dhruva-4-ocp-prod-jfk01
    - wxc-dhruva-5-ocp-prod-jfk01
- name: wjfkwxc-c-2
  status: online
  env_name: wjfk01-ocp4-prod
  datacenter: jfk
  region: us
  pipeline_bundles:
    - platform/post-provision-ecr.yaml
    - platform/base-apps.yaml
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  health_checks: true
  cidr_pods_prefix: 19
  cidr_node_prefix: 26
  provisioning_extra_args: kubelet_max_pods=56 container_manager='containerd'
  node_pools:
  - name: worker-pool
    type: worker
    min_size: 6
    max_size: 6
    root_block_storage: true
  - type: external-media
    name: sip-pool-a
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3251
    public_network_name: public-3252
    metadata:
      dns_prefix: sse-a
      node_labels: pool-name=sip-pool-a,type=external-media,infra.webex.com/public-ipv6-addr=2607-fcf0-9000-1--6
    security_groups:
      media:
      - name: sse-sip-internal-udp-1
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: 10.0.0.0/8
      - name: sse-sip-internal-udp-2
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: **********/12
      - name: sse-sip-internal-tcp-1
        type: ingress
        protocol: tcp
        from: 5060
        to: 5060
        cidr: 10.0.0.0/8
      - name: sse-sip-internal-tcp-2
        type: ingress
        protocol: tcp
        from: 5060
        to: 5060
        cidr: **********/12
      ext:
      - name: sse-sip-internal-tcp-3
        type: ingress
        protocol: tcp
        from: 5061
        to: 5061
        cidr: 0.0.0.0/0
      - name: sse-sip-internal-tcp-4
        type: ingress
        protocol: tcp
        from: 5062
        to: 5062
        cidr: 0.0.0.0/0
      - name: sse-sip-internal-tcp-5
        type: ingress
        protocol: tcp
        from: 8934
        to: 8934
        cidr: 0.0.0.0/0
  - type: external-media
    name: media-pool-a
    min_size: 3
    max_size: 3
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-3251
    public_network_name: public-3252
    metadata:
      dns_prefix: mse-a
      node_labels: pool-name=media-pool-a,type=external-media
    security_groups:
      media:
      - name: mse-grpc-tcp-1
        type: ingress
        protocol: tcp
        from: 9443
        to: 9443
        cidr: 10.0.0.0/8
      - name: mse-grpc-tcp-2
        type: ingress
        protocol: tcp
        from: 9443
        to: 9443
        cidr: **********/12
      - name: mse-media-rtp-udp-media-1
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 10.0.0.0/8
      - name: mse-media-rtp-udp-media-2
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: **********/12
      ext:
      - name: mse-media-rtp-udp
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      - name: mse-media-multiplexed
        type: ingress
        protocol: udp
        from: 5004
        to: 5004
        cidr: 0.0.0.0/0
  metadata:
    cluster_type: wxcedge-prod
    platform_release_channel: beta-1
    annotations:
      helm3Only: true
      service_block: *wjfkwxc-p-2_sb
    deployment_groups:
    - wxc-edge-mse-jfk-canary
    - wxc-edge-sse-jfk-canary
    - wxc-edge-sbc-operator-jfk-canary
defaults:
  import_defaults: ["../../../manifest.yaml"]
