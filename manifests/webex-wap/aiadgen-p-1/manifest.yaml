version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
- name: aiadgen-p-1
  status: online
  module_version: wxcnp-38227-eni-patch
  datacenter: us-east-1
  region: us-east-1
  env_name: a-useast-1-wap
  cidr_pods: auto
  cidr_svcs: auto
  master_flavor: m5ad.8xlarge
  worker_count: 0
  ingress_count: 1
  ingress_int_count: 1
  optimized_storage_count: 0
  provisioning_extra_args: container_manager='containerd' istio_enabled='true' enable_coredns_resolv_file_forwarding='true'
  pipeline_bundles:
    - platform/aws-cloud-controller.yaml
    - platform/post-provision-ecr.yaml
    - platform/base-apps.yaml
    - platform/autoscale.yaml
    - platform/kafka-base-apps-v2.yaml
  vpc_routing: true
  create_eip: true
  aws_infra_azs:
    - us-east-1a
    - us-east-1b
    - us-east-1c
  network_names:
    - a-useast-1a-wap
    - a-useast-1b-wap
    - a-useast-1c-wap
  metadata:
    cluster_type: generic
    platform_release_channel: stable-1
    annotations:
      helm3Only: true
    deployment_groups:
    - wap-pinot-prod-aiad
    - wap-udp-prod-aiad
    - wap-tidb-prod-aiad
    - wap-kafka-prod-aiad
    - wap-stmkafka-prod-aiad
    - wap-report-platform-prod-aiad
    - wap-cca-portal-prod-aiad
    - wap-personal-insights-prod-aiad
    - wap-custom-dashboard-prod-aiad
    - wap-advanced-diagnostic-prod-aiad
    - wap-roma-prod-aiad
    - wap-datahub-prod-aiad
    - wap-radar-prod-aiad
    - wap-mats-prod-aiad
    - wap-unified-telemetry-prod-aiad
    - wap-unified-telemetry-sec-prod-aiad
    - wap-activityreportservice-prod-aiad
    - wap-business-analytics-prod-aiad
    - wap-data-warehouse-prod-aiad
    - wap-datasciencehub-prod-aiad
    - wap-empath-prod-aiad
    - wap-oneview-prod-aiad
    - wap-piiencryptionservice-prod-aiad
    - wap-profile-system-prod-aiad
    - wap-reference-app-prod-aiad
    - wap-sdwanapi-prod-aiad
    - wap-trino-int-prod-aiad
    - wap-trino-ext-prod-aiad
    - wap-trino-device-prod-aiad
    - wap-trino-pinot-prod-aiad
    - wap-wapapiservice-prod-aiad
    - wap-waphelperservice-prod-aiad
    - wap-datascience-prod-aiad
    - wap-ragaas-prod-aiad
    - wap-starrocks-prod-aiad
    - anycast-prod
  mesh:
    enabled: true
  node_pools:
    - name: platform-worker
      type: worker
      flavor: m5a.2xlarge
      min_size: 3
      max_size: 30
      metadata:
        node_labels: type=worker
    - name: anycast-proxy-pool
      type: worker
      min_size: 3
      max_size: 10
      flavor: c6a.2xlarge
      security_groups:
        int:
        - name: anycast-proxy-tls
          type: ingress
          protocol: tcp
          from: 10511
          to: 10511
          cidr: 0.0.0.0/0
        - name: anycast-proxy-healthcheck
          type: ingress
          protocol: tcp
          from: 15021
          to: 15021
          cidr: 0.0.0.0/0
      metadata:
        node_labels: type=anycast-proxy-node
        node_taints: dedicated=anycast-proxy-node:NoSchedule
    - name: wap-istio-pub
      type: worker
      flavor: m5a.2xlarge
      min_size: 1
      max_size: 20
      metadata:
        node_labels: dedicated=wap-istio-ingress-pub
        node_taints: dedicated=wap-istio-ingress-pub:NoSchedule
    - name: wap-istio-provider
      type: worker
      flavor: m5a.2xlarge
      min_size: 1
      max_size: 20
      metadata:
        node_labels: dedicated=wap-istio-ingress-provider
        node_taints: dedicated=wap-istio-ingress-provider:NoSchedule
    - name: wap-istio-roma
      type: worker
      flavor: m5a.xlarge
      min_size: 1
      max_size: 3
      metadata:
        node_labels: dedicated=wap-istio-ingress-roma
        node_taints: dedicated=wap-istio-ingress-roma:NoSchedule
    - name: optimized-storage-node
      type: worker
      flavor: r6a.8xlarge
      min_size: 0
      max_size: 3
      metadata:
        node_labels: type=storage-node
        node_taints: type=storage-node:NoSchedule
    - name: split-prom-pool
      type: worker
      flavor: r6a.4xlarge
      min_size: 0
      max_size: 6
      availability_zones:
        - us-east-1b
      metadata:
        node_labels: type=optimized-storage-node
        node_taints: dedicated=split-prometheus:NoSchedule
    - name: sdm-mesh-gateway
      type: worker
      flavor: m5a.2xlarge
      min_size: 0
      max_size: 30
      metadata:
        node_labels: dedicated=sdm-mesh-gateway
        node_taints: dedicated=sdm-mesh-gateway:NoSchedule
      security_groups:
        wap-kafka-provider-sg:
          - name: webex-dc
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: 10.0.0.0/8
          - name: in-cluster
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: **********/14
    - name: wap-micro-services
      type: worker
      flavor: c5.4xlarge
      min_size: 0
      max_size: 120
      metadata:
        node_labels: dedicated=wap-micro-services
        node_taints: dedicated=wap-micro-services:NoSchedule
    - name: wap-micro-service-mem-opt
      type: worker
      flavor: m6a.4xlarge
      min_size: 0
      max_size: 120
      metadata:
        node_labels: dedicated=wap-micro-services-cost-saving
        node_taints: dedicated=wap-micro-services:NoSchedule
    - name: wap-pool-kafka
      type: worker
      flavor: r5.2xlarge
      availability_zones:
        - us-east-1b
      min_size: 0
      max_size: 65
      metadata:
        node_labels: dedicated=wap-kafka
        node_taints: dedicated=wap-kafka:NoSchedule
    - name: wap-pool-trino
      type: worker
      flavor: m5.4xlarge
      min_size: 0
      max_size: 28
      metadata:
        node_labels: dedicated=wap-trino
        node_taints: dedicated=wap-trino:NoSchedule
    - name: wap-pool-trino-1b
      type: worker
      flavor: m5.4xlarge
      availability_zones:
      - us-east-1b
      min_size: 0
      max_size: 28
      metadata:
        node_labels: dedicated=wap-trino-1b
        node_taints: dedicated=wap-trino-1b:NoSchedule
    - name: wap-pool-trino-pinot
      type: worker
      flavor: m6a.4xlarge
      availability_zones:
      - us-east-1b
      min_size: 0
      max_size: 7
      metadata:
        node_labels: dedicated=wap-trino-pinot
        node_taints: dedicated=wap-trino-pinot:NoSchedule
    - name: wap-pinot-server-cf
      type: worker
      flavor: i4i.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 36
      max_size: 36
      suspended_processes:
        - AZRebalance
        - ReplaceUnhealthy
      metadata:
        node_labels: dedicated=wap-pinot-server-cf
        node_taints: dedicated=wap-pinot-server-cf:NoSchedule
    - name: wap-pinot-server-sp
      type: worker
      flavor: i4i.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 83
      max_size: 83
      suspended_processes:
        - AZRebalance
        - ReplaceUnhealthy
      metadata:
        node_labels: dedicated=wap-pinot-server-sp
        node_taints: dedicated=wap-pinot-server-sp:NoSchedule
    - name: wap-pinot-minion-cf
      type: worker
      flavor: i4i.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 3
      suspended_processes:
        - AZRebalance
        - ReplaceUnhealthy
      metadata:
        node_labels: dedicated=wap-pinot-minion-cf
        node_taints: dedicated=wap-pinot-minion-cf:NoSchedule
    - name: wap-pinot-minion-sp
      type: worker
      flavor: i4i.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 1
      max_size: 1
      suspended_processes:
        - AZRebalance
        - ReplaceUnhealthy
      metadata:
        node_labels: dedicated=wap-pinot-minion-sp
        node_taints: dedicated=wap-pinot-minion-sp:NoSchedule
    - name: wap-pinot-cf
      type: worker
      flavor: m5.8xlarge
      availability_zones:
        - us-east-1b
      min_size: 4
      max_size: 4
      metadata:
        node_labels: dedicated=wap-pinot-cf
        node_taints: dedicated=wap-pinot-cf:NoSchedule
      security_groups:
        wap-provider-sg:
          - name: webex-dc
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: 10.0.0.0/8
          - name: in-cluster
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: **********/14
          - name: pc-aoreprod-int-az1
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: **********/18
          - name: pc-aoreprod-int-az2
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: ***********/18
          - name: pc-aoreprod-int-az3
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: ************/18
      extra_enis:
        - device_index: 1
          subnet_tags:
            Tier: provider
          assign_eip: false
          create_eip: false
          security_groups_name: "wap-provider-sg"
          extra_security_groups: []
          mtu: 1500
          routes:
            - 10.0.0.0/8
    - name: wap-pinot-sp
      type: worker
      flavor: m5.8xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 6
      metadata:
        node_labels: dedicated=wap-pinot-sp
        node_taints: dedicated=wap-pinot-sp:NoSchedule
      security_groups:
        wap-provider-sg:
          - name: webex-dc
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: 10.0.0.0/8
          - name: in-cluster
            type: ingress
            protocol: all
            from: 1
            to: 65535
            cidr: **********/14
      extra_enis:
        - device_index: 1
          subnet_tags:
            Tier: provider
          assign_eip: false
          create_eip: false
          security_groups_name: "wap-provider-sg"
          extra_security_groups: []
          mtu: 1500
          routes:
            - 10.0.0.0/8
    - name: wap-pinot-prometheus-02
      type: worker
      flavor: m5.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 0
      max_size: 1
      metadata:
        node_labels: dedicated=wap-pinot-prometheus-prod-02
        node_taints: dedicated=wap-pinot-prometheus-prod-02:NoSchedule
    - name: wap-tidb-videomesh-tikv
      type: worker
      flavor: m5.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 5
      max_size: 5
      suspended_processes:
        - AZRebalance
        - ReplaceUnhealthy
      metadata:
        node_labels: dedicated=wap-tidb-videomesh-tikv
        node_taints: dedicated=wap-tidb-videomesh-tikv:NoSchedule
    - name: wap-tidb-videomesh-pd
      type: worker
      flavor: m5.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 3
      metadata:
        node_labels: dedicated=wap-tidb-videomesh-pd
        node_taints: dedicated=wap-tidb-videomesh-pd:NoSchedule
    - name: wap-tidb-prometheus
      type: worker
      flavor: m5.2xlarge
      availability_zones:
        - us-east-1b
      min_size: 0
      max_size: 1
      metadata:
        node_labels: dedicated=wap-tidb-prometheus
        node_taints: dedicated=wap-tidb-prometheus:NoSchedule
    - name: wap-tidb-prometheus-02
      type: worker
      flavor: m5.2xlarge
      availability_zones:
        - us-east-1b
      min_size: 0
      max_size: 1
      metadata:
        node_labels: dedicated=wap-tidb-prometheus-02
        node_taints: dedicated=wap-tidb-prometheus-02:NoSchedule
    - name: wap-pool-udp
      type: worker
      flavor: m5a.8xlarge
      min_size: 2
      max_size: 2
      metadata:
        node_labels: dedicated=wap-udp
        node_taints: dedicated=wap-udp:NoSchedule
    - name: wap-pool-udp-job
      type: worker
      flavor: m5a.8xlarge
      min_size: 0
      max_size: 38
      metadata:
        node_labels: dedicated=wap-udp-job
        node_taints: dedicated=wap-udp-job:NoSchedule
    - name: wap-pool-udp-job-moderate
      type: worker
      flavor: m5a.8xlarge
      min_size: 0
      max_size: 14
      metadata:
        node_labels: dedicated=wap-udp-job-moderate
        node_taints: dedicated=wap-udp-job-moderate:NoSchedule
    - name: wap-pool-udp-job-minor
      type: worker
      flavor: m5a.8xlarge
      min_size: 0
      max_size: 14
      metadata:
        node_labels: dedicated=wap-udp-job-minor
        node_taints: dedicated=wap-udp-job-minor:NoSchedule
    - name: wap-udp-job-noscale
      type: worker
      flavor: m5a.8xlarge
      volume_size: 500
      min_size: 0
      max_size: 2
      metadata:
        node_labels: dedicated=wap-udp-job-noscale
        node_taints: dedicated=wap-udp-job-noscale:NoSchedule
    - name: wap-pool-udp-gdpr
      type: worker
      flavor: r5a.8xlarge
      volume_size: 500
      min_size: 0
      max_size: 6
      metadata:
        node_labels: dedicated=wap-udp-gdpr
        node_taints: dedicated=wap-udp-gdpr:NoSchedule
    - name: wap-pool-udp-job-storage
      type: worker
      flavor: r5a.8xlarge
      volume_size: 1000
      min_size: 0
      max_size: 67
      metadata:
        node_labels: dedicated=wap-udp-job-storage
        node_taints: dedicated=wap-udp-job-storage:NoSchedule
    - name: wap-udp-ca
      type: worker
      flavor: r5a.8xlarge
      volume_size: 1000
      min_size: 15
      max_size: 15
      metadata:
        node_labels: dedicated=wap-udp-ca
        node_taints: dedicated=wap-udp-ca:NoSchedule
    - name: wap-udp-storage-moderate
      type: worker
      flavor: r6a.8xlarge
      volume_size: 1000
      min_size: 0
      max_size: 3
      metadata:
        node_labels: dedicated=wap-udp-job-storage-moderate
        node_taints: dedicated=wap-udp-job-storage-moderate:NoSchedule
    - name: wap-udp-job-cpu-optimized
      type: worker
      flavor: r6i.8xlarge
      volume_size: 500
      min_size: 0
      max_size: 20
      metadata:
        node_labels: dedicated=wap-udp-job-cpu-optimized
        node_taints: dedicated=wap-udp-job-cpu-optimized:NoSchedule
    - name: wap-udp-maf-wxc
      type: worker
      flavor: m6a.8xlarge
      volume_size: 500
      min_size: 0
      max_size: 3
      metadata:
        node_labels: dedicated=wap-udp-maf-wxc
        node_taints: dedicated=wap-udp-maf-wxc:NoSchedule
    - name: wap-pool-udp-amoro
      type: worker
      flavor: r5a.4xlarge
      min_size: 4
      max_size: 4
      metadata:
        node_labels: dedicated=wap-udp-amoro
        node_taints: dedicated=wap-udp-amoro:NoSchedule
    - name: wap-pool-udp-celeborn
      type: worker
      flavor: t3a.medium
      volume_size: 500
      min_size: 0
      max_size: 9
      metadata:
        node_labels: dedicated=wap-udp-celeborn
        node_taints: dedicated=wap-udp-celeborn:NoSchedule
    - name: wap-udp-roma-jobs
      type: worker
      flavor: m6a.8xlarge
      volume_size: 300
      min_size: 0
      max_size: 6
      metadata:
        node_labels: dedicated=wap-udp-roma-jobs
        node_taints: dedicated=wap-udp-roma-jobs:NoSchedule
    - name: wap-datasciencehub
      type: worker
      flavor: m5.4xlarge
      min_size: 8
      max_size: 12
      metadata:
        node_labels: dedicated=wap-datasciencehub
        node_taints: dedicated=wap-datasciencehub:NoSchedule
    - name: wap-tidb-pd-plus
      type: worker
      flavor: m5.8xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 3
      metadata:
        node_labels: dedicated=wap-tidb-pd-plus
        node_taints: dedicated=wap-tidb-pd-plus:NoSchedule
    - name: wap-tidb-tikv-plus
      type: worker
      flavor: m5.8xlarge
      availability_zones:
        - us-east-1b
      min_size: 9
      max_size: 9
      metadata:
        node_labels: dedicated=wap-tidb-tikv-plus
        node_taints: dedicated=wap-tidb-tikv-plus:NoSchedule
    - name: wap-tidb-pd-plus-02
      type: worker
      flavor: m5.8xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 3
      metadata:
        node_labels: dedicated=wap-tidb-pd-plus-02
        node_taints: dedicated=wap-tidb-pd-plus-02:NoSchedule
    - name: wap-tidb-tikv-plus-02
      type: worker
      flavor: m5.8xlarge
      availability_zones:
        - us-east-1b
      min_size: 12
      max_size: 12
      metadata:
        node_labels: dedicated=wap-tidb-tikv-plus-02
        node_taints: dedicated=wap-tidb-tikv-plus-02:NoSchedule
    - name: wap-pinot-02-cz
      type: worker
      flavor: m5.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-02-cz
        node_taints: dedicated=wap-pinot-02-cz:NoSchedule
    - name: wap-pinot-02-controller
      type: worker
      flavor: m5.8xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 3
      metadata:
        node_labels: dedicated=wap-pinot-02-controller
        node_taints: dedicated=wap-pinot-02-controller:NoSchedule
    - name: wap-tidb-tidb-plus
      type: worker
      flavor: m5.16xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 3
      metadata:
        node_labels: dedicated=wap-tidb-tidb-plus
        node_taints: dedicated=wap-tidb-tidb-plus:NoSchedule
    - name: wap-sr-cn-prod
      type: worker
      flavor: m6a.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 14
      metadata:
        node_labels: dedicated=wap-sr-cn-prod
        node_taints: dedicated=wap-sr-cn-prod:NoSchedule
    - name: wap-sr-fe-prod
      type: worker
      flavor: m6a.4xlarge
      availability_zones:
        - us-east-1b
      min_size: 3
      max_size: 3
      metadata:
        node_labels: dedicated=wap-sr-fe-prod
        node_taints: dedicated=wap-sr-fe-prod:NoSchedule
    - name: wap-udp-pda-us
      type: worker
      flavor: m5a.8xlarge
      min_size: 8
      max_size: 8
      metadata:
        node_labels: dedicated=wap-pda-us
        node_taints: dedicated=wap-pda-us:NoSchedule
defaults:
  import_defaults: ["../../../manifest.yaml"]
