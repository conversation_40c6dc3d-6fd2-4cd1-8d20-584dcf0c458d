---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infra:
- name: afrawxtprod-abomwxp
  module_path: modules/aws/wxt/peering_connection
  env_name: afrawxt-prod
  module_version: v8.2.3
  terraform_version: v1.2.4 # Anything >= v1.0.0 should work
  include_environment: false # including environment and defaults causes a lot of additional variables
  include_defaults: false # to be dropped into the template, which must then be removed.
  dir: infra/pcx
  args:
    source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
    source_vpc_name: afrawxt-prod
    destination_vpc_name: abom-wxp-prod-a
    source_aws_infra_region: eu-central-1
    destination_aws_infra_region: ap-south-1
    security_groups:
    - name: elasticsearch
      inbound:
      - from: 443
        to: 443
        protocol: TCP
        name: elasticsearch
        target_sg_id: sg-08aaace8479d22390
      outbound:
      - from: 0
        to: 65535
        protocol: all
        name: default
        target_sg_id: sg-01552dd91cc9c1f61
defaults:
  import_defaults: ["../../manifest.yaml"]
