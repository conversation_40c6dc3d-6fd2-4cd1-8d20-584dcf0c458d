version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # VPC peering APP to APP
  - name: d71_app2-d7p_pst0
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea2-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d71_app2
      destination_vpc_name: d7p_pst0
      source_aws_infra_region: us-east-2
      destination_aws_infra_region: us-east-2
      name: d71_app2-d7p_pst0
      destination_subnet_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
  # VPC peering APP2 to afrap1
  - name: d71_app2-acmhp1
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea2-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d71_app2
      destination_vpc_name: acmhwxt-prod
      source_aws_infra_region: us-east-2
      destination_aws_infra_region: us-east-2
      name: d71_app2-acmhp1
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP1 to afrap2
  - name: d71_app2-acmhp2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea2-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d71_app2
      destination_vpc_name: acmhwxt-prod-v2
      source_aws_infra_region: us-east-2
      destination_aws_infra_region: us-east-2
      name: d71_app2-acmhp2
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP2 to afras1
  - name: d71_app2-acmhs1
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea2-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d71_app2
      destination_vpc_name: achm-vpc
      source_aws_infra_region: us-east-2
      destination_aws_infra_region: us-east-2
      name: d71_app2-acmhs1
      destination_subnet_filter:
        - "*private*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*private*"
  # VPC peering APP2 to afras2
  - name: d71_app2-acmhs2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea2-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d71_app2
      destination_vpc_name: achm-wxp-prod-a
      source_aws_infra_region: us-east-2
      destination_aws_infra_region: us-east-2
      name: d71_app2-acmhs2
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP1 to afras3
  - name: d71_app2-acmhs3
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea2-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d71_app2
      destination_vpc_name: achm-wxp-prod-b
      source_aws_infra_region: us-east-2
      destination_aws_infra_region: us-east-2
      name: d71_app2-acmhs3
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*cluster*"
  - name: wxt-d71-app2-sg
    module_path: modules/aws/wxt/security_groups
    env_name: a-usea2-p0-0
    module_version: v10.11.13
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/wxt/sg
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d71_app2
      source_aws_infra_region: us-east-2
      default_tags: {}
      security_groups:
        wxt-a02-app2-sg:
          create_new: true
          existing_sg_id: ""
          description: "SG attached on WxT workers to access persistence"
          tags:
            name: "wxt-d71-app2-sg"
            security_group_type: "kubed"
          ingress_rules:
            - from_port: 0
              to_port: 65535
              protocol: TCP
              cidr_blocks:
                - "**********/16"
                - "**********/16"
                - "**********/16"
                - "**********/16"
                - "**********/16"
                - "**********/16"
                - "***********/25"
                - "********/16"
                - "*************/28"
                - "***********/24"
                - "********/16"
                - "************/20"
                - "************/20"
                - "************/20"
                - "**********/20"
                - "************/22"
                - "************/20"
                - "**********/16"
                - "*********/16"
              security_groups: []
              ipv6_cidr_blocks: []
              prefix_list_ids: []
              self: false
          egress_rules:
            - from_port: 0
              to_port: 65535
              protocol: TCP
              cidr_blocks: ["0.0.0.0/0"]
              security_groups: []
              ipv6_cidr_blocks: []
              prefix_list_ids: []
              self: false

defaults:
  infractl_version: v7.18.33
  backend: s3
  terraform_version: v1.5.7
  use_provider_template: true
  embed_provider_template: true

  terraform_templates:
    - path: providers-commercial.tf.tpl

  include_defaults: true
