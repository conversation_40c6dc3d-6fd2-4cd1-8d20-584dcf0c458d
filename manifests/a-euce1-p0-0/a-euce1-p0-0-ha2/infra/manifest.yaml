version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # VPC peering APP to afrap1
  - name: d31_app2-afrap1
    module_path: modules/aws/wxt/peering_connection
    env_name: a-euce1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d31_app2
      destination_vpc_name: afrawxt-prod
      source_aws_infra_region: eu-central-1
      destination_aws_infra_region: eu-central-1
      name: d31_app2-afrap1
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP1 to afrap2
  - name: d31_app2-afrap2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-euce1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d31_app2
      destination_vpc_name: afrawxt-prod-v2
      source_aws_infra_region: eu-central-1
      destination_aws_infra_region: eu-central-1
      name: d31_app2-afrap2
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP1 to afras2
  - name: d31_app2-afras2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-euce1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d31_app2
      destination_vpc_name: afra-wxp-prod-a
      source_aws_infra_region: eu-central-1
      destination_aws_infra_region: eu-central-1
      name: d31_app2-afras2
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP1 to afras3
  - name: d31_app2-afras3
    module_path: modules/aws/wxt/peering_connection
    env_name: a-euce1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d31_app2
      destination_vpc_name: afra-wxp-prod-b
      source_aws_infra_region: eu-central-1
      destination_aws_infra_region: eu-central-1
      name: d31_app2-afras3
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP1 to afras1
  - name: d31_app2-afras1
    module_path: modules/aws/wxt/peering_connection
    env_name: a-euce1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d31_app2
      destination_vpc_name: afra-vpc
      source_aws_infra_region: eu-central-1
      destination_aws_infra_region: eu-central-1
      name: d31_app2-afras1
      destination_subnet_filter:
        - "*private*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*private*"
  # VPC peering APP1 to afras1
  - name: d31_app2-d3p_pst0
    module_path: modules/aws/wxt/peering_connection
    env_name: a-euce1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d31_app2
      destination_vpc_name: d3p_pst0
      source_aws_infra_region: eu-central-1
      destination_aws_infra_region: eu-central-1
      name: d31_app2-d3p_pst0
      destination_subnet_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
  - name: wxt-d31-app2-sg
    module_path: modules/aws/wxt/security_groups
    env_name: a-euce1-p0-0
    module_version: v10.11.13
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/wxt/sg
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d31_app2
      source_aws_infra_region: eu-central-1
      default_tags: {}
      security_groups:
        wxt-a02-app2-sg:
          create_new: true
          existing_sg_id: ""
          description: "SG attached on WxT workers to access persistence"
          tags:
            name: "wxt-d31-app2-sg"
            security_group_type: "kubed"
          ingress_rules:
            - from_port: 0
              to_port: 65535
              protocol: TCP
              cidr_blocks:
                - "**********/16"
                - "**********/16"
                - "**********/16"
                - "**********/16"
                - "************/22"
                - "***********/21"
                - "************/20"
                - "************/20"
                - "************/20"
                - "**********/16"
                - "*************/28"
                - "**********/16"
                - "**********/24"
              security_groups: []
              ipv6_cidr_blocks: []
              prefix_list_ids: []
              self: false
          egress_rules:
            - from_port: 0
              to_port: 65535
              protocol: TCP
              cidr_blocks: ["0.0.0.0/0"]
              security_groups: []
              ipv6_cidr_blocks: []
              prefix_list_ids: []
              self: false

defaults:
  infractl_version: v7.18.33
  backend: s3
  terraform_version: v1.5.7
  use_provider_template: true
  embed_provider_template: true

  terraform_templates:
    - path: providers-commercial.tf.tpl

  include_defaults: true
