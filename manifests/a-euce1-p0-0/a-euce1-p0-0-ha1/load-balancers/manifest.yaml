version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # DualStack NLB of wxt-calling mesh for cluster afrawxt-prd-1
  - name: wxtc-prod-afra-calling1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: wxtc-prod-afra-calling1
      mesh_name: wxt-calling
      ingressgateway_name: wxt-calling-ingressgateway
      custom_nlb_name: wxtc-prod-afra-calling1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-8443:
          protocol: TCP
          port: 8443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 8443
              to: 8443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB of wxt-ci mesh for cluster afrawxt-prd-1
  - name: wxtci-prod-afra-ci1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: wxtci-prod-afra-ci1
      mesh_name: wxt-ci
      ingressgateway_name: wxt-ci-ingressgateway
      custom_nlb_name: wxtci-prod-afra-ci1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxt-general mesh
  - name: wxtgen-prod-afra-general1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: wxtgen-prod-afra-general1
      mesh_name: wxt-general
      ingressgateway_name: wxt-general-ingressgateway
      custom_nlb_name: wxtgen-prod-afra-general1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxt-meet mesh
  - name: wxtm-prod-afra-meet1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: wxtm-prod-afra-meet1
      mesh_name: wxt-meet
      ingressgateway_name: wxt-meet-ingressgateway
      custom_nlb_name: wxtm-prod-afra-meet1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 5061
              to: 5062
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxt-mercury mesh
  - name: wxtmer-prod-afra-mercury1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: wxtmer-prod-afra-mercury1
      mesh_name: wxt-mercury
      ingressgateway_name: wxt-mercury-ingressgateway
      custom_nlb_name: wxtmer-prod-afra-mercury1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxt-message mesh
  - name: wxtmsg-prod-afra-message1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: wxtmsg-prod-afra-message1
      mesh_name: wxt-message
      ingressgateway_name: wxt-message-ingressgateway
      custom_nlb_name: wxtmsg-prod-afra-message1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxt-registration mesh
  - name: wxtreg-prod-afra-regnlb1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: wxtreg-prod-afra-registration1
      mesh_name: wxt-registration
      ingressgateway_name: wxt-registration-ingressgateway
      custom_nlb_name: wxtreg-prod-afra-reg1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for wxt-wxid mesh
  - name: wxtci-prod-afra-idaas1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: wxtci-prod-afra-idaas1
      mesh_name: wxt-wxid
      ingressgateway_name: wxt-wxid-ingressgateway
      custom_nlb_name: wxtci-prod-afra-idaas1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # Static NLB of console-pub-ingressgateway for cluster afrawxt-prd-1
  - name: wxc-general-pub-afra1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: mobius-prod-wxt-eu
      mesh_name: wxc-general
      ingressgateway_name: wxc-general-pub-ingressgateway
      custom_nlb_name: wxc-general-pub-afra1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      allowed_clusters:
        - afrawxt-prd-1
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # Static NLB of console-pub-ingressgateway for cluster afrawxt-prd-1
  - name: wxcctl-pub-afra1
    env_name: afrawxt-prod
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: eu-central-1
      vpc_name: afrawxt-prod
      ip_address_type: dualstack
      deployment_group: mobius-prod-wxt-eu
      mesh_name: wxc-general
      ingressgateway_name: wxcctl-pub-ingressgateway
      custom_nlb_name: wxcctl-pub-afra1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0ac4d3516f9afce14
      allowed_clusters:
        - afrawxt-prd-1
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
