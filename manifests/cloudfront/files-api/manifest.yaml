---
version: 1
command_and_control: mccprod
infra:
- name: cf/files-api/asyd
  module_version: v10.7.4
  infractl_version: v7.19.0
  terraform_version: v1.5.0
  module_path: modules/aws/cloudfront
  env_name: au-01a-ha1
  args:
    create_origin_access_identity: true
    aws_infra_region: "ap-southeast-2"
    aliases: ["files-prod-ap-southeast-2.webexcontent.com"]
    tags:
      ApplicationGroup: wxt-general
      Service: files-api
      Name: files-prod-ap-southeast-2
    origin:
      s3_origin:
        domain_name: files-prod-ap-southeast-2.s3.ap-southeast-2.amazonaws.com
        origin_id: files-prod-ap-southeast-2
    origin_group: {}
    viewer_certificate:
      acm_certificate_arn: "arn:aws:acm:us-east-1:527856644868:certificate/c24d9c51-02bd-498b-aa38-cdd37007e029"
    default_cache_behavior: {
      allowed_methods:  [
        "GET",
        "HEAD",
        "OPTIONS",
        "PUT",
        "POST",
        "PATCH",
        "DELETE"
      ],
      cached_methods: [
        "GET",
        "HEAD",
        "OPTIONS"
      ],
      compress: false,
      query_string: false,
      viewer_protocol_policy: "redirect-to-https",
      lambda_function_association: {
        origin-request: {
          lambda_arn: "arn:aws:lambda:us-east-1:527856644868:function:filesCloudFront:6"
        }
      }
    }
