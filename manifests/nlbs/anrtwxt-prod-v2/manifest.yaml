version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # DualStack NLB for wxt-calling mesh
  - name: wxtc-prod-anrt-calling2
    env_name: anrtwxt-prod-v2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-northeast-1
      vpc_name: anrtwxt-prod-v2
      ip_address_type: dualstack
      deployment_group: wxtc-prod-anrt-calling2
      mesh_name: wxt-calling
      ingressgateway_name: wxt-calling-ingressgateway
      custom_nlb_name: wxtc-prod-anrt-calling2-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-09e959e045c1b1dd4
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-8443:
          protocol: TCP
          port: 8443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-meet mesh
  - name: wxtm-prod-anrt-meet2
    env_name: anrtwxt-prod-v2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-northeast-1
      vpc_name: anrtwxt-prod-v2
      ip_address_type: dualstack
      deployment_group: wxtm-prod-anrt-meet2
      mesh_name: wxt-meet
      ingressgateway_name: wxt-meet-ingressgateway
      custom_nlb_name: wxtm-prod-anrt-meet2-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-09e959e045c1b1dd4
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
