version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
### pst0 to app1
  - name: a5p_pst0-a50_app1-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: a-uswe2-a0-0
    module_version: v10.9.0
    terraform_version: v1.5.3 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: a5p_pst0
      destination_vpc_name: a50_app1
      source_aws_infra_region: us-west-2
      destination_aws_infra_region: us-west-2
      name: a5p_pst0-a50_app1-pcx
      source_subnet_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*prv-direct*'
      destination_route_table_filter:
      - '*prv-direct*'
### pst0 to app2
  - name: a5p_pst0-a51_app2-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: a-uswe2-a0-0
    module_version: v10.9.0
    terraform_version: v1.5.3 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: a5p_pst0
      destination_vpc_name: a51_app2
      source_aws_infra_region: us-west-2
      destination_aws_infra_region: us-west-2
      name: a5p_pst0-a51_app2-pcx
      source_subnet_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*prv-direct*'
      destination_route_table_filter:
      - '*prv-direct*'
### pst0 to a5p_pst0-apdxwxt-alpha-v2
  - name: a5p_pst0-alpha-v2-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: a-uswe2-a0-0
    module_version: v10.9.0
    terraform_version: v1.5.3 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: a5p_pst0
      destination_vpc_name: apdxwxt-alpha-v2
      source_aws_infra_region: us-west-2
      destination_aws_infra_region: us-west-2
      name: a5p_pst0-alpha-v2-pcx
      source_subnet_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*cluster*'
      destination_route_table_filter:
      - '*cluster*'
### pst0 to apdx-vpc
  - name: a5p_pst0-apdx-vpc
    module_path: modules/aws/wxt/peering_connection
    env_name: a-uswe2-a0-0
    module_version: v10.9.0
    terraform_version: v1.5.3 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: a5p_pst0
      destination_vpc_name: apdx-vpc
      source_aws_infra_region: us-west-2
      destination_aws_infra_region: us-west-2
      name: a5p_pst0-apdx-vpc
      source_subnet_filter:
      - "*workload_iso*"
      source_route_table_filter:
      - "*workload_iso*"
      destination_subnet_filter:
      - '*apdx*'
      destination_route_table_filter:
      - '*apdx*'
