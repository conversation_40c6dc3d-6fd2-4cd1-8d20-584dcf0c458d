version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

networks:
    # Name of the netowrk, has to be unique across all the manifests in one "cloud" (e.g. mccint)
  - name: a-uswe2-a0-0-pst
    # Name of the environment this network is part of, has to match the name of the environment
    env_name: a-uswe2-a0-0
    # This isn't used but the validation requires it
    aws_infra_az: us-west-2
    # The version of the module to use, use the latest version available
    module_version: v10.11.13
    include_environment: true
    # The path of the module to use in federated-infra
    module_path: "modules/aws/network_archipelago"

    infractl_version: v7.18.36
    primary_cidr_prefix_length: 16

    # The region of the network
    region: us-west-2
    # A unique 3 letter code of the environment that is used as a DNS anchor.
    # THis value is pulled from elemental cmdb sheet
    environment: 'a5p'
    # The list of availability zones to use
    availability_zones:
      az1:
        availability_zone: us-west-2a
      az2:
        availability_zone: us-west-2b
      az3:
        availability_zone: us-west-2c

    # egress_vpc, ingress_vpc, inspection_vpc are mandatory vpcs for making a network even if you don't want them

    # This VPC handles all the routing outside of the network, including to the internet
    egress_vpc:
      vpc_index: 0
      # If the network is connected to the Webex backbone for routing to the 10.x/8
      # then a NAT range is needed here (should be a /26) otherwise it should be "disabled"
      webx_dc_nat_range: disabled

    # This VPC handles all the routing to the network, including from the DC
    ingress_vpc:
      vpc_index: 0
      # If the network is connected to the Webex backbone in a DC on a Stick setup
      # this range defines the range of publicly routable IPs that are used for ingressing public traffic
      cidr_public_services: disabled
      # If the network is connected to the Webex backbone for routing to the 10.x/8
      # this range defines the range of IPs that are used for ingressing Webex-private traffic from the DC
      cidr_private_services: **************/25
      primary_cld_cidr: ********/16
      secondary_isolated_cidr: **********/16

    # This VPC is reserved for inspection firewall use cases
    inspection_vpc:
      vpc_index: 0
      enabled: false
      cidr_prv_direct_management: disabled
      aws_gateway_asn: **********

    ##################################
    ### Application VPC Configurations
    ##################################
    media_vpcs:
      # Ignore WxC
      calling:
        enabled: false
        vpc_index: 0
        aws_gateway_asn: **********
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled

      # this is the persistence vpc
      persistence:
        role: pst
        enabled: true
        vpc_index: 0
        aws_gateway_asn: **********
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled

        subnets:
          isolated:
            # Region unique, routable only within VPC
            ipv4_cidr: **********/16
            routes:
              0.0.0.0/0:
                nat_gateway_subnet: public-eip
                nat_gateway_type: public
              10.0.0.0/8:
                nat_gateway_subnet: private
                nat_gateway_type: private
          cloud:
            # Region unique, routable within the environment
            ipv4_cidr: ********/16
          public-eip:
            # Has a internet gateway by default, add a public NAT gateway
            nat_public: true
          private:
            # The CIDR block for the private (10.x) range of the VPC
            # Only define this if the VPC is connected to the Webex backbone
            # This range needs to be unique across all of Webex. If you don't know what to use, ask your nearest Kubed member
            ipv4_cidr: ************/25
            # Has Webex DC 10.x/8 reachability, add a private NAT gateway
            nat_private: true

    ################################
    # If enabled, this will create a peering connection between the Meetings and Calling VPCs
    enable_peering_for_calling_meetings: false
    # This is the toggle to enable connection to the Webex backbone
    enable_dx_association: true
    core_tgw_amazon_asn: 64512
    # This the VGW ID when peering the Meetings/Calling VPC to the DC in a DC on a Stick setup
    vgw_dx_gateway_id: 64e00fc2-168d-4980-8ad7-afe454fb8156
    # This is the TGW ID when peering the egress/ingress VPCs to the DC
    tgw_dx_gateway_id: 8e6dc27e-d92d-4f41-a622-ae08f2767963
    # This is the account ID when peering the egress/ingress VPCs to the DC
    dx_amazon_owner_account_id: ************
    # This should always be webex.com in commercial
    root_domain: webex.com
    # This enables the use of prv.webex.com in this network environment, requires a 10.x peering
    # Also requires SNOW tickets to be created so infoblox can be updated
    private_root_domain: disabled
    # This is the root domain that will be used for this network
    # Use int.infra.webex.com for integration environments
    public_root_domain: prod.infra.webex.com
    # This should be set to true if the public_root_domain hosted zone already exists in Route53
    absorb_public_root_domain: true
    webex_any_cast_ip: ["************"]
    public_ipv4_pool: ipv4pool-ec2-002c70c81368c701c

defaults:
    infractl_version: v7.18.36
    backend: s3
    terraform_version: v1.5.7
    use_provider_template: true
    embed_provider_template: true

    terraform_templates:
      - path: providers-commercial.tf.tpl

    include_defaults: true
