---
version: 1
command_and_control: mccprod
infra:
  - name: c2p-pp-cond-node-role
    module_path: modules/aws/iam_roles
    env_name: c2p-pre-prod
    aws_infra_region: us-east-2
    args:
      create_instance_profile: true
      assume_role_policy_statements:
        - Sid: ""
          Effect: Allow
          Principal:
            Service: ec2.amazonaws.com
          Action: sts:AssumeRole
      tags:
        CcpAccountId: 32bc6b5d-84a0-4df6-9838-fb9a16d4cffb
        CcpOrganizationId: 9edd5e35-1f5a-4fbc-978d-8f250f3501b0
        DataClassification: Cisco Highly Confidential
        Environment: Prod
        ApplicationName: Cisco Cloud Platform
        ResourceOwner: markushn
        CiscoMailAlias: <EMAIL>
        DataTaxonomy: Administrative Data + Customer Data + Entrusted Data + Telemetry Data
        IntendedPublic: True
      iam_policies:
        - Id: capi-node-role
          Version: "2012-10-17"
          Statement:
            - Action:
                - ec2:AssignIpv6Addresses
                - ec2:DescribeInstances
                - ec2:DescribeRegions
                - ec2:CreateTags
                - ec2:DescribeTags
                - ec2:DescribeNetworkInterfaces
                - ec2:DescribeInstanceTypes
                - ec2:DetachNetworkInterface
                - ec2:DescribeNetworkInterfaces
                - ec2:AttachNetworkInterface
                - ec2:UnassignPrivateIpAddresses
                - ec2:DescribeSubnets
                - ecr:GetAuthorizationToken
                - ecr:BatchCheckLayerAvailability
                - ecr:GetDownloadUrlForLayer
                - ecr:GetRepositoryPolicy
                - ecr:DescribeRepositories
                - ecr:ListImages
                - ecr:BatchGetImage
                - ecr:DescribeImages
                - ecr:ListTagsForResource
                - elasticloadbalancing:*
              Resource:
                - "*"
              Effect: Allow
              Condition: {}
            - Action:
                - secretsmanager:DeleteSecret
                - secretsmanager:GetSecretValue
              Resource:
                - arn:*:secretsmanager:*:*:secret:aws.cluster.x-k8s.io/*
              Effect: Allow
              Condition: {}
            - Action:
                - ssm:UpdateInstanceInformation
                - ssmmessages:CreateControlChannel
                - ssmmessages:CreateDataChannel
                - ssmmessages:OpenControlChannel
                - ssmmessages:OpenDataChannel
                - s3:GetEncryptionConfiguration
              Resource:
                - "*"
              Effect: Allow
              Condition: {}
            - Action:
                - sts:AssumeRole
              Effect: Allow
              Resource:
                - "*"
              Condition: {}
        - Id: cluster-autoscaler
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - autoscaling:DescribeAutoScalingGroups
                - autoscaling:DescribeAutoScalingInstances
                - autoscaling:DescribeLaunchConfigurations
                - autoscaling:DescribeScalingActivities
                - autoscaling:DescribeTags
                - ec2:DescribeInstanceTypes
                - ec2:DescribeLaunchTemplateVersions
                - autoscaling:SetDesiredCapacity
                - autoscaling:TerminateInstanceInAutoScalingGroup
                - ec2:DescribeImages
                - ec2:GetInstanceTypesFromInstanceRequirements
                - eks:DescribeNodegroup
              Resource:
                - "*"
              Condition: {}
        - Id: capi-efs
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - elasticfilesystem:DescribeAccessPoints
                - elasticfilesystem:DescribeFileSystems
                - elasticfilesystem:DescribeMountTargets
                - ec2:DescribeAvailabilityZones
              Resource:
                - "*"
              Condition: {}
            - Effect: Allow
              Action:
                - elasticfilesystem:CreateAccessPoint
              Resource:
                - "*"
              Condition:
                StringLike:
                  aws:RequestTag/efs.csi.aws.com/cluster:
                    - "true"
            - Effect: Allow
              Action:
                - elasticfilesystem:TagResource
              Resource:
                - "*"
              Condition:
                StringLike:
                  aws:ResourceTag/efs.csi.aws.com/cluster:
                    - "true"
            - Effect: Allow
              Action:
                - elasticfilesystem:DeleteAccessPoint
              Resource:
                - "*"
              Condition:
                StringEquals:
                  aws:ResourceTag/efs.csi.aws.com/cluster:
                    - "true"
        - Id: ebs-csi
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - ec2:DescribeAvailabilityZones
                - ec2:DescribeInstances
                - ec2:DescribeSnapshots
                - ec2:DescribeTags
                - ec2:DescribeVolumes
                - ec2:DescribeVolumesModifications
              Resource:
                - "*"
              Condition: {}
            - Effect: Allow
              Action:
                - ec2:CreateSnapshot
                - ec2:ModifyVolume
              Resource:
                - arn:aws:ec2:*:*:volume/*
              Condition: {}
            - Effect: Allow
              Action:
                - ec2:AttachVolume
                - ec2:DetachVolume
              Resource:
                - arn:aws:ec2:*:*:volume/*
                - arn:aws:ec2:*:*:instance/*
              Condition: {}
            - Effect: Allow
              Action:
                - ec2:CreateVolume
                - ec2:EnableFastSnapshotRestores
              Resource:
                - arn:aws:ec2:*:*:snapshot/*
              Condition: {}
            - Effect: Allow
              Action:
                - ec2:CreateTags
              Resource:
                - arn:aws:ec2:*:*:volume/*
                - arn:aws:ec2:*:*:snapshot/*
              Condition:
                StringEquals:
                  ec2:CreateAction:
                    - CreateVolume
                    - CreateSnapshot
            - Effect: Allow
              Action:
                - ec2:DeleteTags
              Resource:
                - arn:aws:ec2:*:*:volume/*
                - arn:aws:ec2:*:*:snapshot/*
              Condition: {}
            - Effect: Allow
              Action:
                - ec2:CreateVolume
              Resource:
                - arn:aws:ec2:*:*:volume/*
              Condition:
                StringLike:
                  aws:RequestTag/ebs.csi.aws.com/cluster:
                    - "true"
            - Effect: Allow
              Action:
                - ec2:CreateVolume
              Resource:
                - arn:aws:ec2:*:*:volume/*
              Condition:
                StringLike:
                  aws:RequestTag/CSIVolumeName:
                    - "*"
            - Effect: Allow
              Action:
                - ec2:DeleteVolume
              Resource:
                - arn:aws:ec2:*:*:volume/*
              Condition:
                StringLike:
                  ec2:ResourceTag/ebs.csi.aws.com/cluster:
                    - "true"
            - Effect: Allow
              Action:
                - ec2:DeleteVolume
              Resource:
                - arn:aws:ec2:*:*:volume/*
              Condition:
                StringLike:
                  ec2:ResourceTag/kubernetes.io/created-for/pvc/name:
                    - "*"
            - Effect: Allow
              Action:
                - ec2:CreateSnapshot
              Resource:
                - arn:aws:ec2:*:*:snapshot/*
              Condition:
                StringLike:
                  aws:RequestTag/CSIVolumeSnapshotName:
                    - "*"
            - Effect: Allow
              Action:
                - ec2:CreateSnapshot
              Resource:
                - arn:aws:ec2:*:*:snapshot/*
              Condition:
                StringLike:
                  aws:RequestTag/ebs.csi.aws.com/cluster:
                    - "true"
            - Effect: Allow
              Action:
                - ec2:DeleteSnapshot
              Resource:
                - arn:aws:ec2:*:*:snapshot/*
              Condition:
                StringLike:
                  ec2:ResourceTag/CSIVolumeSnapshotName:
                    - "*"
            - Effect: Allow
              Action:
                - ec2:DeleteSnapshot
              Resource:
                - arn:aws:ec2:*:*:snapshot/*
              Condition:
                StringLike:
                  ec2:ResourceTag/ebs.csi.aws.com/cluster:
                    - "true"
        - Id: ecr-full-access
          Version: "2012-10-17"
          Statement:
            - Action:
                - ecr:*
              Effect: Allow
              Resource:
                - "*"
              Condition: {}
environments:
  - name: c2p-pre-prod
    module_version: v10.16.14
    domain: c30.prod.infra.webex.com
    s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    infra_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
    dns_credentials_path: secret/data/mccprod/infra/route53/credentials
    peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
    cloud_service_provider: aws
    backend: s3
    enable_credential_lookup: true
    s3_bucket_region: us-east-2
defaults:
  import_defaults:
    - ../../../manifest.yaml
