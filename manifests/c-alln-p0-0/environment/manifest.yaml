version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
  - name: c-alln-p0-0
    module_version: v10.8.1
    blueprint_repo: null
    backend: s3
    terraform_version: v1.5.4
    infractl_version: v7.18.19
    enable_availability_zone_hints: false
    base_image: wbx3-focal-1.25.6-containerd-2f1cc0c
    base_k8s_image: wbx3-focal-1.25.6-containerd-2f1cc0c
    bastion_block_storage: true
    dns_credentials_path: secret/data/mccprod/infra/route53/credentials
    domain: prod.infra.webex.com
    gateway_flavor: gv.4vcpu.8mem.0ssd.0eph
    gateway_name: runon-capi
    infra_credentials_path: secret/data/mccprod/infra/runon-prod/openstack
    ingress_block_storage: true
    ingress_int_block_storage: true
    location: DFW
    master_block_storage: true
    image_flavor: 8vCPUx16GB
    master_flavor: 8vCPUx16GB
    worker_flavor: 16vCPUx32GB
    bastion_flavor: 2vCPUx4GB
    ingress_flavor: 8vCPUx16GB
    ingress_int_flavor: 8vCPUx16GB
    provisioning_module_version: v5.11.8
    server_group_policies:
      - soft-anti-affinity
    volume_storage: true
    worker_block_storage: true
    cloud_service_provider: runon
defaults:
  blueprint_repo: null
  include_defaults: true
  include_environment: true
  use_provider_template: true
  embed_provider_template: false
  enable_credential_lookup: true
  terraform_templates:
    - path: providers.tf.tpl
  import_defaults:
    - ../../../manifest.yaml
