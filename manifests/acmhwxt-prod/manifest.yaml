---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
defaults:
  import_defaults: ["../../manifest.yaml"]
infra:
- name: acmhwxtprod-achmwxpproda
  module_path: modules/aws/wxt/peering_connection
  env_name: acmhwxt-prod
  module_version: v10.2.3
  terraform_version: v1.5.0 # Anything >= v1.0.0 should work
  include_environment: false # including environment and defaults causes a lot of additional variables
  include_defaults: false # to be dropped into the template, which must then be removed.
  dir: infra/pcx
  args:
    source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
    source_vpc_name: acmhwxt-prod
    destination_vpc_name: achm-wxp-prod-a
    source_aws_infra_region: us-east-2
    destination_aws_infra_region: us-east-2
    name: acmhwxtprod-achmwxpproda
    destination_route_table_filter:
    - '*cluster*'
    source_route_table_filter:
    - '*cluster*'
    source_subnet_filter:
    - cluster*
    security_groups:
    - name: milvusdb
      inbound:
      - from: 19530
        to: 19530
        protocol: TCP
        name: cassandra
        target_sg_id: sg-07f522ab33db00b1e
      outbound:
      - from: 0
        to: 65535
        protocol: TCP
        name: default
        target_sg_id: sg-09af4763289a13984
