---
version: 1
command_and_control: mccprod
infra:
- name: s3/ccg/ccg-prod
  module_version: v10.8.0
  infractl_version: v10.16.19
  terraform_version: v1.5.0
  module_path: modules/aws/s3
  env_name: sg-01a-ha1
  args:
    bucket: calling-compliance-gateway-prod
    aws_infra_region: us-east-2
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    tags:
      DataTaxonomy: Cisco Operations Data
      DataClassification: Cisco Highly Confidential
      supportgroup: wx-teams-platform
      Environment: PROD
      ApplicationName: Webex Teams
      CiscoMailAlias: <EMAIL>
      ResourceOwner: Cisco Cloud Engineering
      webexservice: telephony-gateway
    server_side_encryption_configuration:
        rule:
          apply_server_side_encryption_by_default:
            sse_algorithm: AES256
    lifecycle_rule:
      - enabled: true
        expiration:
          days: 30
        noncurrent_version_expiration:
          days: 30
- name: s3/ccg/ccg-prod-eu
  module_version: v10.8.0
  infractl_version: v10.16.19
  terraform_version: v1.5.0
  module_path: modules/aws/s3
  env_name: sg-01a-ha1
  args:
    bucket: calling-compliance-gateway-prod-eu
    aws_infra_region: eu-central-1
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    tags:
      DataTaxonomy: Cisco Operations Data
      DataClassification: Cisco Highly Confidential
      supportgroup: wx-teams-platform
      Environment: PROD
      ApplicationName: Webex Teams
      CiscoMailAlias: <EMAIL>
      ResourceOwner: Cisco Cloud Engineering
      webexservice: calling-compliance-gateway
    server_side_encryption_configuration:
      rule:
        apply_server_side_encryption_by_default:
          sse_algorithm: AES256
    lifecycle_rule:
      - enabled: true
        expiration:
          days: 30
        noncurrent_version_expiration:
          days: 30
