---
version: 1
command_and_control: mccprod
infra:
  - name: s3/aibridge/ats/aiad
    infractl_version: v7.18.14
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-ats-aiad
      aws_infra_region: us-west-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: ATS
  - name: s3/aibridge/bts/jfk01
    infractl_version: v7.18.14
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-bts-jfk01
      aws_infra_region: us-west-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: BTS
  - name: s3/aibridge/prod/jfk02
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-jfk02
      aws_infra_region: us-west-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/iad02
    infractl_version: v7.18.14
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-iad02
      aws_infra_region: us-east-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/dfw02
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-dfw02
      aws_infra_region: us-west-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/dfw03
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-dfw03
      aws_infra_region: us-west-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/sin01
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-sin01
      aws_infra_region: ap-southeast-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/nrt03
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-nrt03
      aws_infra_region: ap-northeast-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/syd01
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-syd01
      aws_infra_region: ap-southeast-2
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/fra01
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-fra01
      aws_infra_region: eu-central-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/lhr03
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-lhr03
      aws_infra_region: eu-west-2
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/bom01
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-bom01
      aws_infra_region: ap-south-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/yyz02
    infractl_version: v7.18.2
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-yyz02
      aws_infra_region: ca-central-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
  - name: s3/aibridge/prod/jed01
    infractl_version: v7.18.14
    module_path: modules/aws/s3
    env_name: aibridge-bucket-env
    args:
      bucket: aibridge-prod-jed01
      aws_infra_region: me-central-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: customer data
        supportgroup: webapps
        servertype: aibridge
        Environment: PROD
environments:
  - name: aibridge-bucket-env
    domain: prod.infra.webex.com
    module_version: v10.8.0
    infractl_version: v7.18.14
    terraform_version: v1.5.0
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    dns_credentials_path: secret/data/mccprod/infra/route53/credentials
defaults:
  import_defaults:
    - "../../../manifest.yaml"
