---
version: 1
command_and_control: mccprod
infra:
- name: s3/file-brackets/adxb
  module_version: v10.8.0
  infractl_version: v7.18.14
  terraform_version: v1.5.0
  module_path: modules/aws/s3
  env_name: sg-01a-ha1
  args:
    bucket: file-brackets-prod-adxb
    aws_infra_region: me-central-1
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    tags:
      DataTaxonomy: Cisco Operations Data
      DataClassification: Cisco Highly Confidential
      supportgroup: wx-teams-platform
      Environment: PROD
      ApplicationName: Webex Teams
      CiscoMailAlias: <EMAIL>
      ResourceOwner: Cisco Cloud Engineering
      webexservice: file-brackets
    server_side_encryption_configuration:
        rule:
          apply_server_side_encryption_by_default:
            sse_algorithm: AES256
- name: s3/file-brackets/asyd
  module_version: v10.7.4
  infractl_version: v7.19.0
  terraform_version: v1.5.0
  module_path: modules/aws/s3
  env_name: au-01a-ha1
  args:
    bucket: file-brackets-prod-asyd
    aws_infra_region: ap-southeast-2
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    tags:
      DataTaxonomy: Cisco Operations Data
      DataClassification: Cisco Highly Confidential
      supportgroup: wx-teams-platform
      Environment: PROD
      ApplicationName: Webex Teams
      CiscoMailAlias: <EMAIL>
      ResourceOwner: Cisco Cloud Engineering
      webexservice: file-brackets
    server_side_encryption_configuration:
        rule:
          apply_server_side_encryption_by_default:
            sse_algorithm: AES256
- name: file-brackets-user-asyd
  module_version: v10.7.4
  infractl_version: v7.19.0
  terraform_version: v1.5.0
  module_path: modules/aws/wxt/iam
  env_name: au-01a-ha1
  include_environment: true
  include_defaults: true
  dir: infra/iam
  args:
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    aws_infra_region: ap-southeast-2
    vault_path: secret/mccprod/infra/file-brackets-user-asyd/credentials
    iam_user_name: file-brackets-user-asyd
    iam_policies:
      file-brackets-policy-asyd:
        Version: '2012-10-17'
        Statement:
        - Effect: Allow
          Action:
          - s3:ListBucketVersions
          - s3:ListBucket
          - s3:GetBucketVersioning
          - s3:PutObject
          - s3:GetObject
          - s3:DeleteObjectVersion
          - s3:GetObjectVersion
          - s3:GetObjectTagging
          - s3:PutObjectTagging
          - s3:AbortMultipartUpload
          - s3:DeleteObject
          - s3:GetBucketLocation
          - s3:GetObjectMetadata
          - s3:GeneratePresignedUrl
          Resource:
          - arn:aws:s3:::file-brackets-prod-asyd
          - arn:aws:s3:::file-brackets-prod-asyd/*
- name: s3/file-brackets/usea1
  module_version: v10.7.4
  infractl_version: v7.19.0
  terraform_version: v1.5.0
  module_path: modules/aws/s3
  env_name: a-usea1-i0-0
  args:
    bucket: file-brackets-s3-usea1
    aws_infra_region: us-east-1
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    tags:
      DataTaxonomy: Cisco Operations Data
      DataClassification: Cisco Highly Confidential
      supportgroup: wx-teams-platform
      Environment: PROD
      ApplicationName: Webex Teams
      CiscoMailAlias: <EMAIL>
      ResourceOwner: Cisco Cloud Engineering
      webexservice: file-brackets
    server_side_encryption_configuration:
        rule:
          apply_server_side_encryption_by_default:
            sse_algorithm: AES256
- name: file-brackets-user-usea1
  module_version: v10.7.4
  infractl_version: v7.19.0
  terraform_version: v1.5.0
  module_path: modules/aws/wxt/iam
  env_name: a-usea1-i0-0
  include_environment: true
  include_defaults: true
  dir: infra/iam
  args:
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    aws_infra_region: us-east-1
    vault_path: secret/mccprod/infra/iam-users
    iam_user_name: file-brackets-user-usea1
    iam_policies:
      file-brackets-policy-usea1:
        Version: '2012-10-17'
        Statement:
        - Effect: Allow
          Action:
          - s3:ListBucketVersions
          - s3:ListBucket
          - s3:GetBucketVersioning
          - s3:PutObject
          - s3:GetObject
          - s3:DeleteObjectVersion
          - s3:GetObjectVersion
          - s3:GetObjectTagging
          - s3:PutObjectTagging
          - s3:AbortMultipartUpload
          - s3:DeleteObject
          - s3:GetBucketLocation
          - s3:GetObjectMetadata
          - s3:GeneratePresignedUrl
          Resource:
          - arn:aws:s3:::file-brackets-s3-usea1
          - arn:aws:s3:::file-brackets-s3-usea1/*
