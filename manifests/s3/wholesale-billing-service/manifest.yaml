---
version: 1
command_and_control: mccprod
infra:
  - name: s3/billing-reports/adxb
    module_version: v10.8.0
    infractl_version: v7.18.14
    terraform_version: v1.5.0
    module_path: modules/aws/s3
    env_name: me-01a-ha1
    args:
      bucket: wxt-bpb-billing-reports-adxb
      aws_infra_region: me-central-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: Cisco Operations Data
        DataClassification: Cisco Highly Confidential
        supportgroup: wx-teams-platform
        Environment: PROD
        ApplicationName: wholesale-billing-service
        CiscoMailAlias: <EMAIL>
        ResourceOwner: Cisco Cloud Engineering
        webexservice: wholesale-billing-service
      server_side_encryption_configuration:
        rule:
          apply_server_side_encryption_by_default:
            sse_algorithm: AES256
  - name: s3/billing-user/adxb
    module_version: v10.8.0
    infractl_version: v7.18.14
    terraform_version: v1.5.0
    module_path: modules/aws/s3
    env_name: me-01a-ha1
    args:
      bucket: wxt-bpb-billing-user-reports-adxb
      aws_infra_region: me-central-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: Cisco Operations Data
        DataClassification: Cisco Highly Confidential
        supportgroup: wx-teams-platform
        Environment: PROD
        ApplicationName: wholesale-billing-service
        CiscoMailAlias: <EMAIL>
        ResourceOwner: Cisco Cloud Engineering
        webexservice: wholesale-billing-service
      server_side_encryption_configuration:
        rule:
          apply_server_side_encryption_by_default:
            sse_algorithm: AES256
defaults:
  import_defaults: ["../../../manifest.yaml"]
