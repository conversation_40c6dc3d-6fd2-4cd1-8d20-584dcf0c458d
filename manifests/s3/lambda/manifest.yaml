---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infra:
  - name: s3/lambda-int
    module_path: modules/aws/s3
    module_version: v10.16.4
    terraform_version: v1.5.4
    infractl_version: v7.20.2
    env_name: s3-lambda
    args:
      bucket: kubed-lambda-int
      aws_infra_region: us-east-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: Administrative Data
        supportgroup: Platform
  - name: s3/lambda-prod
    module_path: modules/aws/s3
    module_version: v10.16.4
    terraform_version: v1.5.4
    infractl_version: v7.20.2
    env_name: s3-lambda
    args:
      bucket: kubed-lambda-prod
      aws_infra_region: us-east-1
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: Administrative Data
        supportgroup: Platform
environments:
  - name: s3-lambda
    domain: prod.infra.webex.com
    module_version: v10.16.4
    terraform_version: v1.5.4
    infractl_version: v7.20.2
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    dns_credentials_path: secret/data/mccprod/infra/route53/credentials
defaults:
  import_defaults: ["../../../manifest.yaml"]
