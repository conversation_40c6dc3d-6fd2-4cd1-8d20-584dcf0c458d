---
version: 1
command_and_control: mccprod
infra:
- name: s3/lexus/mec1
  module_path: modules/aws/s3
  env_name: me-01a-ha1
  args:
    bucket: s3bucket-lexus-adxb
    aws_infra_region: me-central-1
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    tags:
      DataTaxonomy: Cisco Operations Data
      DataClassification: Cisco Highly Confidential
      supportgroup: wx-teams-platform
      Environment: PROD
      ApplicationName: Webex Teams
      CiscoMailAlias: <EMAIL>
      ResourceOwner: Cisco Cloud Engineering
      webexservice: lexus
    server_side_encryption_configuration:
        rule:
          apply_server_side_encryption_by_default:
            sse_algorithm: AES256
    attach_policy: true
    policy: |
      {
        "Version": "2012-10-17",
        "Statement": [
        {
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:*",
            "Resource": [
                "arn:aws:s3:::s3bucket-lexus-adxb",
                "arn:aws:s3:::s3bucket-lexus-adxb/*"
            ],
            "Condition": {
                "StringEquals": {
                    "aws:PrincipalAccount": "************"
                }
            }
        }
        ]
      }
