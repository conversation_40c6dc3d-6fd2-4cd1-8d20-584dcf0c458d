version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # DualStack NLB for wxt-calling mesh
  - name: wxtc-prod-asyd-calling2
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: wxtc-prod-asyd-calling2
      mesh_name: wxt-calling
      ingressgateway_name: wxt-calling-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-8443:
          protocol: TCP
          port: 8443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-ci mesh
  - name: wxtci-prod-asyd-ci2
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: wxtci-prod-asyd-ci2
      mesh_name: wxt-ci
      ingressgateway_name: wxt-ci-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-general mesh
  - name: wxtgen-prod-asyd-general2
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: wxtgen-prod-asyd-general2
      mesh_name: wxt-general
      ingressgateway_name: wxt-general-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-meet mesh
  - name: wxtm-prod-asyd-meet2
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: wxtm-prod-asyd-meet2
      mesh_name: wxt-meet
      ingressgateway_name: wxt-meet-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-mercury mesh
  - name: wxtmer-prod-asyd-mercury2
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: wxtmer-prod-asyd-mercury2
      mesh_name: wxt-mercury
      ingressgateway_name: wxt-mercury-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-message mesh
  - name: wxtmsg-prod-asyd-message2
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: wxtmsg-prod-asyd-message2
      mesh_name: wxt-message
      ingressgateway_name: wxt-message-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-registration mesh
  - name: wxtreg-prod-asyd-regnlb2
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: wxtreg-prod-asyd-registration2
      mesh_name: wxt-registration
      ingressgateway_name: wxt-registration-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-wxid mesh
  - name: wxtci-prod-asyd-idaas2
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: wxtci-prod-asyd-idaas2
      mesh_name: wxt-wxid
      ingressgateway_name: wxt-wxid-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxc-general mesh
  - name: wxc-gen-pub-asyd-nlb
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: mobius-prod-wxt-syd
      mesh_name: wxc-general
      ingressgateway_name: wxc-general-pub-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      allowed_clusters:
        - asydwxt-prd-4
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  - name: wxcctl-devices-pub-asyd
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: mobius-prod-wxt-syd
      mesh_name: wxc-general
      ingressgateway_name: wxcctl-devices-pub-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      allowed_clusters:
        - asydwxt-prd-4
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  - name: wxcctl-pub-asyd-nlb
    env_name: au-01a-ha2
    module_path: modules/aws/nlb
    module_version: v10.16.4
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: ap-southeast-2
      vpc_name: a2_mtg1
      ip_address_type: dualstack
      deployment_group: mobius-prod-wxt-syd
      mesh_name: wxc-general
      ingressgateway_name: wxcctl-pub-ingressgateway
      subnet_type: public-eip
      domain: a2.prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0990b6b6583fbbd89
      allowed_clusters:
        - asydwxt-prd-4
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
