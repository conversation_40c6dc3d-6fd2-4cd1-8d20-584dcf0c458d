---
version: 1
command_and_control: mccprod
clusters:
- name: wjfkmw-ev-1
  env_name: wjfk02-ocp4-prod
  status: online
  worker_count: 0
  ingress_count: 10
  ingress_int_count: 2
  optimized_storage_count: 0
  bastion_count: 1
  master_count: 3
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: container_manager='containerd'
  cidr_node_prefix: 26
  cidr_pods_prefix: 18
  cidr_svcs_prefix: 22
  health_checks: true
  metadata:
    cluster_type: webapps-prod
    platform_release_channel: stable-3
    annotations:
      helm3Only: true
    deployment_groups:
    - mw-prod-ev
  pipeline_bundles: [platform/ecr-deploy.yaml, platform/post-provision.yaml, platform/base-apps.yaml,
    webapps/init.yaml, platform/external-dns.yaml,
    platform/falco.yaml]
  server_group_policies: [soft-anti-affinity]
  external_network: public-431
  management_network: provider-430
  internal_provider_network: provider-430
  internal_provider_subnets: [************/23]
  worker_eth1_network: provider-430
  volume_storage: true
  gateway_name: wjfk02-ocp4-prod
  node_pools:
  - name: mw-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.128mem.0ssd.0eph
    root_block_storage: true
    min_size: 78
    max_size: 78
    metadata:
      node_labels: pool-name=mw-p0,type=worker
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
defaults:
  import_defaults: ["../../../manifest.yaml"]
