version: 1
command_and_control: mccprod
security_groups:
  sse_media: &sse_media
  - name: sse-sip-internal-udp-1
    type: ingress
    protocol: udp
    from: 5060
    to: 5060
    cidr: 10.0.0.0/8
  - name: sse-sip-internal-udp-2
    type: ingress
    protocol: udp
    from: 5060
    to: 5060
    cidr: **********/12
  - name: sse-sip-internal-tcp-1
    type: ingress
    protocol: tcp
    from: 5060
    to: 5060
    cidr: 10.0.0.0/8
  - name: sse-sip-internal-tcp-2
    type: ingress
    protocol: tcp
    from: 5060
    to: 5060
    cidr: **********/12
  - name: sse-lb-udp
    type: ingress
    protocol: udp
    from: 3000
    to: 5000
    cidr: 10.0.0.0/8
  - name: sse-lb-tcp
    type: ingress
    protocol: tcp
    from: 3000
    to: 5000
    cidr: 10.0.0.0/8
  sse_ext: &sse_ext
  - name: sse-sip-internal-tcp-3
    type: ingress
    protocol: tcp
    from: 5061
    to: 5061
    cidr: 0.0.0.0/0
  - name: sse-sip-internal-tcp-4
    type: ingress
    protocol: tcp
    from: 5062
    to: 5062
    cidr: 0.0.0.0/0
  - name: sse-sip-internal-tcp-5
    type: ingress
    protocol: tcp
    from: 8934
    to: 8934
    cidr: 0.0.0.0/0
  mse_media: &mse_media
  - name: mse-grpc-tcp-1
    type: ingress
    protocol: tcp
    from: 9443
    to: 9443
    cidr: 10.0.0.0/8
  - name: mse-grpc-tcp-2
    type: ingress
    protocol: tcp
    from: 9443
    to: 9443
    cidr: **********/12
  - name: mse-media-rtp-udp-media-1
    type: ingress
    protocol: udp
    from: 19560
    to: 65535
    cidr: 10.0.0.0/8
  - name: mse-media-rtp-udp-media-2
    type: ingress
    protocol: udp
    from: 19560
    to: 65535
    cidr: **********/12
  mse_ext: &mse_ext
  - name: mse-media-rtp-udp
    type: ingress
    protocol: udp
    from: 19560
    to: 65535
    cidr: 0.0.0.0/0
  - name: mse-media-multiplexed
    type: ingress
    protocol: udp
    from: 5004
    to: 5004
    cidr: 0.0.0.0/0
  dhruva_media: &dhruva_media
  - name: wxc-dhruva-proxy-udp
    type: ingress
    protocol: udp
    from: 5060
    to: 5060
    cidr: 0.0.0.0/0
  - name: wxc-dhruva-proxy-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5061
    cidr: 0.0.0.0/0
  dhruva_ext: &dhruva_ext
  - name: wxc-dhruva-proxy-udp
    type: ingress
    protocol: udp
    from: 5060
    to: 5060
    cidr: 0.0.0.0/0
  - name: wxc-dhruva-proxy-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5062
    cidr: 0.0.0.0/0
  dhruva_ext_1: &dhruva_ext_1
  - name: wxc-dhruva-proxy-udp
    type: ingress
    protocol: udp
    from: 5060
    to: 5060
    cidr: 0.0.0.0/0
  - name: wxc-dhruva-proxy-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5061
    cidr: 0.0.0.0/0
  dhruva_calling_ext: &dhruva_calling_ext
  - name: wxc-dhruva-proxy-udp
    type: ingress
    protocol: udp
    from: 5060
    to: 5060
    cidr: 0.0.0.0/0
  - name: wxc-dhruva-proxy-emu-sp-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5062
    cidr: 173.39.90.64/27
  - name: wxc-dhruva-proxy-telnyx-chicago-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5062
    cidr: 192.76.120.10/32
  - name: wxc-dhruva-proxy-telnyx-washington-dc-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5062
    cidr: 64.16.250.10/32
  - name: wxc-dhruva-proxy-dt-germany-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5062
    cidr: 9.141.137.195/32
  - name: wxc-dhruva-proxy-dt-west-europe-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5062
    cidr: 9.163.138.173/32
  dhruva_int2_ext: &dhruva_int2_ext
  - name: wxc-dhruva-proxy-int2-udp
    type: ingress
    protocol: udp
    from: 5060
    to: 5060
    cidr: 0.0.0.0/0
  - name: wxc-dhruva-proxy-int2-emu-sp-tcp
    type: ingress
    protocol: tcp
    from: 5060
    to: 5061
    cidr: 173.39.90.64/27
  antares_media: &antares_media
  - name: wxc-dhruva-antares-udp
    type: ingress
    protocol: udp
    from: 19560
    to: 65535
    cidr: 0.0.0.0/0
  - name: wxc-dhruva-antares-tcp
    type: ingress
    protocol: tcp
    from: 19560
    to: 65535
    cidr: 0.0.0.0/0
  antares_ext: &antares_ext
  - name: wxc-dhruva-antares-udp
    type: ingress
    protocol: udp
    from: 19560
    to: 65535
    cidr: 0.0.0.0/0
  - name: wxc-dhruva-antares-tcp
    type: ingress
    protocol: tcp
    from: 19560
    to: 65535
    cidr: 0.0.0.0/0
clusters:
- name: wjfkwxc-int-1
  status: online
  env_name: wjfk02-ocp4-int
  datacenter: jfk
  region: us
  module_version: v7.23.4-custom-routing
  module_commit: 0bb818d130adf6cc23152130a8319b2f35a65513
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/base-apps.yaml
  - wxcedge/wxcedge-init.yaml
  - wxcedge/wxcedge-kafka-mirror-cl.yaml
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  health_checks: true
  cidr_node_prefix: 26
  provisioning_extra_args: kubelet_max_pods=56 container_manager='containerd'
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  metadata:
    cluster_type: wxcedge-int
    annotations:
      helm3Only: true
      kafka_logging_target: int
      kafka_metrics_target: int
    deployment_groups:
    - kubed-prod-calling
    - wxc-edge-sse-jfk-int
    - wxc-edge-mse-jfk-int
    - wxc-edge-sbc-operator-jfk-int
    - wxc-edge-sel-jfk-int
    - wxc-dhruva-ocp-int-jfk02
    - wxc-dhruva-mno-ocp-int-jfk02
    - wxc-dhruva-int2-ocp-int-jfk02
    - wxc-dhruva-int2-1-ocp-int-jfk02
    - wxc-dhruva-perf-ocp-int-jfk02
    - wxm-dhruva-audio-ocp-int-jfk02
    - wxm-dhruva-pstn-psf-int2-ocp-int-jfk02
    - wxm-dhruva-cmr-psf-int2-ocp-int-jfk02
    - wxm-dhruva-pstn-int2-ocp-int-jfk02
    - wxm-dhruva-ccasp-int2-ocp-int-jfk02
    - wxm-dhruva-ccasp-psf-int2-ocp-int-jfk02
    - wxm-dhruva-ccaent-int2-ocp-int-jfk02
    - wxm-dhruva-bofa-int2-ocp-int-jfk02
    - wxm-dhruva-edge-audio-int2-ocp-int-jfk02
    - wxm-dhruva-cmr-int2-ocp-int-jfk02
    - dhruva-operator-ocp-int-jfk02
    - wxc-dhruva-1-ocp-int-jfk02
    - wxc-dhruva-perf-2-ocp-int-jfk02
    - wxc-dhruva-perf-5-ocp-int-jfk02
  node_pools:
  - name: worker-pool
    type: worker
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-449
    media_network_name: provider-446
    min_size: 6
    max_size: 6
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - type: external-media
    name: sip-pool
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-449
    media_network_name: provider-446
    min_size: 10
    max_size: 10
    root_block_storage: true
    metadata:
      dns_prefix: sse
      node_labels: pool-name=sip-pool,infra.webex.com/dns-prefix=sse,type=external-media,infra.webex.com/public-ipv6-addr=2a05-4200-7--5bc-13
    security_groups:
      media: *sse_media
      ext: *sse_ext
  - type: external-media
    name: media-pool
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-449
    media_network_name: provider-446
    min_size: 6
    max_size: 6
    root_block_storage: true
    metadata:
      dns_prefix: mse
      node_labels: pool-name=media-pool,infra.webex.com/dns-prefix=mse,type=external-media
    security_groups:
      media: *mse_media
      ext: *mse_ext
  - type: internal-media
    name: sip-ingress-pool
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-449
    media_network_name: provider-446
    min_size: 4
    max_size: 4
    root_block_storage: true
    metadata:
      node_labels: pool-name=sip-ingress-pool
    security_groups:
      media: *sse_media
      ext: *sse_ext
  - type: internal-media
    name: sip-pool-b
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-449
    media_network_name: provider-446
    min_size: 4
    max_size: 4
    root_block_storage: true
    metadata:
      dns_prefix: sse-b
      node_labels: pool-name=sip-pool-b
    security_groups:
      media: *sse_media
      ext: *sse_ext
  - type: external-media
    name: sip-peering-pool
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: provider-455
    media_network_name: provider-446
    min_size: 1
    max_size: 1
    root_block_storage: true
    custom_routing: true
    routes:
      eth1:
      - 10.0.0.0/8
      eth2:
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
    metadata:
      dns_prefix: sse-p
      node_labels: pool-name=sip-peering-pool,infra.webex.com/dns-prefix=sse-p,type=external-media
    security_groups:
      media: *sse_media
      ext: *sse_ext
  - type: external-media
    name: media-peering-pool
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: provider-455
    media_network_name: provider-446
    min_size: 1
    max_size: 1
    root_block_storage: true
    custom_routing: true
    routes:
      eth1:
      - 10.0.0.0/8
      eth2:
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
      - **************/25
      - ************/25
    metadata:
      dns_prefix: mse-p
      node_labels: pool-name=media-peering-pool,infra.webex.com/dns-prefix=mse-p,type=external-media
    security_groups:
      media: *mse_media
      ext: *mse_ext
  - name: dhruva-proxy-wxc-pstn
    type: external-media
    flavor: kubed.gv.32vcpu.64mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=SI
    security_groups:
      media: *dhruva_media
      ext: *dhruva_calling_ext
  - name: dhruva-proxy-wxc-pstn-2
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=SI-testing
    security_groups:
      media: *dhruva_media
      ext: *dhruva_calling_ext
  - name: dhruva-proxy-wxc-mno
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=SI-mno
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  - name: antares-b2b-wxc-pstn
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 4
    max_size: 4
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-antares
      node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=SI
    security_groups:
      media: *antares_media
      ext: *antares_ext
  - name: antares-b2b-wxm
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 1
    max_size: 1
    root_block_storage: true
    metadata:
      dns_prefix: antares
      node_labels: pool-name=wxm-dhruva-antares,infra.webex.com/dns-prefix=wxm-dhruva-antares,deployment=SI
    security_groups:
      media: *antares_media
      ext: *antares_ext
  - name: antares-b2b-wxc-pstn-2
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 1
    max_size: 1
    root_block_storage: true
    metadata:
      dns_prefix: antares
      node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=SI-testing
    security_groups:
      media: *antares_media
      ext: *antares_ext
  - name: antares-b2b-wxc-mno
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-antares
      node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=SI-mno
    security_groups:
      media: *antares_media
      ext: *antares_ext
  # ATS PSTN - Dhruva proxy
  - name: dhruva-proxy-wxm-pstn
    type: external-media
    flavor: kubed.gv.8vcpu.8mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxm-proxy-pstn
      node_labels: pool-name=wxm-dhruva-proxy-pstn,infra.webex.com/dns-prefix=wxm-proxy-pstn,deployment=SI
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # ATS PSTN- Dhruva antares
  - name: antares-b2b-wxm-pstn
    type: external-media
    flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 3
    max_size: 3
    root_block_storage: true
    metadata:
      dns_prefix: wxm-antares-pstn
      node_labels: pool-name=wxm-dhruva-antares-pstn,infra.webex.com/dns-prefix=wxm-antares-pstn,deployment=SI
    security_groups:
      media: *antares_media
      ext: *antares_ext
  # ATS CCA - Dhruva proxy
  - name: dhruva-proxy-wxm-cca
    type: external-media
    flavor: kubed.gv.8vcpu.8mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 3
    max_size: 3
    root_block_storage: true
    metadata:
      dns_prefix: wxm-proxy-cca
      node_labels: pool-name=wxm-dhruva-proxy-cca,infra.webex.com/dns-prefix=wxm-proxy-cca,deployment=SI
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # ATS Edge audio - Dhruva antares
  - name: antares-b2b-wxm-edge
    type: external-media
    flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxm-antares-edge
      node_labels: pool-name=wxm-dhruva-antares-edge,infra.webex.com/dns-prefix=wxm-antares-edge,deployment=SI
    security_groups:
      media: *antares_media
      ext: *antares_ext
  # ATS CMR - Dhruva proxy
  - name: dhruva-proxy-wxm-cmr
    type: external-media
    flavor: kubed.gv.8vcpu.8mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxm-proxy-cmr
      node_labels: pool-name=wxm-dhruva-proxy-cmr,infra.webex.com/dns-prefix=wxm-proxy-cmr,deployment=SI
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # ATS2 PSTN - Dhruva proxy
  - name: dhruva-proxy-wxm-pstn2
    type: external-media
    flavor: kubed.gv.8vcpu.8mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxm-proxy-pstn
      node_labels: pool-name=wxm-dhruva-proxy-pstn,infra.webex.com/dns-prefix=wxm-proxy-pstn,deployment=ATS2
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # ATS2 PSTN - Dhruva antares
  - name: dhruva-antares-wxm-pstn2
    type: external-media
    flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxm-antares-pstn
      node_labels: pool-name=wxm-dhruva-antares-pstn,infra.webex.com/dns-prefix=wxm-antares-pstn,deployment=ATS2
    security_groups:
      media: *antares_media
      ext: *antares_ext
  # ATS2 Edge audio - Dhruva antares
  - name: dhruva-antares-wxm-edge2
    type: external-media
    flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxm-antares-edge
      node_labels: pool-name=wxm-dhruva-antares-edge,infra.webex.com/dns-prefix=wxm-antares-edge,deployment=ATS2
    security_groups:
      media: *antares_media
      ext: *antares_ext
  # pool 1 PSTN - Dhruva proxy
  - name: wxc-dhr-proxy-int2-1-pstn
    type: external-media
    flavor: kubed.gv.8vcpu.8mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhr-proxy-int2-1,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=int2
    security_groups:
      media: *dhruva_media
      ext: *dhruva_int2_ext
  # BTS PSTN - Dhruva proxy
  - name: bts-dhruva-proxy-wxm-pstn
    type: external-media
    flavor: kubed.gv.8vcpu.8mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxm-proxy-pstn
      node_labels: pool-name=wxm-dhruva-proxy-pstn,infra.webex.com/dns-prefix=wxm-proxy-pstn,deployment=BTS
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # 32vCPU Proxy node for performance testing
  - name: dhruva-proxy-perf-32vcpu
    type: external-media
    flavor: kubed.gv.32vcpu.64mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=perf32
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  - name: dhruva-proxy-wxc-pstn-3
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=SI-Backup
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # BTS Edge audio - Dhruva proxy
  - name: bts-dhruva-proxy-wxm-edge
    type: external-media
    flavor: kubed.gv.8vcpu.8mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 1
    max_size: 1
    root_block_storage: true
    metadata:
      dns_prefix: wxm-proxy-edge
      node_labels: pool-name=wxm-dhruva-proxy-edge,infra.webex.com/dns-prefix=wxm-proxy-edge,deployment=BTS
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
    # Dhruva proxy perf test
  - name: wxc-dhruva-proxy-perf
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=perf
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # Dhruva antares perf test
  - name: wxc-dhr-antares-perf
    type: external-media
    flavor: kubed-antares.nv.16vcpu.16mem.0ssd.0eph
    public_network_name: public-489
    media_network_name: provider-486
    min_size: 13
    max_size: 13
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhr-antares
      node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhr-antares,deployment=perf
    security_groups:
      media: *antares_media
      ext: *antares_ext
  # Dhruva sipp perf test
  - name: dhruva-sipp-perf
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: dhruva-sipp
      node_labels: pool-name=dhruva-sipp,infra.webex.com/dns-prefix=dhruva-sipp,deployment=perf
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # Dhruva kafka perf test
  - name: dhruva-kafka-perf
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 1
    max_size: 1
    root_block_storage: true
    metadata:
      dns_prefix: dhruva-kafka
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=dhruva-kafka,deployment=perf
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext
  # SI-int PSTN - Dhruva proxy
  - name: wxc-dhr-proxy-int2-pstn
    type: external-media
    flavor: kubed.gv.8vcpu.8mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-proxy-pstn
      node_labels: pool-name=wxc-dhr-proxy-int2,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=int2
    security_groups:
      media: *dhruva_media
      ext: *dhruva_int2_ext
  # SI-int PSTN - Dhruva antares
  - name: wxc-dhr-antares-int2-pstn
    type: external-media
    flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-antares-pstn
      node_labels: pool-name=wxc-dhr-antares-int2,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=int2
    security_groups:
      media: *antares_media
      ext: *antares_ext
  - name: wxc-dhr-proxy-1
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-450
    media_network_name: provider-447
    min_size: 2
    max_size: 2
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhr-proxy-1
      node_labels: pool-name=wxc-dhr-proxy-1,infra.webex.com/dns-prefix=wxc-dhr-proxy-1,deployment=SI
    security_groups:
      media: *dhruva_media
      ext: *dhruva_ext_1
  service_block: inactive
defaults:
  import_defaults: ["../../../manifest.yaml"]
