---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
- name: wjfk02-ocp4-int
  gateway_name: wjfk02-ocp4-int
  domain: prod.infra.webex.com
  cloud_service_provider: openstack
  internal_network: wjfk02-ocp4-int
  external_network: public-431
  management_network: provider-430
  worker_eth1_network: provider-430
  internal_provider_network: provider-430
  base_image: wbx3-focal-1.23.5-containerd-v1-23-5
  base_k8s_image: wbx3-focal-1.23.5-containerd-v1-23-5
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  optimized_storage_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  external_media_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  thousand_eyes_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  infra_credentials_path: secret/data/mccprod/infra/wjfk02-ocp4-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  terraform_version: v1.2.4
  provisioning_module_version: v5.9.3
  module_version: v8.4.0
  infractl_version: v7.17.8
  backend: s3
  location: JFK
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  master_count: 3
  worker_count: 1
  ingress_count: 2
  ingress_int_count: 2
  thousand_eyes_node_count: 0
  optimized_storage_count: 0
  bastion_count: 1
  internal_media_node_count: 0
  external_media_node_count: 0
  internal_mini_media_node_count: 0
  gluster_count: 0
  cidr_pods_range: 172.16.0.0/13
  cidr_svcs_range: 172.24.0.0/13
networks:
- name: wjfk02-ocp4-int
  env_name: wjfk02-ocp4-int
gateways:
- name: wjfk02-ocp4-int
  env_name: wjfk02-ocp4-int
  gateway_count: 2
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  gateway_block_storage: true
defaults:
  import_defaults: ["../../manifest.yaml"]
