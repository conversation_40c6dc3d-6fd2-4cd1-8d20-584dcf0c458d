version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  - name: s3/ms-teams-gov/prod-achm
    module_path: modules/aws/s3
    env_name: ms-teams-gov-cf-env
    args:
      aws_infra_region: us-east-1
      bucket: msteams-calling-prod-gov-webexcontent.com
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      tags:
        DataTaxonomy: Cisco Operations Data
        DataClassification: Cisco Highly Confidential
        supportgroup: wx-teams-platform
        Environment: PROD
        ApplicationName: Webex Teams
        CiscoMailAlias: <EMAIL>
        ResourceOwner: Cisco Cloud Engineering
        webexservice: ms-teams
    versioning:
      enabled: true
    server_side_encryption_configuration:
      rule:
        apply_server_side_encryption_by_default:
          sse_algorithm: "AES256"

environments:
  - name: ms-teams-gov-cf-env
    domain: prod.infra.webex.com
    module_version: v10.13.3
    infractl_version: v7.18.43
    terraform_version: v1.5.7
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    dns_credentials_path: secret/data/mccprod/infra/route53/credentials

defaults:
  import_defaults: ["../../../manifest.yaml"]
