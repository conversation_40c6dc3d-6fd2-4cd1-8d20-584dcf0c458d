version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
networks:
- name: w-dfwa1-a0-0-a1
  env_name: w-dfwa1-a0-0
  module_version: v10.13.4
  module_path: modules/openstack/network-new
  infractl_version: v7.18.36
  blueprint_repo: null
  environment: v60
  external_network: pub-direct
  media_networks:
    meetings:
      availability_zones:
        - az1
      enabled: true
      iso_network:
        name: workload_Isolated_net_vlan691
        ipv6_enabled: false
      prv_kubed_network:
        name: private-lb-vip-ats-vlan95
        ipv6_enabled: false
      pub_kubed_network:
        name: public-lb-vip-ats-vlan85
        ipv6_enabled: false
      pub_direct_networks:
        - name: public-412
          ipv6_enabled: false
      pub_nat_network: public-412
      prv_nat_network: provider-413
      enable_prv_nat: true

defaults:
    infractl_version: v7.18.36
    backend: s3
    terraform_version: v1.5.7
    use_provider_template: true
    embed_provider_template: true
    terraform_templates:
      - path: providers-commercial.tf.tpl
    include_defaults: true
