version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
  - name: us-oraccpb2
    env_name: a-uswe2-i3-0
    network_name: a-uswe2-i3-0-ha1
    base_os: noble
    kubernetes_version: v1.31.5
    release: v1.9.1
    module_version: v10.16.18
    cluster_chart_version: 0.13.1
    cluster_api: true
    admin_port: 6443
    apiserver_nlb_subnet_type: public
    assign_eip: true
    apiserver_record_public: true
    aws_infra_region: us-west-2
    aws_infra_az: us-west-2a
    aws_infra_azs:
      - us-west-2a
      - us-west-2b
      - us-west-2c
    bastion_count: 1
    bastion_flavor: t3a.small
    bastion_image_name: wbx3-capi-noble-1.31.5-c8d-amd64-v2.25.5.1-c2p
    cloud_service_provider: aws
    dns_credentials_path: secret/data/mccprod/infra/route53/credentials
    domain: c30.prod.infra.webex.com
    gateway_name: not-used
    infra_credentials_path: secret/data/mccprod/infra/************/atlantis-c2p-pre-prod
    location: PDX
    managed_by: us-txccnc2
    master_count: 5
    master_flavor: c6a.xlarge
    nlb_vpc_type: ingress
    nlb_subnet_purpose: service
    mesh:
      enabled: true
    metadata:
      c2p: true
      c2p_release: 1.1.0-rc
      c2p_cloud: pre-prod
      node_labels: CcpAccountId=32bc6b5d-84a0-4df6-9838-fb9a16d4cffb,CcpOrganizationId=9edd5e35-1f5a-4fbc-978d-8f250f3501b0
      annotations:
        helm3Only: true
      cluster_type: c2p-control-plane
      deployment_groups:
        - kubed-prod-gen
        - ccp-preprod-2
    node_pools:
      - max_size: 15
        metadata:
          node_labels: type=worker,CcpAccountId=32bc6b5d-84a0-4df6-9838-fb9a16d4cffb,CcpOrganizationId=9edd5e35-1f5a-4fbc-978d-8f250f3501b0
        min_size: 3
        name: worker
        type: worker
      - cluster_security_groups:
          - name: istio-ciscoint
        max_size: 3
        metadata:
          node_labels: dedicated=istio-ingress-ciscoint,CcpAccountId=32bc6b5d-84a0-4df6-9838-fb9a16d4cffb,CcpOrganizationId=9edd5e35-1f5a-4fbc-978d-8f250f3501b0
          node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
        min_size: 1
        name: istio-ciscoint
        type: worker
      - cluster_security_groups:
          - name: istio-public
        max_size: 3
        metadata:
          node_labels: dedicated=istio-ingress,CcpAccountId=32bc6b5d-84a0-4df6-9838-fb9a16d4cffb,CcpOrganizationId=9edd5e35-1f5a-4fbc-978d-8f250f3501b0
          node_taints: dedicated=istio-ingress:NoSchedule
        min_size: 1
        name: istio-public
        type: worker
    pipeline_bundles:
      - platform/post-provision.yaml
      - platform/aws-cloud-controller.yaml
      - platform/base-apps.yaml
    security_groups:
      istio-ciscoint:
        rules:
          - cidrs:
              - 10.0.0.0/8
            from: 443
            name: cisco-https
            protocol: tcp
            to: 443
            type: ingress
          - cidrs:
              - vpc
            from: 15021
            name: health
            protocol: tcp
            to: 15021
            type: ingress
        tags:
          - key: kubernetes.io/cluster/us-oraccpb2
            value: owned
      istio-public:
        rules:
          - cidrs:
              - 0.0.0.0/0
            from: 443
            name: https
            protocol: tcp
            to: 443
            type: ingress
          - cidrs:
              - vpc
            from: 15021
            name: health
            protocol: tcp
            to: 15021
            type: ingress
        tags:
          - key: kubernetes.io/cluster/us-oraccpb2
            value: owned
    status: online
    use_oidc: false
    oidc_issuer_url: https://bootstrap.keeper.cisco.com/v1/CiscoCloudPlatform/pre-prod/identity/oidc
    oidc_issuer_namespace: CiscoCloudPlatform/pre-prod
    oidc_client_id: c2p
    oidc_provider_name: not-applicable
    oidc_login_scope: not-applicable
    vpc_mission_tag_app: teams
    worker_flavor: c6a.2xlarge
defaults:
  s3_bucket_region: us-east-2
  include_defaults: true
  import_defaults:
    - ../../../../manifest.yaml
