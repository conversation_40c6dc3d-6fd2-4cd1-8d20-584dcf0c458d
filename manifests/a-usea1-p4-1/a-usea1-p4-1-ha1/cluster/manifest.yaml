version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
    - name: us-vaalog1
      env_name: a-usea1-p4-1
      release: v1.7.2
      cluster_api: true
      terraform_templates:
        - path: providers.tf.tpl
      admin_port: 6443
      apiserver_record_public: true
      aws_infra_region: us-east-1
      bastion_count: 0
      bastion_flavor: c6a.large
      bastion_image_name: wbx3-capi-focal-1.27.12-containerd-2
      cloud_service_provider: aws
      delegate_to_main_account: true
      gateway_name: not-used
      kubernetes_version: v1.31.5
      local_dns_enabled: true
      location: IAD
      managed_by: us-txccnc2
      master_count: 3
      master_flavor: c6a.2xlarge
      mesh:
        enabled: true
      metadata:
        annotations:
            helm3Only: true
        cluster_type: agglog
        deployment_groups:
            - kubed-prod-gen
            - agglog-agg-us
      node_pools:
        - availability_zones:
            - us-east-1a
          cluster_security_groups:
            - name: istio_public
          flavor: c6a.2xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 1
          name: istio-pub-pool-1a
          type: worker
        - availability_zones:
            - us-east-1b
          cluster_security_groups:
            - name: istio_public
          flavor: c6a.2xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 1
          name: istio-pub-pool-1b
          type: worker
        - availability_zones:
            - us-east-1c
          cluster_security_groups:
            - name: istio_public
          flavor: c6a.xlarge
          max_size: 70
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 1
          name: istio-pub-pool-1c
          type: worker
        - cluster_security_groups:
            - name: istio_private
          flavor: c6a.xlarge
          max_size: 6
          metadata:
            node_labels: dedicated=istio-ingress-ciscoint
            node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
          min_size: 0
          name: istio-ingress-cisco
          type: worker
        - availability_zones:
            - us-east-1a
          flavor: c5n.9xlarge
          max_size: 4
          metadata:
            node_labels: type=cilium-egress,infra.webex.com/egress-type=private
            node_taints: dedicated=cilium-egress:NoSchedule
          min_size: 1
          name: egress-prv-1a
          subnet_type: private_media
          type: worker
        - availability_zones:
            - us-east-1b
          flavor: c5n.9xlarge
          max_size: 4
          metadata:
            node_labels: type=cilium-egress,infra.webex.com/egress-type=private
            node_taints: dedicated=cilium-egress:NoSchedule
          min_size: 1
          name: egress-prv-1b
          subnet_type: private_media
          type: worker
        - availability_zones:
            - us-east-1c
          flavor: c5n.9xlarge
          max_size: 4
          metadata:
            node_labels: type=cilium-egress,infra.webex.com/egress-type=private
            node_taints: dedicated=cilium-egress:NoSchedule
          min_size: 1
          name: egress-prv-1c
          subnet_type: private_media
          type: worker
        - max_size: 100
          metadata:
            node_labels: type=worker
          min_size: 1
          name: worker
          type: worker
        - flavor: r6i.2xlarge
          max_size: 5
          metadata:
            node_labels: type=optimized-storage-node
            node_taints: type=optimized-storage-node:NoSchedule
          min_size: 0
          name: optimized-storage
          type: worker
        - flavor: m5.4xlarge
          max_size: 12
          metadata:
            node_labels: pool-name=agglog-general
          min_size: 0
          name: agglog-general
          spot_allocation_strategy: capacity-optimized
          spot_instance_pools: 0
          spot_max_price: ""
          type: worker
          use_spot_instances: true
        - flavor: c5.4xlarge
          max_size: 4
          metadata:
            node_labels: pool-name=agglog-prometheus
          min_size: 0
          name: agglog-prometheus
          spot_allocation_strategy: capacity-optimized
          spot_instance_pools: 0
          spot_max_price: ""
          type: worker
          use_spot_instances: true
        - flavor: c5.4xlarge
          max_size: 200
          metadata:
            node_labels: pool-name=agglog-logstash
          min_size: 0
          name: agglog-logstash
          type: worker
          use_spot_instances: true
        - flavor: m5n.xlarge
          max_size: 3
          metadata:
            node_labels: pool-name=agglog-kafka
          min_size: 0
          name: agglog-kafka
          spot_allocation_strategy: capacity-optimized
          spot_instance_pools: 0
          spot_max_price: ""
          type: worker
          use_spot_instances: true
        - flavor: c5.4xlarge
          max_size: 50
          metadata:
            node_labels: pool-name=agglog-loki
          min_size: 0
          name: agglog-loki
          spot_allocation_strategy: capacity-optimized
          spot_instance_pools: 0
          spot_max_price: ""
          type: worker
          use_spot_instances: true
      pipeline_bundles:
        - platform/post-provision.yaml
        - platform/aws-cloud-controller.yaml
        - platform/base-apps.yaml
      security_groups:
        istio_private:
            rules:
                - cidrs:
                    - 10.0.0.0/8
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - vpc
                  from: 15021
                  name: healthcheck
                  protocol: tcp
                  to: 15021
                  type: ingress
            tags:
                - key: kubernetes.io/cluster/us-vaalog1
                  value: owned
        istio_public:
            rules:
                - cidrs:
                    - 0.0.0.0/0
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - vpc
                  from: 15021
                  name: healthcheck
                  protocol: tcp
                  to: 15021
                  type: ingress
            tags:
                - key: kubernetes.io/cluster/us-vaalog1
                  value: owned
      status: online
      use_oidc: true
      vpc_mission_tag: logging
      vpc_mission_tag_app: logging
      worker_flavor: c6a.2xlarge
      zone_type: public
defaults:
    import_defaults:
        - ../../../../manifest.yaml
