version: 1
command_and_control: mccprod
clusters:
- name: wdfwgen-p-4
  env_name: wdfw03-ocp4-prod
  cloud_service_provider: openstack
  status: online
  worker_count: 0
  ingress_count: 3
  ingress_int_count: 1
  optimized_storage_count: 0
  bastion_count: 1
  master_count: 3
  module_version: v10.2.17-pool-image
  module_commit: 81c932dab07ad9308f7ce62a4964b7e9a8090044
  infractl_version: v7.18.12
  provisioning_module_version: v5.11.13
  terraform_version: v1.5.4
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  internal_provider_network: provider-406
  internal_provider_subnets: [***********/23]
  base_image: wbx3-jammy-1.25.6-containerd-v1-27-5-202407
  base_k8s_image: wbx3-jammy-1.25.6-containerd-v1-27-5-202407
  ocp_ownership_business_service: WBX3 Platform - Meetings
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: kube_network_node_prefix=26 kubelet_max_pods=56 container_manager='containerd'
  cidr_node_prefix: 26
  health_checks: true
  metadata:
    cluster_type: generic
    platform_release_channel: stable-2
    annotations:
      helm3Only: true
    deployment_groups:
    - mw-gen-prod-wdfw03-dc
    - opstream-dfw-prod
    - aggmet-infra-dfw
    - mas-monitor-prod-gsb
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/openstack-cloud-controller.yaml
  - platform/base-apps.yaml
  gateway_name: wdfw03-ocp4-prod
  node_pools:
  - name: worker
    type: worker
    min_size: 5
    max_size: 5
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: pool-name=worker,type=worker
  - name: metlp-p0-
    type: provider-worker
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    min_size: 3
    max_size: 3
    metadata:
      node_labels: dedicated=metlp,type=worker
      node_taints: dedicated=metlp:NoSchedule
  - name: provider-worker
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 10
    max_size: 10
    metadata:
      node_labels: pool-name=provider-worker,type=worker
  - name: mct-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 15
    max_size: 15
    metadata:
      node_labels: dedicated=mct,type=worker
      node_taints: dedicated=mct:NoSchedule
defaults:
  import_defaults: [../../../manifest.yaml]
