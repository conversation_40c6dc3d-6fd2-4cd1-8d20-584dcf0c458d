version: 1
command_and_control: mccprod
clusters:
- name: wdfwmw-ct-1
  env_name: wdfw03-ocp4-prod
  cloud_service_provider: openstack
  status: online
  worker_count: 0
  ingress_count: 10
  ingress_int_count: 2
  optimized_storage_count: 0
  bastion_count: 1
  master_count: 3
  worker_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  server_group_policies: [soft-anti-affinity]
  management_network: provider-407
  worker_eth1_network: provider-407
  internal_provider_network: provider-407
  internal_provider_subnets: [***********/23]
  base_image: wbx3-focal-1.23.5-containerd-v1-23-5-mw
  base_k8s_image: wbx3-focal-1.23.5-containerd-v1-23-5-mw
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: kube_network_node_prefix=26 kubelet_max_pods=56 container_manager='containerd'
  cidr_node_prefix: 26
  health_checks: true
  metadata:
    cluster_type: webapps-prod
    platform_release_channel: stable-3
    annotations:
      helm3Only: true
    deployment_groups:
    - mw-prod-ct
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/openstack-cloud-controller.yaml
  - platform/base-apps.yaml
  - webapps/init.yaml
  gateway_name: wdfw03-ocp4-prod
  node_pools:
  - name: mw-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 95
    max_size: 95
    metadata:
      node_labels: pool-name=mw-p0,type=worker
  - name: optimized-storage
    type: worker
    min_size: 2
    max_size: 2
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
defaults:
  import_defaults: [../../../manifest.yaml]
