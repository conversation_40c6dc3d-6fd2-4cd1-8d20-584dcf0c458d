---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
- name: wdfw03-ocp4-prod
  gateway_name: wdfw03-ocp4-prod
  domain: prod.infra.webex.com
  cloud_service_provider: openstack
  internal_network: wdfw03-ocp4-prod
  external_network: public-408
  management_network: provider-406
  worker_eth1_network: provider-406
  internal_provider_network: provider-406
  internal_provider_subnets: [10.245.32.0/23]
  base_image: wbx3-focal-1.23.5-containerd-v1-23-5
  base_k8s_image: wbx3-focal-1.23.5-containerd-v1-23-5
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  image_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  optimized_storage_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  infra_credentials_path: secret/data/mccprod/infra/wdfw03-ocp4-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  terraform_version: v1.2.4
  provisioning_module_version: v5.9.3
  module_version: v8.4.0
  infractl_version: v7.17.8
  backend: s3
  location: DFW
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  master_count: 3
  worker_count: 1
  ingress_count: 2
  ingress_int_count: 2
  thousand_eyes_node_count: 0
  optimized_storage_count: 0
  bastion_count: 1
  internal_media_node_count: 0
  external_media_node_count: 0
  internal_mini_media_node_count: 0
  gluster_count: 0
- name: wdfw03-ocp4-prod-media
  gateway_name: wdfw03-ocp4-prod-media
  domain: prod.infra.webex.com
  internal_network: wdfw03-ocp4-prod-media
  external_network: public-414
  internal_provider_network: provider-416
  management_network: provider-416
  internal_provider_subnets: [10.0.0.0/8]
  base_image: wbx3-focal-1.25.6-containerd-v1.27.0
  base_k8s_image: wbx3-focal-1.25.6-containerd-v1.27.0
  infra_credentials_path: secret/data/mccprod/infra/wdfw03-ocp4-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  terraform_version: v1.5.4
  module_version: v10.2.17
  infractl_version: v7.18.12
  provisioning_module_version: v5.11.10
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  backend: s3
  location: DFW
  ocp_ownership_business_service: "WBX3 Platform - Meetings"
networks:
- name: wdfw03-ocp4-prod
  env_name: wdfw03-ocp4-prod
- name: wdfw03-ocp4-prod-media
  env_name: wdfw03-ocp4-prod-media
gateways:
- name: wdfw03-ocp4-prod
  env_name: wdfw03-ocp4-prod
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  gateway_block_storage: true
  volume_storage: true
  gateway_count: 2
- name: wdfw03-ocp4-prod-media
  env_name: wdfw03-ocp4-prod-media
  internal_network: wdfw03-ocp4-prod-media
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  gateway_block_storage: true
  volume_storage: true
  gateway_count: 3
defaults:
  import_defaults: ["../../manifest.yaml"]
