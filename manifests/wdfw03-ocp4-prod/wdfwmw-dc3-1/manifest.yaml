version: 1
command_and_control: mccprod
clusters:
- name: wdfwmw-dc3-1
  env_name: wdfw03-ocp4-prod
  cloud_service_provider: openstack
  status: online
  worker_count: 0
  ingress_count: 4
  ingress_int_count: 2
  optimized_storage_count: 0
  bastion_count: 1
  master_count: 3
  worker_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  server_group_policies: [soft-anti-affinity]
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: kube_network_node_prefix=26 kubelet_max_pods=56 container_manager='containerd'
  cidr_node_prefix: 26
  cidr_pods_prefix: 18
  cidr_svcs_prefix: 22
  health_checks: true
  metadata:
    cluster_type: webapps-prod
    platform_release_channel: stable-3
    annotations:
      helm3Only: true
    deployment_groups:
    - mw-prod-dfw03-dc
    - mw-prod-dfw-dc3
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/openstack-cloud-controller.yaml
  - platform/base-apps.yaml
  - webapps/init.yaml
  gateway_name: wdfw03-ocp4-prod
  node_pools:
  - name: mw-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 35
    max_size: 35
    metadata:
      node_labels: pool-name=mw-p0,type=worker
  - name: mw-p1-
    type: provider-worker
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    min_size: 20
    max_size: 20
    metadata:
      node_labels: pool-name=mw-p1,type=worker
  - name: nbr-p0-
    type: provider-worker
    flavor: kubed-nbr.nv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 100
    max_size: 100
    metadata:
      node_labels: pool-name=nbr-p0,type=worker
      node_taints: dedicated=nbrwes:NoSchedule
  - name: wss-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 15
    max_size: 15
    metadata:
      node_labels: pool-name=wss-p0,type=worker
      node_taints: dedicated=nbrwss:NoSchedule
  - name: optimized-storage
    type: worker
    min_size: 2
    max_size: 2
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
defaults:
  import_defaults: [../../../manifest.yaml]
