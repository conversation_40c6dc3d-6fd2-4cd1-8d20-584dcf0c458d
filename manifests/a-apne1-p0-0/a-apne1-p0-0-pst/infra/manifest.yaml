version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # VPC peering pst0 to anrtp1
  - name: d8p_pst0-anrtp1
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d8p_pst0
      destination_vpc_name: anrtwxt-prod
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d8p_pst0-anrtp1
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*media_prv-direct*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering pst0 to anrtp2
  - name: d8p_pst0-anrtp2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d8p_pst0
      destination_vpc_name: anrtwxt-prod-v2
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d8p_pst0-anrtp2
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*media_prv-direct*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering pst0 to anrts2
  - name: d8p_pst0-anrts2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d8p_pst0
      destination_vpc_name: anrt-wxp-prod-a
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d8p_pst0-anrts2
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*media_prv-direct*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering pst0 to anrts1
  - name: d8p_pst0-anrts1
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d8p_pst0
      destination_vpc_name: anrt-vpc
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d8p_pst0-anrts1
      destination_subnet_filter:
        - "*private*"
      source_subnet_filter:
        - "*media_prv-direct*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
      destination_route_table_filter:
        - "*private*"
# pst0 to s3 backup
  - name: d8p_pst0-d8p_s30
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d8p_pst0
      destination_vpc_name: d8p_s30
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-2
      name: d8p_pst0-d8p_s30
      source_subnet_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*cluster*'
      destination_route_table_filter:
      - '*cluster*'
# s3 backup endpoint
  - name: d8p_s30-s3
    module_path: modules/aws/network_vpc_endpoint
    env_name: a-apne1-p0-0
    module_version: v10.16.7
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    args:
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      vpc_name: d8p_s30
      aws_infra_region: ap-northeast-2
      vpc_endpoint_list:
        - service_name: s3
          vpc_endpoint_type: Interface
          security_groups:
          - d8p_s30
      security_groups:
        d8p_s30:
          rules:
            - name: d8p_s30-s3
              description: "Allow all egress to S3"
              protocol: tcp
              from: 443
              to: 443
              cidrs:
                - 0.0.0.0/0
              type: egress
            - name: d8p_s30-s3-ingress
              description: "Allow all ingress from peer VPC"
              protocol: tcp
              from: 443
              to: 443
              cidrs:
                # Same as the isolated CIDR from the Persistence VPC
                - **********/16
              type: ingress
defaults:
  infractl_version: v7.18.33
  backend: s3
  terraform_version: v1.5.7
  use_provider_template: true
  embed_provider_template: true

  terraform_templates:
    - path: providers-commercial.tf.tpl

  include_defaults: true
