version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # VPC peering APP to APP
  - name: d80_app1-d81_app2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d80_app1
      destination_vpc_name: d81_app2
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d80_app1-d81_app2
      destination_subnet_filter:
        - "*workload_iso*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*workload_iso*"
  # VPC peering APP1 to pst0
  - name: d80_app1-d8p_pst0
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d80_app1
      destination_vpc_name: d8p_pst0
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d80_app1-d8p_pst0
      destination_subnet_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
      source_subnet_filter:
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
      destination_route_table_filter:
        - "*workload_iso*"
        - "*media_prv-direct*"
  # VPC peering APP1 to anrtp1
  - name: d80_app1-anrtp1
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d80_app1
      destination_vpc_name: anrtwxt-prod
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d80_app1-anrtp1
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP1 to anrtp2
  - name: d80_app1-anrtp2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d80_app1
      destination_vpc_name: anrtwxt-prod-v2
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d80_app1-anrtp2
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*cluster*"
  # VPC peering APP1 to anrts1
  - name: d80_app1-anrts1
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d80_app1
      destination_vpc_name: anrt-vpc
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d80_app1-anrts1
      destination_subnet_filter:
        - "*private*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*private*"
  # VPC peering APP1 to anrts2
  - name: d80_app1-anrts2
    module_path: modules/aws/wxt/peering_connection
    env_name: a-apne1-p0-0
    module_version: v10.9.0
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/pcx/splat-prod
      source_vpc_name: d80_app1
      destination_vpc_name: anrt-wxp-prod-a
      source_aws_infra_region: ap-northeast-1
      destination_aws_infra_region: ap-northeast-1
      name: d80_app1-anrts2
      destination_subnet_filter:
        - "*cluster*"
      source_subnet_filter:
        - "*service-igw*"
        - "*workload_iso*"
      source_route_table_filter:
        - "*workload_iso*"
        - "*service_cld-igw*"
      destination_route_table_filter:
        - "*cluster*"
  - name: wxt-d80-app1-sg
    module_path: modules/aws/wxt/security_groups
    env_name: a-apne1-p0-0
    module_version: v10.11.13
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/wxt/sg
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: d80_app1
      source_aws_infra_region: ap-northeast-1
      default_tags: {}
      security_groups:
        wxt-a02-app2-sg:
          create_new: true
          existing_sg_id: ""
          description: "SG attached on WxT workers to access persistence"
          tags:
            name: "wxt-d80-app1-sg"
            security_group_type: "kubed"
          ingress_rules:
            - from_port: 0
              to_port: 65535
              protocol: TCP
              cidr_blocks:
                - "********/16"
                - "********/16"
                - "*************/28"
                - "************/21"
                - "**********/24"
                - "***********/25"
                - "***********/20"
                - "***********/20"
                - "***********/20"
                - "**********/16"
                - "**********/16"
                - "**********/16"
                - "**********/16"
                - "*************/23"
                - "***********/18"
                - "**********/16"
              security_groups: []
              ipv6_cidr_blocks: []
              prefix_list_ids: []
              self: false
          egress_rules:
            - from_port: 0
              to_port: 65535
              protocol: TCP
              cidr_blocks: ["0.0.0.0/0"]
              security_groups: []
              ipv6_cidr_blocks: []
              prefix_list_ids: []
              self: false

defaults:
  infractl_version: v7.18.33
  backend: s3
  terraform_version: v1.5.7
  use_provider_template: true
  embed_provider_template: true

  terraform_templates:
    - path: providers-commercial.tf.tpl

  include_defaults: true
