version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

environments:
- name: w-jfk02-p0-0
  blueprint_repo: null
  gateway_name: not-used
  backend: s3
  enable_credential_lookup: true
  infra_credentials_path: secret/data/mccprod/infra/wjfk02-ocp4-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  domain: prod.infra.webex.com
  bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  terraform_version: v1.10.4
  cloud_provider: openstack
  ocp_ownership_server_type: wxkbsvr
  ocp_ownership_support_group: wbx3-prod
  ocp_ownership_component: wxkb
  ocp_ownership_business_service: WBX3 Platform - Meetings
  location: JFK

defaults:
    module_version: v10.13.4
    infractl_version: v7.18.44
    use_provider_template: true
    embed_provider_template: true
    terraform_templates:
      - path: providers-commercial.tf.tpl
    include_defaults: true
    import_defaults: ["../../../manifest.yaml"]
