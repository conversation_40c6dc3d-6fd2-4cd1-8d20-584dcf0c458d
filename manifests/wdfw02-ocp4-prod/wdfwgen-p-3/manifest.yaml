---
version: 1
command_and_control: mccprod
clusters:
- name: wdfwgen-p-3
  env_name: wdfw02-ocp4-prod
  provisioning_module_version: v5.6.0
  module_version: v7.23.4
  infractl_version: v7.17.2
  terraform_version: v1.2.4
  status: online
  ingress_count: 2
  ingress_int_count: 2
  bastion_count: 1
  master_count: 3
  worker_count: 0
  optimized_storage_count: 0
  cloud_service_provider: openstack
  worker_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  bastion_flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  health_checks: true
  cidr_pods: auto
  cidr_svcs: auto
  external_network: public-floating-3090
  worker_eth1_network: provider-3091
  management_network: provider-3091
  internal_provider_network: provider-3091
  internal_network: wdfw02-ocp4-prod
  internal_provider_subnets:
  - ***********/24
  provisioning_extra_args: container_manager='containerd'
  base_image: wbx3-focal-1.23.5-containerd-659_421a101
  base_k8s_image: wbx3-focal-1.23.5-containerd-659_421a101
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  https_internal_ips: [10.0.0.0/8, ***********/16, **********/16, **********/12, **********/14,
    *************/23, ************/24, ***********/24, ***********/21, *************/19,
    *************/21, *************/24, **********/14, **********/16, **********/19,
    ************/20, ***********/20, **********/16, ***********/20, ***********/20,
    **********/14]
  metadata:
    cluster_type: generic
    platform_release_channel: stable-2
    deployment_groups:
      - wap-advanced-diagnostic-prod-wdfw
      - wap-datahub-prod-wdfw
      - wap-people-insight-prod-wdfw
      - wap-profile-system-prod-wdfw
      - wap-report-platform-prod-wdfw
      - engsys-backstage-prod-wdfw
      - iaas-dfw-prod
      - mas-alert-prod-wdfw
      - mas-pinot-prod-wdfw
      - mas-monitor-prod-2
      - mas-monitor-worker-prod-2
      - mas-monitor-worker-prod-dfw02
      - mas-monitor-worker-prod-gsb
    annotations:
      helm3Only: true
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/base-apps.yaml
  - platform/kafka-base-apps.yaml
  - platform/external-dns.yaml
  - platform/falco.yaml
  server_group_policies: [soft-anti-affinity]
  gateway_name: wdfw02-ocp4-prod
  node_pools:
  - name: wap-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 25
    max_size: 25
    metadata:
      node_labels: pool-name=wap-p0,type=worker
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.64mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - name: worker
    type: provider-worker
    min_size: 5
    max_size: 5
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: pool-name=worker,type=worker
  - name: iaas-p0-
    type: provider-worker
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    min_size: 20
    max_size: 20
    metadata:
      node_labels: pool-name=iaas-p0,type=worker
  - name: mct-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 23
    max_size: 23
    metadata:
      node_labels: dedicated=mct,type=worker
      node_taints: dedicated=mct:NoSchedule
defaults:
  import_defaults: ["../../../manifest.yaml"]
