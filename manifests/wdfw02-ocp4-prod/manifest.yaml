---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
- name: wdfw02-ocp4-prod
  gateway_name: wdfw02-ocp4-prod
  domain: prod.infra.webex.com
  cloud_service_provider: openstack
  internal_network: wdfw02-ocp4-prod
  external_network: public-floating-3090
  internal_provider_network: provider-3091
  internal_provider_subnets: [10.250.71.0/24]
  base_image: wbx3-focal-1.23.5-containerd-659_421a101
  base_k8s_image: wbx3-focal-1.23.5-containerd-659_421a101
  infra_credentials_path: secret/data/mccprod/infra/wdfw02-ocp4-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  terraform_version: v1.2.4
  module_version: v7.23.4
  infractl_version: v7.17.2
  provisioning_module_version: v5.6.0
  backend: s3
  location: DFW
- name: wdfw02-ocp4-prod-media
  gateway_name: wdfw02-ocp4-prod-media
  domain: prod.infra.webex.com
  internal_network: wdfw02-ocp4-prod-media
  external_network: wdfw-prod-3098
  internal_provider_network: provider-3030
  management_network: provider-3091
  internal_provider_subnets: [10.0.0.0/8]
  base_image: wbx3-focal-1.23.5-containerd-659_421a101
  base_k8s_image: wbx3-focal-1.23.5-containerd-659_421a101
  infra_credentials_path: secret/data/mccprod/infra/wdfw02-ocp4-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  terraform_version: v1.2.4
  module_version: v7.23.4
  infractl_version: v7.17.2
  provisioning_module_version: v5.6.0
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  backend: s3
  location: DFW
networks:
- name: wdfw02-ocp4-prod
  env_name: wdfw02-ocp4-prod
- name: wdfw02-ocp4-mw
  env_name: wdfw02-ocp4-prod
  internal_network: wdfw02-ocp4-mw
  gateway_name: wdfw02-ocp4-mw
  external_network: public-floating-3090
- name: wdfw02-ocp4-prod-media
  env_name: wdfw02-ocp4-prod-media
gateways:
- name: wdfw02-ocp4-prod
  env_name: wdfw02-ocp4-prod
  gateway_count: 2
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  volume_storage: true
- name: wdfw02-ocp4-mw
  env_name: wdfw02-ocp4-prod
  internal_network: wdfw02-ocp4-mw
  gateway_name: wdfw02-ocp4-mw
  external_network: public-floating-3090
  internal_provider_network: provider-3252
  internal_provider_subnets: [10.244.72.0/22]
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  volume_storage: true
  gateway_count: 2
- name: wdfw02-ocp4-prod-media
  env_name: wdfw02-ocp4-prod-media
  internal_network: wdfw02-ocp4-prod-media
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  volume_storage: true
  gateway_block_storage: true
  gateway_count: 2
defaults:
  import_defaults: ["../../manifest.yaml"]
