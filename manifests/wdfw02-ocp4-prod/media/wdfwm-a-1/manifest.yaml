---
version: 1
command_and_control: mccprod
clusters:
- name: wdfwm-a-1
  status: online
  cloud_service_provider: openstack
  service_block: &wdfwm-a-1_sb america-b
  env_name: wdfw02-ocp4-prod-media
  module_version: v7.23.4-patch-customised-storage-size-v6
  module_commit: 5d4d8ea7ab52cff1cd9aa94d813a7be564245a61
  worker_eth1_network: provider-3091
  ingress_count: 3
  ingress_int_count: 1
  bastion_count: 1
  master_count: 3
  worker_count: 0
  optimized_storage_count: 0
  cidr_pods_prefix: 18
  cidr_node_prefix: 27
  worker_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  bastion_flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  health_checks: true
  cidr_pods: auto
  cidr_svcs: auto
  provisioning_extra_args: container_manager='containerd'
  mesh:
    enabled: true
  metadata:
    cluster_type: media
    platform_release_channel: stable-2
    annotations:
      calliope_org: wdfwm
      calliope_group: wdfwm
      service_block: *wdfwm-a-1_sb
      helm3Only: true
    deployment_groups:
      - crc-prod-america-canary
      - apsvcs-media-wdfwm-a-1
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/external-dns.yaml
  - platform/base-apps.yaml
  - platform/kafka-base-apps.yaml
  - media/init.yaml
  - media/lma-mirrors.yaml
  - media/calliope-media.yaml
  - platform/falco.yaml
  node_pools:
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.16vcpu.128mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - name: storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    root_volume_size: 512
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - name: worker
    type: worker
    min_size: 4
    max_size: 4
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: pool-name=worker,type=worker
  - name: hesiod-pool
    type: internal-mini-media
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    root_block_storage: true
    min_size: 19
    max_size: 19
    metadata:
      node_labels: type=hesiod
      dns_prefix: internal-mini-media
  - name: homer-pool
    type: external-large-media
    flavor: homer.12vcpu.24mem.0ssd.0eph
    root_block_storage: true
    min_size: 270
    max_size: 270
    metadata:
      node_labels: type=external-large-media,infra.webex.com/remove-ds-a-record=true,infra.webex.com/dual-stack-identifier=ds
      dns_prefix: xlm
  - name: linus-pool
    type: external-media
    flavor: Spark-Media.8vcpu.16mem.0ssd.0eph.numa
    root_block_storage: true
    min_size: 1
    max_size: 1
  - name: istio-ingress
    type: provider-worker
    min_size: 3
    max_size: 3
    root_block_storage: true
    metadata:
      node_labels: dedicated=istio-ingress
    security_groups:
      provider:
      - name: istio-lb-tcp
        type: ingress
        protocol: tcp
        from: 3000
        to: 5000
        cidr: 10.0.0.0/8
  external_media_sg_rules:
  - name: external-media-https
    type: ingress
    protocol: tcp
    from: 443
    to: 444
    cidr: 0.0.0.0/0
  - name: external-media-cascade-ports-tcp
    type: ingress
    protocol: tcp
    from: 11000
    to: 33000
    cidr: 0.0.0.0/0
  - name: external-media-cascade-ports-udp
    type: ingress
    protocol: udp
    from: 11000
    to: 33000
    cidr: 0.0.0.0/0
  - name: external-media-rtp-ports-tcp
    type: ingress
    protocol: tcp
    from: 49152
    to: 65535
    cidr: 0.0.0.0/0
  - name: external-media-rtp-ports-udp
    type: ingress
    protocol: udp
    from: 49152
    to: 65535
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-1-tcp
    type: ingress
    protocol: tcp
    from: 5004
    to: 5004
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-1-udp
    type: ingress
    protocol: udp
    from: 5004
    to: 5004
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-3-tcp
    type: ingress
    protocol: tcp
    from: 9000
    to: 9000
    cidr: 0.0.0.0/0
  - name: external-media-shared-ports-3-udp
    type: ingress
    protocol: udp
    from: 9000
    to: 9000
    cidr: 0.0.0.0/0
  - name: external-media-https-v6
    type: ingress
    protocol: tcp
    from: 443
    to: 444
    cidr: "::/0"
  - name: external-media-cascade-ports-tcp-v6
    type: ingress
    protocol: tcp
    from: 11000
    to: 33000
    cidr: "::/0"
  - name: external-media-cascade-ports-udp-v6
    type: ingress
    protocol: udp
    from: 11000
    to: 33000
    cidr: "::/0"
  - name: external-media-rtp-ports-tcp-v6
    type: ingress
    protocol: tcp
    from: 49152
    to: 65535
    cidr: "::/0"
  - name: external-media-rtp-ports-udp-v6
    type: ingress
    protocol: udp
    from: 49152
    to: 65535
    cidr: "::/0"
  - name: external-media-shared-ports-1-tcp-v6
    type: ingress
    protocol: tcp
    from: 5004
    to: 5004
    cidr: "::/0"
  - name: external-media-shared-ports-1-udp-v6
    type: ingress
    protocol: udp
    from: 5004
    to: 5004
    cidr: "::/0"
  - name: external-media-shared-ports-3-tcp-v6
    type: ingress
    protocol: tcp
    from: 9000
    to: 9000
    cidr: "::/0"
  - name: external-media-shared-ports-3-udp-v6
    type: ingress
    protocol: udp
    from: 9000
    to: 9000
    cidr: "::/0"
defaults:
  import_defaults: ["../../../../manifest.yaml"]
