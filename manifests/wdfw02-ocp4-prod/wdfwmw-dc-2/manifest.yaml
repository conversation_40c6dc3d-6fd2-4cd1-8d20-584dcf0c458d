version: 1
command_and_control: mccprod
clusters:
- name: wdfwmw-dc-2
  status: online
  env_name: wdfw02-ocp4-prod
  worker_count: 0
  ingress_count: 6
  ingress_int_count: 2
  optimized_storage_count: 0
  worker_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  bastion_flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  base_image: wbx3-focal-1.23.5-containerd-v1-23-5-mw
  base_k8s_image: wbx3-focal-1.23.5-containerd-v1-23-5-mw
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: kube_network_node_prefix=26 kubelet_max_pods=56 container_manager='containerd'
  health_checks: true
  terraform_version: v1.2.4
  provisioning_module_version: v5.9.3
  module_version: v8.4.0
  infractl_version: v7.17.8
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  metadata:
    cluster_type: webapps-prod
    platform_release_channel: stable-3
    annotations:
      helm3Only: true
    deployment_groups:
    - mw-prod-dfw02-dc1
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/openstack-cloud-controller.yaml
  - platform/base-apps.yaml
  - webapps/init.yaml
  server_group_policies: [soft-anti-affinity]
  internal_network: wdfw02-ocp4-mw
  external_network: public-floating-3090
  management_network: provider-3252
  internal_provider_network: provider-3252
  internal_provider_subnets: [***********/22]
  worker_eth1_network: provider-3252
  gateway_name: wdfw02-ocp4-mw
  node_pools:
  - name: mw-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 15
    max_size: 15
    metadata:
      node_labels: pool-name=mw-p0,type=worker
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.64mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - name: nbr-p0-
    type: provider-worker
    flavor: kubed-nbr.nv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 355
    max_size: 355
    metadata:
      node_labels: pool-name=nbr-p0,type=worker
      node_taints: dedicated=nbrwes:NoSchedule
  - name: wss-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 32
    max_size: 32
    metadata:
      node_labels: pool-name=wss-p0,type=worker
      node_taints: dedicated=nbrwss:NoSchedule
defaults:
  import_defaults: [../../../manifest.yaml]
