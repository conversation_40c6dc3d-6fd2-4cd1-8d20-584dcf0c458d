version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # DualStack NLB for wxt-calling mesh
  - name: wxtc-int-first-calling1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtc-int-first-calling1
      mesh_name: wxt-calling
      ingressgateway_name: wxt-calling-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-8443:
          protocol: TCP
          port: 8443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15020:
          protocol: TCP
          port: 15020
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-ccuc mesh
  - name: wxtuc-int-first-ccuc1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtuc-int-first-ccuc1
      mesh_name: wxt-ccuc
      ingressgateway_name: wxt-ccuc-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-ci mesh
  - name: wxtci-int-first-ci1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtci-int-first-ci1
      mesh_name: wxt-ci
      ingressgateway_name: wxt-ci-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15020:
          protocol: TCP
          port: 15020
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-l2sip-calling mesh
  - name: wxtm-int-first-l2sip1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtm-int-first-l2sip-calling1
      mesh_name: wxt-l2sip-calling
      ingressgateway_name: wxt-l2sip-calling-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-meet mesh
  - name: wxtm-int-first-meet1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtm-int-first-meet1
      mesh_name: wxt-meet
      ingressgateway_name: wxt-meet-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15020:
          protocol: TCP
          port: 15020
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15061:
          protocol: TCP
          port: 15061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15062:
          protocol: TCP
          port: 15062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-25061:
          protocol: TCP
          port: 25061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-25062:
          protocol: TCP
          port: 25062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-mercury mesh
  - name: wxtmer-int-first-mercury1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtmer-int-first-mercury1
      mesh_name: wxt-mercury
      ingressgateway_name: wxt-mercury-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-message mesh
  - name: wxtmsg-int-first-message1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtmsg-int-first-message1
      mesh_name: wxt-message
      ingressgateway_name: wxt-message-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-registration mesh
  - name: wxtreg-int-first-nlb
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtreg-int-first-registration1
      mesh_name: wxt-registration
      ingressgateway_name: wxt-registration-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-wxid mesh
  - name: wxtci-int-first-wxid1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtci-int-first-wxid1
      mesh_name: wxt-wxid
      ingressgateway_name: wxt-wxid-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-general mesh
  - name: wxtgen-int-first-general1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtgen-int-first-general1
      mesh_name: wxt-general
      ingressgateway_name: wxt-general-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15020:
          protocol: TCP
          port: 15020
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-vvs mesh
  - name: wxtvvs-int-first-vvs1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtvvs-int-first-vvs1
      mesh_name: wxt-vvs
      ingressgateway_name: wxt-vvs-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
        TCP-15061:
          protocol: TCP
          port: 15061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15062:
          protocol: TCP
          port: 15062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-25061:
          protocol: TCP
          port: 25061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-25062:
          protocol: TCP
          port: 25062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021

  # DualStack NLB for SPARK-648616 Workload Group POC
  # DualStack NLB for wxt-meet-wg mesh
  - name: wxtm-int-first-wg-1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtm-int-first-wg-meet1
      mesh_name: wxt-meet-wg
      ingressgateway_name: wxt-meet-wg-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15020:
          protocol: TCP
          port: 15020
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15061:
          protocol: TCP
          port: 15061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15062:
          protocol: TCP
          port: 15062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-25061:
          protocol: TCP
          port: 25061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-25062:
          protocol: TCP
          port: 25062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-registration-wg mesh
  - name: wxtreg-int-first-wg-1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtreg-int-first-wg-registration1
      mesh_name: wxt-registration-wg
      ingressgateway_name: wxt-registration-wg-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-mercury-wg mesh
  - name: wxtmer-int-first-wg-1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtmer-int-first-wg-mercury1
      mesh_name: wxt-mercury-wg
      ingressgateway_name: wxt-mercury-wg-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-ci-wg mesh
  - name: wxtci-int-first-wg-1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtci-int-first-wg-ci1
      mesh_name: wxt-ci-wg
      ingressgateway_name: wxt-ci-wg-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15020:
          protocol: TCP
          port: 15020
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
  # DualStack NLB for wxt-general-wg mesh
  - name: wxtgen-int-first-wg-1
    env_name: a-usea1-i0-0
    module_path: modules/aws/nlb
    module_version: dnssec-nlb
    module_commit: 46b39ffce2c4f07eee308d85ceb079349cb2cf43
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-1
      vpc_name: a00_app1
      allowed_clusters:
        - us-vaawxti0
      ip_address_type: dualstack
      deployment_group: wxtgen-int-first-wg-general1
      mesh_name: wxt-general-wg
      ingressgateway_name: wxt-general-wg-ingressgateway
      subnet_type: public-eip
      domain: a00.prod.infra.webex.com
      additional_domains:
        - a00.ite.webexgov.us
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0277a64bec3ddeb69
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15020:
          protocol: TCP
          port: 15020
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
