version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
  - name: us-vaawxti0
    env_name: a-usea1-i0-0
    network_name: a-usea1-i0-0-ha1
    base_os: jammy
    kubernetes_version: v1.31.5
    infractl_version: v7.18.36
    module_version: v10.13.4
    terraform_version: v1.6.3
    release: v1.8.2
    cluster_api: true
    admin_port: 6443
    apiserver_record_public: true
    aws_infra_az: us-east-1a
    aws_infra_azs:
      - us-east-1a
      - us-east-1b
      - us-east-1c
    aws_infra_region: us-east-1
    base_image: wbx3-capi-jammy-1.31.5-c8d-amd64-4
    base_k8s_image: wbx3-capi-jammy-1.31.5-c8d-amd64-4
    bastion_flavor: t2.small
    bastion_image_name: WBX-Ubuntu-20-x86_64-harden-nonfips-v1.2-202211
    cidr_nodes: ************/21
    cloud_service_provider: aws
    dns_credentials_path: secret/data/mccprod/infra/route53/credentials
    domain: a00.prod.infra.webex.com
    gateway_name: not-used
    infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    image_name: wbx3-capi-jammy-1.31.5-c8d-amd64-4
    location: IAD
    managed_by: us-txccnc2
    master_count: 3
    master_flavor: m6a.4xlarge
    mesh:
      enabled: true
    metadata:
      purpose: Integration teams cluster 1 for INTB135
      cluster_type: wxt
      platform_release_channel: alpha-2
      annotations:
        helm3Only: true
        kafka_logging_target: int
        kafka_metrics_target: int
      deployment_groups:
        - kubed-prod-wxt
        - wxtc-int-first-calling1
        - wxtcc-int-first-cc1
        - wxtci-int-first-ci1
        - wxtci-int-first-wxid1
        - wxtgen-int-first-general1
        - wxtm-int-first-l2sip-calling1
        - wxtm-int-first-meet1
        - wxtmer-int-first-mercury1
        - wxtmsg-int-first-message1
        - wxtreg-int-first-registration1
        - wxtuc-int-first-ccuc1
        - wxtvvs-int-first-vvs1
        - webex-persistence-tee4x-int
        - wxtci-int-first-wg-ci1
        - wxtgen-int-first-wg-general1
        - wxtm-int-first-wg-meet1
        - wxtmer-int-first-wg-mercury1
        - wxtreg-int-first-wg-registration1
        - apsvcs-wxt-aiad-int-first1
        - engsys-mao-int-first
        - persistence-cloudwatch-int
        - persistence-ksr-int
        - agglog-int-first-general1
        - aggmet-otel-wxt
        - slido-int-first-lite1
    nameservers:
      - ************
      - ************
    node_pools:
      - name: worker-1a
        availability_zones:
          - us-east-1a
        max_size: 20
        metadata:
          node_labels: type=worker,node.kubernetes.io/lifecycle=spot
        min_size: 0
        type: worker
        use_spot_instances: false
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
      - name: worker-1b
        availability_zones:
          - us-east-1b
        max_size: 20
        metadata:
          node_labels: type=worker,node.kubernetes.io/lifecycle=spot
        min_size: 0
        type: worker
        use_spot_instances: false
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
      - name: worker-1c
        availability_zones:
          - us-east-1c
        max_size: 20
        metadata:
          node_labels: type=worker,node.kubernetes.io/lifecycle=spot
        min_size: 0
        type: worker
        use_spot_instances: false
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
      - availability_zones:
          - us-east-1a
        max_size: 70
        metadata:
          node_labels: type=worker,zone1=us-east-1a,node.kubernetes.io/lifecycle=spot
        min_size: 0
        name: spot-worker-a
        type: worker
        use_spot_instances: true
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        external_security_groups:
          - name: wxt-a00-app1-sg
      - availability_zones:
          - us-east-1b
        max_size: 70
        metadata:
          node_labels: type=worker,zone3=us-east-1b,node.kubernetes.io/lifecycle=spot
        min_size: 0
        name: spot-worker-b
        type: worker
        use_spot_instances: true
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        external_security_groups:
          - name: wxt-a00-app1-sg
      - availability_zones:
          - us-east-1c
        max_size: 70
        metadata:
          node_labels: type=worker,zone3=us-east-1c,node.kubernetes.io/lifecycle=spot
        min_size: 0
        name: spot-worker-c
        type: worker
        use_spot_instances: true
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        external_security_groups:
          - name: wxt-a00-app1-sg
      - cluster_security_groups:
          - name: istio-ciscoint
        max_size: 3
        metadata:
          node_labels: dedicated=istio-ingress-ciscoint,node.kubernetes.io/lifecycle=spot
          node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
        min_size: 0
        name: istio-ciscoint
        type: worker
        use_spot_instances: true
      - cluster_security_groups:
          - name: istio-public
        max_size: 30
        metadata:
          node_labels: dedicated=istio-ingress,node.kubernetes.io/lifecycle=spot
          node_taints: dedicated=istio-ingress:NoSchedule
        min_size: 0
        name: istio-public
        type: worker
        use_spot_instances: true
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m5a.4xlarge
          - m5.4xlarge
      - cluster_security_groups:
          - name: istio-public
        max_size: 30
        metadata:
          node_labels: dedicated=istio-ingress,node.kubernetes.io/lifecycle=spot
          node_taints: dedicated=istio-ingress:NoSchedule
        min_size: 0
        name: istio-public-c
        type: worker
        use_spot_instances: true
        flavor: c6a.4xlarge
        fallback_flavors:
          - c6a.4xlarge
          - c5a.4xlarge
          - c5.4xlarge
      - cluster_security_groups:
          - name: istio-public
        max_size: 30
        metadata:
          node_labels: dedicated=istio-ingress,node.kubernetes.io/lifecycle=spot
          node_taints: dedicated=istio-ingress:NoSchedule
        min_size: 5
        name: istio-public-md
        type: worker
        use_spot_instances: false
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m5a.4xlarge
          - m5.4xlarge
      - availability_zones:
          - us-east-1a
        max_size: 6
        metadata:
          node_labels: type=optimized-storage-node
          node_taints: type=optimized-storage-node:NoSchedule
        min_size: 0
        name: opt-storage-1a
        type: worker
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
      - availability_zones:
          - us-east-1b
        max_size: 6
        metadata:
          node_labels: type=optimized-storage-node
          node_taints: type=optimized-storage-node:NoSchedule
        min_size: 0
        name: opt-storage-1b
        type: worker
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
      - availability_zones:
          - us-east-1c
        max_size: 6
        metadata:
          node_labels: type=optimized-storage-node
          node_taints: type=optimized-storage-node:NoSchedule
        min_size: 0
        name: opt-storage-1c
        type: worker
        flavor: m6a.4xlarge
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
      - name: thousand-eyes-a
        min_size: 1
        max_size: 1
        flavor: c6i.4xlarge
        availability_zones:
        - us-east-1a
        metadata:
          node_labels: dedicated=thousand-eyes
          node_taints: dedicated=thousand-eyes:NoSchedule
        cluster_security_groups:
        - name: thousand-eyes
      - name: thousand-eyes-b
        min_size: 1
        max_size: 1
        flavor: c6i.4xlarge
        availability_zones:
        - us-east-1b
        metadata:
          node_labels: dedicated=thousand-eyes
          node_taints: dedicated=thousand-eyes:NoSchedule
        cluster_security_groups:
        - name: thousand-eyes
      - name: thousand-eyes-c
        min_size: 1
        max_size: 1
        flavor: c6i.4xlarge
        availability_zones:
        - us-east-1c
        metadata:
          node_labels: dedicated=thousand-eyes
          node_taints: dedicated=thousand-eyes:NoSchedule
        cluster_security_groups:
        - name: thousand-eyes
      - availability_zones:
          - us-east-1a
        external_security_groups:
          - name: wxt-a00-app1-sg
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        flavor: m6a.4xlarge
        max_size: 20
        metadata:
          node_labels: type=worker,zone1=us-east-1a,dedicated=int-first
          node_taints: dedicated=int-first:NoSchedule
        min_size: 0
        name: wxt-pool-1a
        type: worker
      - availability_zones:
          - us-east-1b
        external_security_groups:
          - name: wxt-a00-app1-sg
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        flavor: m6a.4xlarge
        max_size: 20
        metadata:
          node_labels: type=worker,zone2=us-east-1b,dedicated=int-first
          node_taints: dedicated=int-first:NoSchedule
        min_size: 0
        name: wxt-pool-1b
        type: worker
      - availability_zones:
          - us-east-1c
        external_security_groups:
          - name: wxt-a00-app1-sg
        fallback_flavors:
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        flavor: m6a.4xlarge
        max_size: 20
        metadata:
          node_labels: type=worker,zone3=us-east-1c,dedicated=int-first
          node_taints: dedicated=int-first:NoSchedule
        min_size: 0
        name: wxt-pool-1c
        type: worker
      - name: spot-wxt-1a
        type: worker
        availability_zones:
          - us-east-1a
        min_size: 0
        max_size: 70
        spot_instance_pools: 0
        use_spot_instances: true
        spot_allocation_strategy: capacity-optimized
        flavor: m6a.4xlarge
        fallback_flavors:
          - m7i.4xlarge
          - m7a.4xlarge
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        metadata:
          node_taints: dedicated=int-first:NoSchedule,lifecycle=spot:NoSchedule
          node_labels: type=worker,zone1=us-east-1a,dedicated=int-first,node.kubernetes.io/lifecycle=spot
        external_security_groups:
          - name: wxt-a00-app1-sg
      - name: spot-wxt-1b
        type: worker
        availability_zones:
          - us-east-1b
        min_size: 0
        max_size: 70
        spot_instance_pools: 0
        use_spot_instances: true
        spot_allocation_strategy: capacity-optimized
        flavor: m6a.4xlarge
        fallback_flavors:
          - m7i.4xlarge
          - m7a.4xlarge
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        metadata:
          node_taints: dedicated=int-first:NoSchedule,lifecycle=spot:NoSchedule
          node_labels: type=worker,zone2=us-east-1b,dedicated=int-first,node.kubernetes.io/lifecycle=spot
        external_security_groups:
          - name: wxt-a00-app1-sg
      - name: spot-wxt-1c
        type: worker
        availability_zones:
          - us-east-1c
        min_size: 0
        max_size: 70
        spot_instance_pools: 0
        use_spot_instances: true
        spot_allocation_strategy: capacity-optimized
        flavor: m6a.4xlarge
        fallback_flavors:
          - m7i.4xlarge
          - m7a.4xlarge
          - m6a.4xlarge
          - m6i.4xlarge
          - m6id.4xlarge
          - m6in.4xlarge
          - m5.4xlarge
          - m5d.4xlarge
          - m5n.4xlarge
        metadata:
          node_taints: dedicated=int-first:NoSchedule,lifecycle=spot:NoSchedule
          node_labels: type=worker,zone3=us-east-1c,dedicated=int-first,node.kubernetes.io/lifecycle=spot
        external_security_groups:
          - name: wxt-a00-app1-sg
    pipeline_bundles:
      - platform/post-provision.yaml
      - platform/aws-cloud-controller.yaml
      - platform/base-apps.yaml
      - platform/keda.yaml
      - platform/flagger.yaml
      - platform/ctf.yaml
      - platform/flagger-loadtester.yaml
      - wxt/wxt-dynamic-config.yaml
      - wxt/wxt-init.yaml
    security_groups:
      istio-ciscoint:
        rules:
          - cidrs:
              - 10.0.0.0/8
              - vpc
            from: 8443
            name: cisco-https
            protocol: tcp
            to: 8443
            type: ingress
          - cidrs:
              - vpc
            from: 15021
            name: health
            protocol: tcp
            to: 15021
            type: ingress
        tags:
          - key: kubernetes.io/cluster/us-vaawxti0
            value: owned
      istio-public:
        rules:
          - cidrs:
              - 0.0.0.0/0
            from: 8443
            name: https
            protocol: tcp
            to: 8443
            type: ingress
          - cidrs:
              - vpc
            from: 15021
            name: health
            protocol: tcp
            to: 15021
            type: ingress
          - cidrs:
              - 0.0.0.0/0
            from: 5061
            name: https
            protocol: tcp
            to: 5062
            type: ingress
        tags:
          - key: kubernetes.io/cluster/us-vaawxti0
            value: owned
      thousand-eyes:
        rules:
          - name: thousand-eyes-icmp-echo-reply
            type: ingress
            protocol: icmp
            from: 0
            to: 0
            cidrs:
              - 0.0.0.0/0
          - name: thousand-eyes-icmp-echo
            type: ingress
            protocol: icmp
            from: 8
            to: 8
            cidrs:
              - 0.0.0.0/0
          - name: thousand-eyes-icmp-unreachable
            type: ingress
            protocol: icmp
            from: 3
            to: 3
            cidrs:
              - 0.0.0.0/0
          - name: thousand-eyes-icmp-time-exceeded
            type: ingress
            protocol: icmp
            from: 11
            to: 11
            cidrs:
              - 0.0.0.0/0
          - name: thousand-eyes-a2a-tcp
            type: ingress
            protocol: tcp
            from: 49153
            to: 49153
            cidrs:
              - 0.0.0.0/0
          - name: thousand-eyes-a2a-udp
            type: ingress
            protocol: udp
            from: 49153
            to: 49153
            cidrs:
              - 0.0.0.0/0
          - name: thousand-eyes-https
            type: ingress
            protocol: tcp
            from: 443
            to: 443
            cidrs:
              - 0.0.0.0/0
          - name: allow-all-egress
            type: egress
            protocol: all
            from: 0
            to: 0
            cidrs:
              - 0.0.0.0/0
        tags:
          - key: kubernetes.io/cluster/us-vaawxti0
            value: owned
    status: online
    use_oidc: true
    vpc_mission_tag_app: teams
    worker_flavor: t3.xlarge

defaults:
  module_version: v10.9.0
  infractl_version: v7.18.36
  terraform_version: v1.5.7
  s3_bucket_region: us-east-2
  use_provider_template: true
  embed_provider_template: true
  terraform_templates:
    - path: providers-commercial.tf.tpl
  include_defaults: true
  import_defaults:
    - ../../../../manifest.yaml
