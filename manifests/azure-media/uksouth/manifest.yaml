version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
- name: mlhrm-a-2
  status: online
  provisioning_extra_args: enable_gw_routing='false' kube_network_plugin='flannel'
    container_manager='containerd' kube_network_node_prefix=26 kubelet_max_pods=56
  internal_network: wbx3-uksouth-1
  azure_cert_vault_name: prod-uksouth-cert
  external_network: public-0
  eip_pool_id: public-ip-sub-a
  internal_media_node_count: 0
  external_media_node_count: 0
  windows_media_node_count: 0
  optimized_storage_count: 3
  thousand_eyes_node_count: 0
  worker_count: 3
  ingress_int_flavor: Standard_F8s_v2
  azurerm_image_shared_version:
    name: 2024.01.26
    image_name: WBX3ImageFocal
    gallery_name: WBX3ImageGallery
    resource_group_name: packertest
  azurerm_scale_shared_image_version:
    name: 202506.10.37
    image_name: WBX3ImageJammy
    gallery_name: WBX3ImageGallery
    resource_group_name: packertest
  azurerm_windows_shared_image_version:
    name: 202506.26.28
    image_name: WBX3ImageWindows
    gallery_name: WBX3ImageGallery
    resource_group_name: packertest
  terraform_version: v1.5.4
  module_version: v10.2.14-lhr-fix
  module_commit: 1f37a87da1124884cec10e2f177ced42f796a3bf
  infractl_version: v7.18.2
  provisioning_module_version: v5.11.3-fix-flannel
  service_block: &mlhrm-a-2_sb europe-msteams
  env_name: az-uksouth
  cidr_node_prefix: 26
  cidr_pods_prefix: 16
  cidr_pods: auto
  cidr_svcs: auto
  gateway_name: runon-prod
  metadata:
    cluster_type: cvi_media
    platform_release_channel: stable-1
    annotations:
      helm3Only: true
      calliope_org: mlhrm
      calliope_group: mlhrma
      service_block: *mlhrm-a-2_sb
  node_pools:
  - name: inta2
    type: internal-media
    min_size: 0
    max_size: 150
    flavor: Standard_F32s_v2
    memory: 62Gi
    cpu: 32500m
  - name: exta2
    type: external-media
    min_size: 0
    max_size: 60
    flavor: Standard_F8s_v2
    memory: 12Gi
    cpu: 8500m
  - name: workera2
    type: worker
    min_size: 0
    max_size: 6
    flavor: Standard_F8s_v2
    memory: 12Gi
    cpu: 8500m
  - name: wlhra2b
    type: windows-media
    min_size: 0
    max_size: 180
    flavor: Standard_F8s_v2
    memory: 12Gi
    cpu: 8500m
    metadata:
      node_labels: type=windows-media-node
      dns_prefix: windows-media
  - name: wlhra2c
    type: windows-media
    min_size: 0
    max_size: 110
    flavor: Standard_F8s_v2
    memory: 12Gi
    cpu: 8500m
    public_network_name: /subscriptions/09e8097f-3300-4dbd-a5ff-7a28921a4bf2/resourcegroups/wbx3-uksouth/providers/Microsoft.Network/publicIPPrefixes/public-ip-sub-c
    metadata:
      node_labels: type=windows-media-node
      dns_prefix: windows-media
  - name: thousand-eyes
    type: thousand-eyes
    min_size: 1
    max_size: 1
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/azure-cloud-controller.yaml
  - platform/autoscale.yaml
  - platform/base-apps.yaml
  - media/init.yaml
  - media/lma-mirrors.yaml
  - platform/keda.yaml
  - media/calliope-media.yaml
  - media/cmtc-deploy.yaml
  - platform/falco.yaml

- name: mlhrm-b-2
  status: online
  provisioning_extra_args: enable_gw_routing='false' kube_network_plugin='flannel'
    container_manager='containerd' kube_network_node_prefix=26 kubelet_max_pods=56
  internal_network: wbx3-uksouth-1
  azure_cert_vault_name: prod-uksouth-cert
  external_network: public-0
  eip_pool_id: public-ip-sub-b
  internal_media_node_count: 0
  external_media_node_count: 0
  windows_media_node_count: 0
  optimized_storage_count: 3
  thousand_eyes_node_count: 0
  worker_count: 3
  ingress_int_flavor: Standard_F8s_v2
  azurerm_image_shared_version:
    name: 2024.01.26
    image_name: WBX3ImageFocal
    gallery_name: WBX3ImageGallery
    resource_group_name: packertest
  azurerm_scale_shared_image_version:
    name: 202506.10.37
    image_name: WBX3ImageJammy
    gallery_name: WBX3ImageGallery
    resource_group_name: packertest
  azurerm_windows_shared_image_version:
    name: 202506.26.28
    image_name: WBX3ImageWindows
    gallery_name: WBX3ImageGallery
    resource_group_name: packertest
  terraform_version: v1.5.4
  module_version: v10.2.14-provider-fix
  module_commit: 49235ba278fe8fceefc36b4343d4d9a810d185e1
  infractl_version: v7.18.2
  provisioning_module_version: v5.11.3-fix-flannel
  service_block: &mlhrm-b-2_sb europe-msteams
  env_name: az-uksouth
  cidr_node_prefix: 26
  cidr_pods_prefix: 16
  cidr_pods: auto
  cidr_svcs: auto
  gateway_name: runon-prod
  metadata:
    cluster_type: cvi_media
    platform_release_channel: stable-2
    annotations:
      helm3Only: true
      calliope_org: mlhrm
      calliope_group: mlhrmb
      service_block: *mlhrm-b-2_sb
  node_pools:
  - name: intb2
    type: internal-media
    min_size: 0
    max_size: 150
    flavor: Standard_F32s_v2
    memory: 62Gi
    cpu: 32500m
  - name: extb2
    type: external-media
    min_size: 0
    max_size: 60
    flavor: Standard_F8s_v2
    memory: 12Gi
    cpu: 8500m
  - name: workerb2
    type: worker
    min_size: 0
    max_size: 6
    flavor: Standard_F8s_v2
    memory: 12Gi
    cpu: 8500m
  - name: wlhrb2b
    type: windows-media
    min_size: 0
    max_size: 180
    flavor: Standard_F8s_v2
    memory: 12Gi
    cpu: 8500m
    metadata:
      node_labels: type=windows-media-node
      dns_prefix: windows-media
  - name: wlhrb2c
    type: windows-media
    min_size: 0
    max_size: 110
    flavor: Standard_F8s_v2
    memory: 12Gi
    cpu: 8500m
    public_network_name: /subscriptions/09e8097f-3300-4dbd-a5ff-7a28921a4bf2/resourcegroups/wbx3-uksouth/providers/Microsoft.Network/publicIPPrefixes/public-ip-sub-c
    metadata:
      node_labels: type=windows-media-node
      dns_prefix: windows-media
  pipeline_bundles:
  - platform/ecr-deploy.yaml
  - platform/post-provision.yaml
  - platform/azure-cloud-controller.yaml
  - platform/autoscale.yaml
  - platform/base-apps.yaml
  - media/init.yaml
  - media/lma-mirrors.yaml
  - platform/keda.yaml
  - media/calliope-media.yaml
  - media/cmtc-deploy.yaml
  - platform/falco.yaml
defaults:
  import_defaults: [../../../manifest.yaml]
