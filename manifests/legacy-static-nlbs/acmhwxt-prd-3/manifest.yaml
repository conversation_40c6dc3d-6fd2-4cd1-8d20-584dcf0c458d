version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  # DualStack NLB for mesh-wxt-ci mesh for acmhwxt-prd-3
  - name: wxtci-prod-achm-cifls1
    env_name: acmhwxt-prod-v2
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-2
      vpc_name: acmhwxt-prod-v2
      ip_address_type: dualstack
      deployment_group: wxtci-prod-achm-cifls1
      mesh_name: mesh-wxt-ci
      ingressgateway_name: wxt-ci-ingressgateway
      custom_nlb_name: wxtci-prod-achm-cifls1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0acf5e097d78cdf09
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 8443
              to: 8443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
  # DualStack NLB for mesh-wxt-wxid mesh for acmhwxt-prd-3
  - name: wxtci-prod-achm-wxid1
    env_name: acmhwxt-prod-v2
    module_path: modules/aws/nlb
    module_version: v10.16.6
    terraform_version: v1.5.3
    infractl_version: v7.19.0
    include_environment: false
    include_defaults: false
    dir: infra/nlb
    args:
      aws_infra_region: us-east-2
      vpc_name: acmhwxt-prod-v2
      ip_address_type: dualstack
      deployment_group: wxtci-prod-achm-wxid1
      mesh_name: mesh-wxt-wxid
      ingressgateway_name: wxt-wxid-ingressgateway
      custom_nlb_name: wxtci-prod-achm-wxid1-legacy
      subnet_type: legacy_public
      domain: prod.infra.webex.com
      assign_eip: true
      ipv4_eip_pool: ipv4pool-ec2-0acf5e097d78cdf09
      target_groups:
        TCP-443:
          protocol: TCP
          port: 443
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5061:
          protocol: TCP
          port: 5061
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-5062:
          protocol: TCP
          port: 5062
          target_type: ip
          preserve_client_ip: true
          proxy_protocol_v2: true
          hc_protocol: TCP
          hc_port: 15021
        TCP-15021:
          protocol: TCP
          port: 15021
          target_type: ip
          hc_protocol: "HTTP"
          hc_port: 15021
          hc_path: "/healthz/ready"
          hc_matcher: "200-399"
      security_groups:
        istio-pub:
          rules:
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 443
              to: 443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 8443
              to: 8443
              name: https
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 15021
              to: 15021
              name: health
              protocol: tcp
              type: ingress
            - cidrs:
                - 0.0.0.0/0
              ipv6_cidrs:
                - ::/0
              from: 0
              to: 65535
              name: egress
              protocol: tcp
              type: egress
