---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
- name: wyul01-ocp4-prod
  gateway_name: wyul01-ocp4-prod
  domain: prod.infra.webex.com
  cloud_service_provider: openstack
  internal_network: wyul01-ocp4-prod
  external_network: provider-430
  management_network: provider-429
  worker_eth1_network: provider-429
  internal_provider_network: provider-429
  internal_provider_subnets: [10.250.210.0/24]
  base_image: wbx3-focal-1.27.12-containerd-202501-5
  base_k8s_image: wbx3-focal-1.27.12-containerd-202501-5
  worker_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  image_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  optimized_storage_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  external_media_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  thousand_eyes_node_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  bastion_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  infra_credentials_path: secret/data/mccprod/infra/wyul-mw-prod/openstack
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  server_group_policies: [anti-affinity]
  terraform_version: v1.5.4
  module_version: v10.2.17
  infractl_version: v7.18.12
  provisioning_module_version: v5.13.6
  backend: s3
  location: YUL
  ocp_ownership_business_service: "WBX3 Platform - Meetings"
  volume_storage: true
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  master_count: 3
  worker_count: 0
  ingress_count: 3
  ingress_int_count: 3
  thousand_eyes_node_count: 0
  optimized_storage_count: 0
  bastion_count: 2
  internal_media_node_count: 0
  external_media_node_count: 0
  internal_mini_media_node_count: 0
  gluster_count: 0
  cidr_nodes: ************/21
  nameservers: [************, ************]
networks:
- name: wyul01-ocp4-prod
  env_name: wyul01-ocp4-prod
gateways:
- name: wyul01-ocp4-prod
  env_name: wyul01-ocp4-prod
  internal_network: wyul01-ocp4-prod
  gateway_flavor: kubed.gv.4vcpu.8mem.0ssd.0eph
  volume_storage: true
  gateway_block_storage: true
  gateway_count: 2
defaults:
  import_defaults: ["../../manifest.yaml"]
