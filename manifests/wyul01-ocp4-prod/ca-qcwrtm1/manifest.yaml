version: 1
command_and_control: mccprod
clusters:
- name: ca-qcwrtm1
  status: online
  env_name: wyul01-ocp4-prod
  release: v1.7.1
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/openstack-cloud-controller.yaml
  - platform/base-apps.yaml
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  datacenter: yul
  health_checks: true
  server_group_policies: [soft-anti-affinity]
  cidr_node_prefix: 26
  provisioning_extra_args: kubelet_max_pods=56 container_manager='containerd'
  infractl_version: v7.18.43
  module_version: v10.7.17-fix-sg-attachment
  module_commit: e3830b10c2ccbd1aa787df315948895af853e5f1
  terraform_version: v1.5.0
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_count: 3
  mesh:
    enabled: false
  node_pools:
  - name: worker-pool
    type: worker
    min_size: 19
    max_size: 19
    root_block_storage: true
  - name: prom-pool
    type: worker
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    metadata:
      node_labels: type=optimized-storage-node
  - name: sip-pool-a
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    min_size: 12
    max_size: 12
    public_network_name: provider-430
    media_network_name: provider-429
    root_block_storage: true
    metadata:
      dns_prefix: sse-a
      node_labels: pool-name=sip-pool-a,type=external-media,infra.webex.com/dns-prefix=sse-a,infra.webex.com/public-ipv6-addr=2a05-4200-e--5bc-101
      node_taints: type=media-node:NoSchedule
    provider_security_group_names:
    - sse-media
    public_security_group_names:
    - sse-ext
  - name: media-pool-a
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    min_size: 16
    max_size: 16
    public_network_name: provider-430
    media_network_name: provider-429
    root_block_storage: true
    metadata:
      dns_prefix: mse-a
      node_labels: pool-name=media-pool-a,type=external-media,infra.webex.com/dns-prefix=mse-a
      node_taints: type=media-node:NoSchedule
    provider_security_group_names:
    - mse-media
    public_security_group_names:
    - mse-ext
  - name: sip-pool-b
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    min_size: 10
    max_size: 10
    public_network_name: public-431
    media_network_name: provider-429
    root_block_storage: true
    metadata:
      dns_prefix: sse-b
      node_labels: pool-name=sip-pool-b,type=external-media,infra.webex.com/dns-prefix=sse-b,infra.webex.com/public-ipv6-addr=2a05-4200-e--5bc-101
      node_taints: type=media-node:NoSchedule
    provider_security_group_names:
    - sse-media
    public_security_group_names:
    - sse-ext
  - name: media-pool-b
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    min_size: 14
    max_size: 14
    public_network_name: public-431
    media_network_name: provider-429
    root_block_storage: true
    metadata:
      dns_prefix: mse-b
      node_labels: pool-name=media-pool-b,type=external-media,infra.webex.com/dns-prefix=mse-b
      node_taints: type=media-node:NoSchedule
    provider_security_group_names:
    - mse-media
    public_security_group_names:
    - mse-ext
  - name: sip-peering-pool
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    min_size: 1
    max_size: 1
    public_network_name: provider-433
    media_network_name: provider-429
    root_block_storage: true
    custom_routing: true
    routes:
      eth1:
      - 10.0.0.0/8
      eth2:
      - ***********/20
      - ************/20
      - ************/20
    metadata:
      dns_prefix: sse-p
      node_labels: pool-name=sip-peering-pool,type=external-media,infra.webex.com/dns-prefix=sse-p
      node_taints: type=media-node:NoSchedule
    provider_security_group_names:
    - sse-media
    public_security_group_names:
    - sse-ext
  - name: media-peering-pool
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    min_size: 2
    max_size: 2
    public_network_name: provider-433
    media_network_name: provider-429
    root_block_storage: true
    custom_routing: true
    routes:
      eth1:
      - 10.0.0.0/8
      eth2:
      - ***********/20
      - ************/20
      - ************/20
    metadata:
      dns_prefix: mse-p
      node_labels: pool-name=media-peering-pool,type=external-media,infra.webex.com/dns-prefix=mse-p
      node_taints: type=media-node:NoSchedule
    provider_security_group_names:
    - mse-media
    public_security_group_names:
    - mse-ext
  - name: wxc-dhruva-proxy
    type: external-media
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    min_size: 2
    max_size: 2
    public_network_name: provider-426
    media_network_name: WxC-Commercial
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,type=external-media,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod
      node_taints: type=media-node:NoSchedule
    provider_security_group_names:
    - dhruva-proxy-media
    public_security_group_names:
    - dhruva-proxy-ext
  - name: wxc-dhruva-antares
    type: external-media
    flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
    min_size: 4
    max_size: 4
    public_network_name: provider-426
    media_network_name: WxC-Commercial
    root_block_storage: true
    metadata:
      dns_prefix: wxc-dhruva-antares
      node_labels: pool-name=wxc-dhruva-antares,type=external-media,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=prod
      node_taints: type=media-node:NoSchedule
    provider_security_group_names:
    - antares-media
    public_security_group_names:
    - antares-ext
  metadata:
    cluster_type: wxcedge-prod
    platform_release_channel: stable-1
    annotations:
      helm3Only: true
    deployment_groups:
    - kubed-prod-gen
    - kubed-prod-calling
    - wxc-edge-sse-ca-qcwrtm1-prod
    - wxc-edge-mse-ca-qcwrtm1-prod
    - wxc-edge-sbc-operator-ca-qcwrtm1-prod
  domain: prod.infra.webex.com
  security_groups:
    sse-media:
    - name: sse-sip-internal-udp-1
      type: ingress
      protocol: udp
      from: 5060
      to: 5060
      cidr: 10.0.0.0/8
    - name: sse-sip-internal-udp-2
      type: ingress
      protocol: udp
      from: 5060
      to: 5060
      cidr: **********/12
    - name: sse-sip-internal-tcp-1
      type: ingress
      protocol: tcp
      from: 5060
      to: 5060
      cidr: 10.0.0.0/8
    - name: sse-sip-internal-tcp-2
      type: ingress
      protocol: tcp
      from: 5060
      to: 5060
      cidr: **********/12
    sse-ext:
    - name: sse-sip-internal-tcp-3
      type: ingress
      protocol: tcp
      from: 5061
      to: 5061
      cidr: 0.0.0.0/0
    - name: sse-sip-internal-tcp-4
      type: ingress
      protocol: tcp
      from: 5062
      to: 5062
      cidr: 0.0.0.0/0
    - name: sse-sip-internal-tcp-5
      type: ingress
      protocol: tcp
      from: 8934
      to: 8934
      cidr: 0.0.0.0/0
    mse-media:
    - name: mse-grpc-tcp-1
      type: ingress
      protocol: tcp
      from: 9443
      to: 9443
      cidr: 10.0.0.0/8
    - name: mse-grpc-tcp-2
      type: ingress
      protocol: tcp
      from: 9443
      to: 9443
      cidr: **********/12
    - name: mse-media-rtp-udp-media-1
      type: ingress
      protocol: udp
      from: 19560
      to: 65535
      cidr: 10.0.0.0/8
    - name: mse-media-rtp-udp-media-2
      type: ingress
      protocol: udp
      from: 19560
      to: 65535
      cidr: **********/12
    mse-ext:
    - name: mse-media-rtp-udp
      type: ingress
      protocol: udp
      from: 19560
      to: 65535
      cidr: 0.0.0.0/0
    - name: mse-media-multiplexed
      type: ingress
      protocol: udp
      from: 5004
      to: 5004
      cidr: 0.0.0.0/0
    dhruva-proxy-media:
    - name: wxc-dhruva-proxy-udp
      type: ingress
      protocol: udp
      from: 5060
      to: 5060
      cidr: 0.0.0.0/0
    - name: wxc-dhruva-proxy-tcp
      type: ingress
      protocol: tcp
      from: 5060
      to: 5061
      cidr: 0.0.0.0/0
    dhruva-proxy-ext:
    - name: wxc-dhruva-proxy-udp
      type: ingress
      protocol: udp
      from: 5060
      to: 5060
      cidr: 0.0.0.0/0
    - name: wxc-dhruva-proxy-tcp
      type: ingress
      protocol: tcp
      from: 5060
      to: 5061
      cidr: 0.0.0.0/0
    antares-media:
    - name: wxc-dhruva-antares-udp
      type: ingress
      protocol: udp
      from: 19560
      to: 65535
      cidr: 0.0.0.0/0
    - name: wxc-dhruva-antares-tcp
      type: ingress
      protocol: tcp
      from: 19560
      to: 65535
      cidr: 0.0.0.0/0
    antares-ext:
    - name: wxc-dhruva-antares-udp
      type: ingress
      protocol: udp
      from: 19560
      to: 65535
      cidr: 0.0.0.0/0
    - name: wxc-dhruva-antares-tcp
      type: ingress
      protocol: tcp
      from: 19560
      to: 65535
      cidr: 0.0.0.0/0
defaults:
  import_defaults: [../../../manifest.yaml]
