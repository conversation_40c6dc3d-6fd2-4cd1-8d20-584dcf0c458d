---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

environments:
- name: apse2-ha1
  domain: prod.infra.webex.com
  infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  #wbx3-aws-tgw (************)
  peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
  s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
  cloud_service_provider: aws

infra:
  # VPC peering add on module
  - name: apse2-pcx-cal1-to-cal2
    env_name: apse2-ha1
    module_path: modules/aws/wxt/peering_connection
    module_version: v10.7.1
    infractl_version: v7.18.17
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_aws_infra_region: ap-southeast-2
      source_vpc_name: p1_cal1
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_aws_infra_region: ap-southeast-4
      destination_vpc_name: p2_cal2
      infra_service_domain: "https://infra.int.mccprod.prod.infra.webex.com"
      name: apse2-pcx-cal1-to-cal2
      destination_subnet_filter:
        - "*prv*"
      destination_route_table_filter:
        - "*prv*"
      source_subnet_filter:
        - "*prv*"
      source_route_table_filter:
        - "*prv*"
  - name: wxc-au-tgw-1
    terraform_version: v1.5.0
    module_version: v10.8.0
    module_path: modules/aws/transit_gateway
    env_name: apse2-ha1
    args:
      aws_infra_region: ap-southeast-2
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      vpc_name: a4_cal1
      subnet_filter:
        webex_purpose: access-untrusted-media
      tgw_owner_id: '423400439627'
      extra_tgw_filters:
        - name: transit-gateway-id
          values:
          - tgw-0e7288b85854cf498
      routes:
        - subnet_tags:
            webex_purpose: access-untrusted-media
          subnet: **************/25
        - subnet_tags:
            webex_purpose: access-untrusted-media
          subnet: ************/25
        - subnet_tags:
            webex_purpose: access-untrusted-media
          subnet: **************/25

networks:
    # This pair is with the Calling deployment in ap-southeast-4
  - name: apse2-production-ha1
    env_name: apse2-ha1
    aws_infra_az: ap-southeast-2a
    include_environment: true
    module_version: v10.7.16
    module_path: "modules/aws/network_archipelago"
    infractl_version: v7.18.17

    region: ap-southeast-2
    environment: 'a4'
    primary_cidr_prefix_length: 17
    ha_deployment_side: first
    availability_zones:
      az1:
        availability_zone: ap-southeast-2a
      az2:
        availability_zone: ap-southeast-2b
      az3:
        availability_zone: ap-southeast-2c

    egress_vpc:
      vpc_index: 1
      webx_dc_nat_range: *************/25 # CIDR for nat_to_dc_subnets

    ingress_vpc:
      vpc_index: 1
      cidr_public_services: disabled # CIDR used in front_end_services_public
      cidr_private_services: ***********/25 # CIDR used in front_end_services_private

    inspection_vpc:
      vpc_index: 1
      enabled: false
      cidr_prv_direct_management: disabled
      aws_gateway_asn: **********

    ##################################
    ### Application VPC Configurations
    ##################################

    media_vpcs:
      calling:
        enabled: true
        vpc_index: 1
        aws_gateway_asn: **********
        cidr_persistence: disabled
        cidr_private_media: disabled
        cidr_public_media: disabled
        cidr_access_private: ***********/24
        cidr_trunk_private: *************/25
        cidr_access_public: **************/25
        cidr_trunk_public: *************/25
    #Support for IP reachability to WxC DC when connection originates from kubed.
        subnets:
          isolated:
            routes:
              10.0.0.0/8:
                nat_gateway_subnet: access-private
                nat_gateway_type: private
          access-private:
            nat_private: true

    outbound_resolver_rules:
    - domain_name: broadcloud.org
      resolver_rule_name: broadcloud-resolver
      targets:
      # DNS IPs for SY1
      - target_ip: **********
      - target_ip: **********
      # DNS IPs for ME1
      - target_ip: **********
      - target_ip: **********
    - domain_name: cisco.com
      resolver_rule_name: cisco-com_outbound
      targets:
      - target_ip: ************
      - target_ip: ************
    - domain_name: webex.com
      resolver_rule_name: webex-com_outbound
      targets:
      - target_ip: ************
      - target_ip: ************

    enable_dx_association: true
    core_tgw_amazon_asn: **********

    ### SYD10
    vgw_dx_gateway_id: ********-f9b8-4758-b563-85ad1b30a17a
    tgw_dx_gateway_id: abbac09f-9a97-4298-beb1-************
    dx_amazon_owner_account_id: ************
    public_ipv4_pool: ipv4pool-ec2-0990b6b6583fbbd89

    root_domain: webex.com
    private_root_domain: disabled
    public_root_domain: prod.infra.webex.com
    absorb_public_root_domain: true

defaults:
    module_version: v10.7.1
    infractl_version: v7.18.17
    backend: s3
    s3_bucket_region: us-east-2
     # Prod is us-east-2, integration is us-east-1
    terraform_version: v1.5.0
    use_provider_template: true
    embed_provider_template: true

    terraform_templates:
      - path: providers-commercial.tf.tpl

    include_defaults: true
    import_defaults: ["../../../manifest.yaml"]
