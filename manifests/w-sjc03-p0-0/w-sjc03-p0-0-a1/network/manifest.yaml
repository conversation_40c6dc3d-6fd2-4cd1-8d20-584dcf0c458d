version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
networks:
- name: w-sjc03-p0-0-a1
  env_name: w-sjc03-p0-0
  module_version: v10.13.4
  module_path: modules/openstack/network-new
  infractl_version: v7.18.36
  blueprint_repo: null
  environment: v60
  external_network: pub-direct
  media_networks:
    meetings:
      availability_zones:
        - az1
      enabled: true
      iso_network:
        name: workload_Isolated_net_vlan687
        ipv6_enabled: false
      prv_kubed_network:
        name: private-lb-vip-prod-vlan96
        ipv6_enabled: false
      pub_kubed_network:
        name: public-lb-vip-prod-vlan86
        ipv6_enabled: false
      pub_direct_networks:
        - name: public-410
          ipv6_enabled: false
      pub_nat_network: public-410
      prv_nat_network: provider-411
      enable_prv_nat: true

defaults:
    infractl_version: v7.18.36
    backend: s3
    terraform_version: v1.5.7
    use_provider_template: true
    embed_provider_template: true
    terraform_templates:
      - path: providers-commercial.tf.tpl
    include_defaults: true
