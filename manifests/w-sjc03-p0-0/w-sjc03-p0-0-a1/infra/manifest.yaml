version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
infra:
  - name: sjc03-vlan687-rr
    env_name: w-sjc03-p0-0
    type: route-reflector
    module_version: v10.13.4
    provisioning_module_version: v5.13.5
    infractl_version: v7.18.36
    blueprint_repo: null
    module_path: modules/openstack/route-reflector
    control_network: provider-411
    create_dns_record: true
    enable_block_storage: true
    flavor: kubed.gv.2vcpu.4mem.0ssd.0eph
    image_name: wbx3-capi-jammy-1.31.5-c8d-amd64-v1.29.6
    iso_network: workload_Isolated_net_vlan687
    iso_network_ips:
      az1:
        - ***********
        - ***********
    availability_zones:
      - az1
    network_mission_tag: meetings
    network_name: w-sjc03-p0-0-a1
    replicas: 2
    route_reflector:
      asn: 65001
      availability_zones:
        - az1
      neighbors:
        aci:
          attributes_unchanged:
            - next-hop
          maximum_prefixes: 1000
          neighbors:
            - increment: 2
              subnet_type: iso_network
            - increment: 3
              subnet_type: iso_network
          prefix_lists:
            - name: kubed_isolated_cidrs
              allow_in: true
              allow_out: true
          remote_as: 65000
        k8s-node:
          listen_range:
            subnet_type: iso_network
          maximum_prefixes: 1000
          prefix_lists:
            - name: kubed_isolated_cidrs
              allow_in: true
              allow_out: true
          remote_as: 65001
          route_reflector_client: true
      prefix_lists:
        f5:
          prefixes:
            - cidr: 100.127.0.0/23
              seq: 5
              type: permit
        kubed_isolated_cidrs:
          prefixes:
            - cidr: 100.80.0.0/12
              le: 27
              seq: 5
              type: permit

defaults:
  infractl_version: v7.18.36
  backend: s3
  terraform_version: v1.5.7
  use_provider_template: true
  embed_provider_template: true
  terraform_templates:
    - path: providers-commercial.tf.tpl
  include_defaults: true
