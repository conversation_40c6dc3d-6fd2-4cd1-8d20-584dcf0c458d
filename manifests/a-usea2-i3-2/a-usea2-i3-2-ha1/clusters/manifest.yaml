version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
  - name: us-ohacuib2
    env_name: a-usea2-i3-2
    mesh:
      enabled: true
    metadata:
      c2p: true
      c2p_release: 1.1.0-rc
      c2p_cloud: pre-prod
      deployment_groups:
        - kas-bootstrap-preprod-1
        - kubed-prod-gen
        - secops-tools-prod
    kcs:
      flavor: preprod-wxt-small-1.0.0
      release: boron-v1.9.1_2
      location: a-usea2-i3-2-ha1-teams
      api_credentials_path: secret/data/mccprod/infra/kcs-cluster-apigw
      account_id: a38389a2-633f-4fba-88a8-c7bbbfbb8e43
      node_pools:
        - metadata:
            name: worker-spot
          spec:
            flavor: c6a.2xlarge
            use_spot_instances: true
            auto_scale:
              max: 6
              min: 3
            node_labels:
              - key: type
                value: worker
              - key: node.kubernetes.io/lifecycle
                value: spot
        - metadata:
            name: istio-ciscoint
          spec:
            flavor: c6a.2xlarge
            use_spot_instances: true
            auto_scale:
              max: 3
              min: 2
            node_labels:
              - key: dedicated
                value: istio-ingress-ciscoint
              - key: node.kubernetes.io/lifecycle
                value: spot
            node_taints:
              - key: dedicated
                value: istio-ingress-ciscoint
                effect: NoSchedule
            security_groups:
              istio-ciscoint:
                rules:
                  - cidrs:
                      - 10.0.0.0/8
                    from: 443
                    name: cisco-https
                    protocol: tcp
                    to: 443
                    type: ingress
                  - cidrs:
                      - vpc
                    from: 15021
                    name: health
                    protocol: tcp
                    to: 15021
                    type: ingress
                tags:
                  - key: kubernetes.io/cluster/us-ohacuib2
                    value: owned
        - metadata:
            name: istio-public
          spec:
            flavor: c6a.2xlarge
            use_spot_instances: true
            auto_scale:
              max: 3
              min: 2
            node_labels:
              - key: dedicated
                value: istio-ingress
              - key: node.kubernetes.io/lifecycle
                value: spot
            node_taints:
              - key: dedicated
                value: istio-ingress
                effect: NoSchedule
            security_groups:
              istio-public:
                rules:
                  - cidrs:
                      - 0.0.0.0/0
                    from: 443
                    name: https
                    protocol: tcp
                    to: 443
                    type: ingress
                  - cidrs:
                      - vpc
                    from: 15021
                    name: health
                    protocol: tcp
                    to: 15021
                    type: ingress
                tags:
                  - key: kubernetes.io/cluster/us-ohacuib2
                    value: owned
    location: IAD
    release: v1.9.1
    module_version: v10.16.19
    backend: s3
    status: online
    enable_credential_lookup: true
    infractl_version: v7.20.1
    s3_bucket_region: us-east-2
    s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
    managed_by: us-txccnc2
    pipeline_bundles:
      - platform/post-provision.yaml
      - platform/aws-cloud-controller.yaml
      - platform/base-apps.yaml
    https_internal_ips:
      - 10.0.0.0/8
      - ***********/16
      - **********/16
      - **********/12
      - **********/14
      - *************/23
      - ************/24
      - ***********/24
      - ***********/21
      - *************/19
      - *************/21
      - *************/24
      - **********/14
      - **********/16
      - **********/19
      - ************/20
      - ***********/20
      - **********/16
      - ***********/20
      - ***********/20
      - ***********/16
      - **********/16
defaults:
  s3_bucket_region: us-east-2
  include_defaults: true
  import_defaults:
    - ../../../../manifest.yaml
