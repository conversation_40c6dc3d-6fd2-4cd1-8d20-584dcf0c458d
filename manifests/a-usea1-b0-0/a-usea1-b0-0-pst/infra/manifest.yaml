version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
### pst0 to app1
  - name: a3p_pst0-a30_app1-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea1-b0-0
    module_version: v10.7.10
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: a3p_pst0
      destination_vpc_name: a30_app1
      source_aws_infra_region: us-east-1
      destination_aws_infra_region: us-east-1
      name: a3p_pst0-a30_app1-pcx
      source_subnet_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*prv-direct*'
      destination_route_table_filter:
      - '*prv-direct*'
### pst0 to wap1
  - name: a3p_pst0-a30_wap1-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea1-b0-0
    module_version: v10.7.10
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: a3p_pst0
      destination_vpc_name: a30_wap1
      source_aws_infra_region: us-east-1
      destination_aws_infra_region: us-east-1
      name: a3p_pst0-a30_wap1-pcx
      source_subnet_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*prv-direct*'
      destination_route_table_filter:
      - '*prv-direct*'
### pst0 to app2
  - name: a3p_pst0-a31_app2-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea1-b0-0
    module_version: v10.7.10
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: a3p_pst0
      destination_vpc_name: a31_app2
      source_aws_infra_region: us-east-1
      destination_aws_infra_region: us-east-1
      name: a3p_pst0-a31_app2-pcx
      destination_subnet_filter:
      - '*prv-direct*'
      source_subnet_filter:
      - '*prv-direct*'
      source_route_table_filter:
      - '*prv-direct*'
      destination_route_table_filter:
      - '*prv-direct*'
### pst0 to wap2
  - name: a3p_pst0-a31_wap2-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea1-b0-0
    module_version: v10.7.10
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: a3p_pst0
      destination_vpc_name: a31_wap2
      source_aws_infra_region: us-east-1
      destination_aws_infra_region: us-east-1
      name: a3p_pst0-a31_wap2-pcx
      source_subnet_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*prv-direct*'
      destination_route_table_filter:
      - '*prv-direct*'
### pst0 to a-mw-prod
  - name: a3p_pst0-a-mw-prod-pcx
    module_path: modules/aws/wxt/peering_connection
    env_name: a-usea1-b0-0
    module_version: v10.7.10
    terraform_version: v1.5.0 # Anything >= v1.0.0 should work
    include_environment: false # including environment and defaults causes a lot of additional variables
    include_defaults: false # to be dropped into the template, which must then be removed.
    dir: infra/pcx
    args:
      source_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      destination_infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      source_vpc_name: a3p_pst0
      destination_vpc_name: a-mw-prod
      source_aws_infra_region: us-east-1
      destination_aws_infra_region: us-east-1
      name: a3p_pst0-a-mw-prod-pcx
      source_subnet_filter:
      - '*workload_iso*'
      source_route_table_filter:
      - '*workload_iso*'
      destination_subnet_filter:
      - '*cluster*'
      destination_route_table_filter:
      - '*cluster*'

defaults:
  infractl_version: v7.18.36
  backend: s3
  terraform_version: v1.5.7
  use_provider_template: true
  embed_provider_template: true

  terraform_templates:
  - path: providers-commercial.tf.tpl

  include_defaults: true
