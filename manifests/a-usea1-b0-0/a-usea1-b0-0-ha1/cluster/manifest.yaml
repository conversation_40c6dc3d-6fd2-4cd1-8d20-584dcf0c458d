version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
clusters:
    - name: us-vaamtgb0
      env_name: a-usea1-b0-0
      cluster_api: true
      s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      admin_port: 6443
      apiserver_record_public: true
      aws_infra_azs:
        - us-east-1a
        - us-east-1b
        - us-east-1c
      aws_infra_region: us-east-1
      base_os: jammy
      bastion_count: 1
      bastion_flavor: t3a.small
      bastion_image_name: WBX-Ubuntu-20-x86_64-harden-nonfips-v1.2-202211
      cloud_service_provider: aws
      datacenter: us-east-1
      dns_credentials_path: secret/data/mccprod/infra/route53/credentials
      domain: a30.prod.infra.webex.com
      gateway_name: not-used
      infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
      kubernetes_version: v1.30.1
      local_dns_enabled: true
      location: IAD
      managed_by: us-txccnc2
      master_count: 3
      master_flavor: c6a.2xlarge
      mesh:
        enabled: true
      metadata:
        annotations:
            helm3Only: true
        cluster_type: webapps-bts
        deployment_groups:
            - aiad0-dc
            - aiad0
            - platform
        platform_release_channel: beta-1
      network_name: a-usea1-b0-0-ha1
      network_names:
        - a-usea1-b0-0-ha1
        - a-usea1-b0-0-ha2
        - a-usea1-b0-0-pst
      node_pools:
        - availability_zones:
            - us-east-1a
          flavor: m5a.2xlarge
          max_size: 4
          metadata:
            node_labels: type=worker
          min_size: 1
          name: platform-worker-1a
          type: worker
        - availability_zones:
            - us-east-1b
          flavor: m5a.2xlarge
          max_size: 4
          metadata:
            node_labels: type=worker
          min_size: 1
          name: platform-worker-1b
          type: worker
        - availability_zones:
            - us-east-1c
          flavor: m5a.2xlarge
          max_size: 4
          metadata:
            node_labels: type=worker
          min_size: 1
          name: platform-worker-1c
          type: worker
        - availability_zones:
            - us-east-1a
          cluster_security_groups:
            - name: istio_public
          extra_policies:
            - wildcard-kms-policy
          flavor: m5a.2xlarge
          max_size: 4
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 1
          name: istio-pool-1a
          type: worker
        - availability_zones:
            - us-east-1b
          cluster_security_groups:
            - name: istio_public
          extra_policies:
            - wildcard-kms-policy
          flavor: m5a.2xlarge
          max_size: 4
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 1
          name: istio-pool-1b
          type: worker
        - availability_zones:
            - us-east-1c
          cluster_security_groups:
            - name: istio_public
          extra_policies:
            - wildcard-kms-policy
          flavor: m5a.2xlarge
          max_size: 4
          metadata:
            node_labels: dedicated=istio-ingress
            node_taints: dedicated=istio-ingress:NoSchedule
          min_size: 1
          name: istio-pool-1c
          type: worker
        - cluster_security_groups:
            - name: istio_private
          flavor: m5a.2xlarge
          max_size: 2
          metadata:
            node_labels: dedicated=istio-ingress-ciscoint
            node_taints: dedicated=istio-ingress-ciscoint:NoSchedule
          min_size: 1
          name: istio-ciscoint
          type: worker
        - availability_zones:
            - us-east-1a
          flavor: r5a.2xlarge
          max_size: 1
          metadata:
            node_labels: type=storage-node
            node_taints: type=storage-node:NoSchedule
          min_size: 0
          name: storage-node-1a
          type: worker
        - availability_zones:
            - us-east-1b
          flavor: r5a.2xlarge
          max_size: 1
          metadata:
            node_labels: type=storage-node
            node_taints: type=storage-node:NoSchedule
          min_size: 0
          name: storage-node-1b
          type: worker
        - availability_zones:
            - us-east-1c
          flavor: r5a.2xlarge
          max_size: 1
          metadata:
            node_labels: type=storage-node
            node_taints: type=storage-node:NoSchedule
          min_size: 0
          name: storage-node-1c
          type: worker
        - flavor: r6i.4xlarge
          max_size: 46
          min_size: 1
          name: mw-p0
          type: worker
          metadata:
            node_labels: type=mw-worker
        - flavor: m6i.4xlarge
          max_size: 10
          metadata:
            node_labels: dedicated=nbr,type=provider-worker
            node_taints: dedicated=nbr:NoSchedule
          min_size: 1
          name: mw-p1
          type: worker
      pipeline_bundles:
        - platform/post-provision.yaml
        - platform/aws-cloud-controller.yaml
        - platform/base-apps.yaml
      release: v1.7.2
      security_groups:
        istio_private:
            rules:
                - cidrs:
                    - 0.0.0.0/0
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - vpc
                  from: 15021
                  name: healthcheck
                  protocol: tcp
                  to: 15021
                  type: ingress
                - cidrs:
                    - 0.0.0.0/0
                  from: 8081
                  name: http
                  protocol: tcp
                  to: 8081
                  type: ingress
            tags:
                - key: kubernetes.io/cluster/us-vaamtgb0
                  value: owned
        istio_public:
            rules:
                - cidrs:
                    - 0.0.0.0/0
                  from: 8443
                  name: https
                  protocol: tcp
                  to: 8443
                  type: ingress
                - cidrs:
                    - vpc
                  from: 15021
                  name: healthcheck
                  protocol: tcp
                  to: 15021
                  type: ingress
                - cidrs:
                    - 0.0.0.0/0
                  from: 8081
                  name: http
                  protocol: tcp
                  to: 8081
                  type: ingress
            tags:
                - key: kubernetes.io/cluster/us-vaamtgb0
                  value: owned
      status: online
      vpc_mission_tag_app: teams
      vpc_routing: true
      worker_flavor: m5a.2xlarge
      zone_type: public
defaults:
    import_defaults:
        - ../../../../manifest.yaml
