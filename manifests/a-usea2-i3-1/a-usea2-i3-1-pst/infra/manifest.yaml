version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

infra:
  - name: ccp-preprod-mq
    env_name: a-usea2-i3-1
    module_path: modules/aws/aws-rabbitmq
    module_version: v10.16.2
    infractl_version: v7.18.47
    args:
      aws_infra_region: us-east-2
      aws_mq_broker_name: ccp-primary-preprod-rabbitmq-prt
      aws_mq_configuration_name: ccp-preprod-rabbitmq-prt-configuration
      aws_mq_admin_credential_path: secret/data/mccprod/infra/ccp-rabbitmq
      ccp_account_id: 32bc6b5d-84a0-4df6-9838-fb9a16d4cffb
      ccp_organization_id: 9edd5e35-1f5a-4fbc-978d-8f250f3501b0
      aws_mq_publicly_accessible: false
      vpc_name: c1p_pst1
      subnet_type: cloud
      aws_mq_security_groups:
        rabbitmq:
          rules:
            - name: amqp
              protocol: tcp
              from: 5671
              to: 5671
              cidrs:
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - 10.0.0.0/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
              ipv6_cidrs: []
              type: ingress
            - name: admin
              protocol: tcp
              from: 443
              to: 443
              cidrs:
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - 10.0.0.0/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
              ipv6_cidrs: []
              type: ingress
  - name: ccp-preprod-pg
    env_name: a-usea2-i3-1
    module_path: modules/aws/aurora_postgresql
    module_version: v10.16.5
    infractl_version: v7.18.19
    infra_credentials_path: secret/data/mccprod/infra/286806641839/atlantis-c2p-pre-prod
    dns_credentials_path: secret/data/mccprod/infra/286806641839/atlantis-c2p-pre-prod
    args:
      aws_infra_region: us-east-2
      aws_secondary_infra_region: us-west-2
      vpc_name: c1p_pst1
      secondary_vpc_name: c3p_pst1
      db_cluster_identifier: ccp-preprod-pg
      master_user_credential_path: "secret/data/mccprod/infra/ccp-preprod-postgres/master_user"
      skip_final_snapshot: false
      subnet_type: cloud
      secondary_subnet_type: cloud
      global_domain: ciscoplatform.cloud
      instance_count: 3
      instance_class: "db.r6g.large"
      enable_secondary_cluster: true
      security_groups:
        postgres:
          rules:
            - name: postgres
              protocol: tcp
              from: 5432
              to: 5432
              cidrs:
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - **********/16
                - 10.0.0.0/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
                - ********/16
              ipv6_cidrs: []
              type: ingress
      tags:
        CcpAccountId: "32bc6b5d-84a0-4df6-9838-fb9a16d4cffb"
        CcpOrganizationId: "9edd5e35-1f5a-4fbc-978d-8f250f3501b0"
  - name: ccp-preprod-eventrule
    env_name: a-usea2-i3-1
    module_path: modules/aws/event_rule
    #  module_version: v10.13.10
    infractl_version: v7.18.47
    module_version: ccp-cmdb-v0.0.7
    args:
      aws_infra_region: us-east-2
      ccp_vault_address: "https://bootstrap.keeper.cisco.com/"
      ccp_vault_name_space: "CiscoCloudPlatform/pre-prod"
      ccp_cmdb_url: "https://api.ciscoplatform.cloud/api/cmdb/v1/"
      ccp_cmdb_role_credential_path: "secret/data/mccprod/infra/ccp-cmdb/role"
      tags:
        CcpOrganizationId: "462d39d5-09b0-47cb-99f1-142f8641babd"
        CcpAccountId: "c55d86ca-61f0-4a7a-b388-e7b1457d7494"
        CcpProductId: "bc036920-f231-4ed9-8f06-9fbfcc855047"
        CcpApplicationId: "ab7c2ee4-57f0-4057-8c84-584e77da70c8"
