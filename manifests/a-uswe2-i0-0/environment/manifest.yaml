version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com

environments:
- name: a-uswe2-i0-0
  domain: a40.prod.infra.webex.com
  s3_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
  infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
  peering_credentials_path: secret/data/mccprod/infra/************/archipelago_service_account
  cloud_service_provider: aws
  backend: s3
  enable_credential_lookup: true
  s3_bucket_region: us-east-2

defaults:
    module_version: v10.9.0
    infractl_version: v7.18.36
    terraform_version: v1.5.3
    use_provider_template: true
    embed_provider_template: true

    terraform_templates:
      - path: providers-commercial.tf.tpl

    include_defaults: true
