version: 1
command_and_control: mccprod
clusters:
- name: wsingen-p-2
  env_name: wsin01-ocp4-prod
  status: online
  worker_count: 0
  ingress_count: 3
  ingress_int_count: 2
  optimized_storage_count: 0
  bastion_count: 1
  master_count: 3
  terraform_version: v1.2.4
  provisioning_module_version: v5.9.3
  module_version: v8.4.0
  infractl_version: v7.17.8
  bastion_flavor: gv.2vcpu.4mem.0ssd.0eph
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
  ingress_int_flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
  base_image: wbx3-focal-1.23.5-containerd-v1-23-5
  base_k8s_image: wbx3-focal-1.23.5-containerd-v1-23-5
  bastion_block_storage: true
  master_block_storage: true
  worker_block_storage: true
  ingress_block_storage: true
  ingress_int_block_storage: true
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  provisioning_extra_args: container_manager='containerd'
  cidr_node_prefix: 26
  cidr_pods_prefix: 18
  cidr_svcs_prefix: 22
  health_checks: true
  metadata:
    cluster_type: generic
    platform_release_channel: stable-2
    annotations:
      helm3Only: true
    deployment_groups:
    - sin01-dc
    - aggmet-infra-sin
  pipeline_bundles: [platform/ecr-deploy.yaml, platform/post-provision.yaml, platform/base-apps.yaml,
    platform/external-dns.yaml, platform/falco.yaml]
  server_group_policies: [soft-anti-affinity]
  internal_network: wsin-mw-prod
  external_network: public-media-1711
  management_network: wbx-meetings
  internal_provider_network: wbx-meetings
  internal_provider_subnets: [************/24]
  worker_eth1_network: wbx-meetings
  volume_storage: true
  gateway_name: wsin01-ocp4-mw
  node_pools:
  - name: mw-p0-
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 23
    max_size: 23
    metadata:
      node_labels: pool-name=mw-p0,type=worker
  - name: optimized-storage
    type: worker
    min_size: 3
    max_size: 3
    flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
    root_block_storage: true
    metadata:
      node_labels: type=optimized-storage-node
  - name: nbr-p0-
    type: provider-worker
    flavor: kubed-nbr.nv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 113
    max_size: 113
    metadata:
      node_labels: pool-name=nbr-p0,type=worker
      node_taints: dedicated=nbrwes:NoSchedule
  - name: worker
    type: provider-worker
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    root_block_storage: true
    min_size: 4
    max_size: 4
    metadata:
      node_labels: pool-name=worker,type=worker
