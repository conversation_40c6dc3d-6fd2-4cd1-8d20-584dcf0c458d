---
version: 1
command_and_control: mccprod
infra_service_domain: https://infra.int.mccprod.prod.infra.webex.com
environments:
- name: &myid-env myid-role-env
  domain: prod.infra.webex.com
  module_version: v10.8.8
  infractl_version: v7.18.25
  terraform_version: v1.5.4
  infra_credentials_path: secret/data/mccprod/infra/mpe-aws-prod/aws
  dns_credentials_path: secret/data/mccprod/infra/route53/credentials
infra:
- name: wxcctl-int-vm-ro
  module_path: modules/aws/myid_aws_access
  env_name: *myid-env
  args:
    aws_role_name: wxc-callcontrol-int-vm-ro
    aws_policy_arns:
      - arn:aws:iam::527856644868:policy/wxcctl-int-vm-ro-policy
    team: wxcctl
    extra_tags: {}

- name: wxcctl-prod-vm-ro
  module_path: modules/aws/myid_aws_access
  env_name: *myid-env
  args:
    aws_role_name: wxc-callcontrol-prod-vm-ro
    aws_policy_arns:
      - arn:aws:iam::527856644868:policy/wxcctl-prod-vm-ro-policy
    team: wxcctl
    extra_tags: {}

defaults:
  import_defaults: ["../../manifest.yaml"]
