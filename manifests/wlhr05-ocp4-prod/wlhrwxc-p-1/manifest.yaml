version: 1
command_and_control: mccprod
clusters:
- name: wlhrwxc-p-1
  status: online
  env_name: wlhr05-ocp4-prod
  pipeline_bundles:
  - platform/post-provision.yaml
  - platform/openstack-cloud-controller.yaml
  - platform/base-apps.yaml
  cidr_pods: auto
  cidr_svcs: auto
  backend: s3
  datacenter: lhr
  health_checks: true
  cidr_node_prefix: 26
  provisioning_extra_args: kubelet_max_pods=56 container_manager='containerd'
  module_version: v7.23.4-custom-routing
  module_commit: 657afea1b2f19a134b7645ebaf9139518f34d835
  master_flavor: kubed.gv.8vcpu.32mem.0ssd.0eph
  ingress_count: 3
  node_pools:
  - name: worker-pool
    type: worker
    min_size: 9
    max_size: 9
    root_block_storage: true
  - name: prom-pool
    type: worker
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed.gv.16vcpu.64mem.0ssd.0eph
    metadata:
      node_labels: type=optimized-storage-node
  - type: external-media
    name: sip-pool-a
    min_size: 16
    max_size: 16
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-400
    public_network_name: public-402
    metadata:
      dns_prefix: sse-a
      node_labels: pool-name=sip-pool-a,type=external-media
    security_groups:
      media: &sip-pool-media-sgs
      - name: sse-sip-internal-udp-1
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: 10.0.0.0/8
      - name: sse-sip-internal-udp-2
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: **********/12
      - name: sse-sip-internal-tcp-1
        type: ingress
        protocol: tcp
        from: 5060
        to: 5060
        cidr: 10.0.0.0/8
      - name: sse-sip-internal-tcp-2
        type: ingress
        protocol: tcp
        from: 5060
        to: 5060
        cidr: **********/12
      ext: &sip-pool-ext-sgs
      - name: sse-sip-internal-tcp-3
        type: ingress
        protocol: tcp
        from: 5061
        to: 5061
        cidr: 0.0.0.0/0
      - name: sse-sip-internal-tcp-4
        type: ingress
        protocol: tcp
        from: 5062
        to: 5062
        cidr: 0.0.0.0/0
      - name: sse-sip-internal-tcp-5
        type: ingress
        protocol: tcp
        from: 8934
        to: 8934
        cidr: 0.0.0.0/0
  - type: external-media
    name: sip-peering-pool
    root_block_storage: true
    min_size: 2
    max_size: 2
    media_network_name: provider-400
    public_network_name: provider-401
    custom_routing: true
    routes:
      eth1:
      - 10.0.0.0/8
      eth2:
      - ***********/20
      - ************/20
      - ************/20
    metadata:
      dns_prefix: sse-p
      node_labels: pool-name=sip-peering-pool,infra.webex.com/dns-prefix=sse-p,type=external-media
    security_groups:
      media: *sip-pool-media-sgs
      ext: *sip-pool-ext-sgs
  - type: external-media
    name: media-pool-a
    min_size: 22
    max_size: 22
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    media_network_name: provider-400
    public_network_name: public-402
    metadata:
      dns_prefix: mse-a
      node_labels: pool-name=media-pool-a,type=external-media
    security_groups:
      media: &media-pool-media-sgs
      - name: mse-grpc-tcp-1
        type: ingress
        protocol: tcp
        from: 9443
        to: 9443
        cidr: 10.0.0.0/8
      - name: mse-grpc-tcp-2
        type: ingress
        protocol: tcp
        from: 9443
        to: 9443
        cidr: **********/12
      - name: mse-media-rtp-udp-media-1
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 10.0.0.0/8
      - name: mse-media-rtp-udp-media-2
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: **********/12
      ext: &media-pool-ext-sgs
      - name: mse-media-rtp-udp
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      - name: mse-media-multiplexed
        type: ingress
        protocol: udp
        from: 5004
        to: 5004
        cidr: 0.0.0.0/0
  - type: external-media
    name: media-peering-pool
    root_block_storage: true
    min_size: 3
    max_size: 3
    custom_routing: true
    media_network_name: provider-400
    public_network_name: provider-401
    routes:
      eth1:
      - 10.0.0.0/8
      eth2:
      - ***********/20
      - ************/20
      - ************/20
    metadata:
      dns_prefix: mse-p
      node_labels: pool-name=media-peering-pool,infra.webex.com/dns-prefix=mse-p,type=external-media
    security_groups:
      media: *media-pool-media-sgs
      ext: *media-pool-ext-sgs
  - type: external-media
    name: sip-pool-g
    min_size: 1
    max_size: 1
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-425
    custom_routing: true
    routes:
      eth1:
        - "10.0.0.0/8"
      eth2:
        - "*************/29"
    metadata:
      dns_prefix: sse-g
      node_labels: pool-name=sip-pool-g,type=external-media,infra.webex.com/dns-prefix=sse-g
    security_groups:
      media: *sip-pool-media-sgs
      ext:
      - name: sse-sip-tango-tcp
        type: ingress
        protocol: tcp
        from: 1024
        to: 65535
        cidr: *************/29
      - name: sse-sip-tango-udp
        type: ingress
        protocol: udp
        from: 1024
        to: 65535
        cidr: *************/29
  - type: external-media
    name: media-pool-g
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed.gv.8vcpu.16mem.0ssd.0eph
    public_network_name: public-425
    custom_routing: true
    routes:
      eth1:
        - "10.0.0.0/8"
      eth2:
        - "*************/29"
    metadata:
      dns_prefix: mse-g
      node_labels: pool-name=media-pool-g,type=external-media,infra.webex.com/dns-prefix=mse-g
    security_groups:
      media: *media-pool-media-sgs
      ext: *media-pool-media-sgs
  - type: external-media
    name: wxc-dhruva-proxy
    min_size: 2
    max_size: 2
    root_block_storage: true
    flavor: kubed-media.nv.32vcpu.64mem.0ssd.0eph
    media_network_name: provider-401
    public_network_name: public-403
    metadata:
      dns_prefix: wxc-dhruva-proxy
      node_labels: pool-name=wxc-dhruva-proxy,infra.webex.com/dns-prefix=wxc-dhruva-proxy,deployment=prod
    security_groups:
      media: &dhruva-proxy-media-sgs
      - name: wxc-dhruva-proxy-udp
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: 0.0.0.0/0
      - name: wxc-dhruva-proxy-tcp
        type: ingress
        protocol: tcp
        from: 5060
        to: 5061
        cidr: 0.0.0.0/0
      ext: &dhruva-proxy-ext-sgs
      - name: wxc-dhruva-proxy-udp
        type: ingress
        protocol: udp
        from: 5060
        to: 5060
        cidr: 0.0.0.0/0
      - name: wxc-dhruva-proxy-tcp
        type: ingress
        protocol: tcp
        from: 5060
        to: 5061
        cidr: 0.0.0.0/0
  - type: external-media
    name: wxc-dhruva-antares
    min_size: 3
    max_size: 3
    flavor: kubed.gv.16vcpu.16mem.0ssd.0eph
    root_block_storage: true
    media_network_name: provider-401
    public_network_name: public-403
    metadata:
      dns_prefix: wxc-dhruva-antares
      node_labels: pool-name=wxc-dhruva-antares,infra.webex.com/dns-prefix=wxc-dhruva-antares,deployment=prod
    security_groups:
      media: &antares-media-sgs
      - name: wxc-dhruva-antares-udp
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      - name: wxc-dhruva-antares-tcp
        type: ingress
        protocol: tcp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      ext: &antares-ext-sgs
      - name: wxc-dhruva-antares-udp
        type: ingress
        protocol: udp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
      - name: wxc-dhruva-antares-tcp
        type: ingress
        protocol: tcp
        from: 19560
        to: 65535
        cidr: 0.0.0.0/0
  metadata:
    cluster_type: wxcedge-prod
    platform_release_channel: stable-1
    annotations:
      helm3Only: true
    deployment_groups:
    - wxc-edge-mse-lhr-prod
    - wxc-edge-sbc-operator-lhr-prod
    - wxc-edge-sse-lhr-prod
    - wxc-dhruva-ocp-prod-lhr05
    - dhruva-operator-ocp-prod-lhr05
    - mobius-prod-lhr
    - wxcctl-prd-uk-lhr05
defaults:
  import_defaults: [../../../manifest.yaml]
